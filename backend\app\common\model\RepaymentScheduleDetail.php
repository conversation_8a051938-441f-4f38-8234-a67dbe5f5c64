<?php
declare (strict_types = 1);

namespace app\common\model;

use think\Model;

/**
 * 还款计划详细模型
 */
class RepaymentScheduleDetail extends Model
{
    protected $name = 'repayment_schedule_details';
    
    // 设置字段信息
    protected $schema = [
        'id'                        => 'int',
        'disbursement_record_id'    => 'int',
        'period_number'             => 'int',
        'due_date'                  => 'date',
        'due_amount'                => 'float',
        'paid_amount'               => 'float',
        'remaining_amount'          => 'float',
        'status'                    => 'string',
        'overdue_days'              => 'int',
        'created_at'                => 'datetime',
        'updated_at'                => 'datetime',
    ];

    // 只读字段
    protected $readonly = ['period_number', 'created_at'];

    /**
     * 关联放款记录
     */
    public function disbursementRecord()
    {
        return $this->belongsTo(LoanDisbursementRecord::class, 'disbursement_record_id');
    }

    /**
     * 状态中文名
     */
    public function getStatusTextAttr(): string
    {
        $statuses = [
            'pending' => '待还款',
            'paid' => '已还清',
            'overdue' => '已逾期',
            'partial' => '部分还款'
        ];
        
        return $statuses[$this->status] ?? '未知';
    }

    /**
     * 获取逾期天数
     */
    public function getOverdueDaysAttr(): int
    {
        if ($this->status === 'paid') {
            return 0;
        }
        
        $today = strtotime(date('Y-m-d'));
        $dueDate = strtotime($this->due_date);
        
        if ($today > $dueDate) {
            return ceil(($today - $dueDate) / 86400);
        }
        
        return 0;
    }

    /**
     * 获取剩余天数
     */
    public function getRemainingDaysAttr(): int
    {
        if ($this->status === 'paid') {
            return 0;
        }
        
        $today = strtotime(date('Y-m-d'));
        $dueDate = strtotime($this->due_date);
        
        if ($dueDate > $today) {
            return ceil(($dueDate - $today) / 86400);
        }
        
        return 0;
    }

    /**
     * 完成率
     */
    public function getCompletionRateAttr(): float
    {
        if ($this->due_amount <= 0) {
            return 0;
        }
        
        return round(($this->paid_amount / $this->due_amount) * 100, 2);
    }

    /**
     * 是否逾期
     */
    public function isOverdue(): bool
    {
        return $this->status !== 'paid' && strtotime($this->due_date) < strtotime(date('Y-m-d'));
    }

    /**
     * 标记为逾期
     */
    public function markAsOverdue(): bool
    {
        if ($this->isOverdue() && $this->status !== 'overdue') {
            return $this->save([
                'status' => 'overdue',
                'overdue_days' => $this->overdue_days_attr
            ]);
        }
        
        return true;
    }

    /**
     * 部分还款
     */
    public function partialPayment(float $amount): bool
    {
        if ($amount <= 0 || $amount > $this->remaining_amount) {
            return false;
        }
        
        $newPaidAmount = $this->paid_amount + $amount;
        $newRemainingAmount = $this->due_amount - $newPaidAmount;
        
        $status = $newRemainingAmount <= 0 ? 'paid' : 'partial';
        
        return $this->save([
            'paid_amount' => $newPaidAmount,
            'remaining_amount' => max(0, $newRemainingAmount),
            'status' => $status
        ]);
    }

    /**
     * 完全还清
     */
    public function payOff(): bool
    {
        return $this->save([
            'paid_amount' => $this->due_amount,
            'remaining_amount' => 0,
            'status' => 'paid'
        ]);
    }
}
