<?php
declare(strict_types=1);

namespace app\common\service;

use think\facade\Db;
use think\facade\Log;
use think\facade\Cache;
use think\facade\Request;

/**
 * 安全审计服务
 * 记录和分析系统安全事件
 */
class SecurityAuditService
{
    /**
     * 审计事件类型
     */
    const EVENT_LOGIN = 'login';
    const EVENT_LOGOUT = 'logout';
    const EVENT_LOGIN_FAILED = 'login_failed';
    const EVENT_PASSWORD_CHANGE = 'password_change';
    const EVENT_DATA_ACCESS = 'data_access';
    const EVENT_DATA_MODIFY = 'data_modify';
    const EVENT_DATA_DELETE = 'data_delete';
    const EVENT_PERMISSION_DENIED = 'permission_denied';
    const EVENT_SECURITY_VIOLATION = 'security_violation';
    const EVENT_SYSTEM_ERROR = 'system_error';

    /**
     * 风险等级
     */
    const RISK_LOW = 'low';
    const RISK_MEDIUM = 'medium';
    const RISK_HIGH = 'high';
    const RISK_CRITICAL = 'critical';

    /**
     * 记录审计事件
     * @param string $event 事件类型
     * @param array $data 事件数据
     * @param string $riskLevel 风险等级
     * @param int|null $userId 用户ID
     * @return bool
     */
    public static function logEvent(string $event, array $data = [], string $riskLevel = self::RISK_LOW, ?int $userId = null): bool
    {
        try {
            $auditData = [
                'event_type' => $event,
                'user_id' => $userId,
                'ip_address' => self::getClientIp(),
                'user_agent' => Request::header('user-agent', ''),
                'url' => Request::url(),
                'method' => Request::method(),
                'risk_level' => $riskLevel,
                'event_data' => json_encode($data, JSON_UNESCAPED_UNICODE),
                'created_at' => date('Y-m-d H:i:s')
            ];

            // 记录到数据库
            Db::name('security_audit_logs')->insert($auditData);

            // 记录到日志文件
            Log::info("Security Audit: {$event}", $auditData);

            // 高风险事件实时告警
            if (in_array($riskLevel, [self::RISK_HIGH, self::RISK_CRITICAL])) {
                self::sendAlert($event, $auditData);
            }

            return true;
        } catch (\Exception $e) {
            Log::error('Security audit logging failed: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 记录登录事件
     * @param int $userId 用户ID
     * @param bool $success 是否成功
     * @param array $extra 额外信息
     */
    public static function logLogin(int $userId, bool $success = true, array $extra = []): void
    {
        $event = $success ? self::EVENT_LOGIN : self::EVENT_LOGIN_FAILED;
        $riskLevel = $success ? self::RISK_LOW : self::RISK_MEDIUM;

        $data = array_merge([
            'user_id' => $userId,
            'success' => $success,
            'timestamp' => time()
        ], $extra);

        self::logEvent($event, $data, $riskLevel, $userId);

        // 检查异常登录行为
        if ($success) {
            self::checkAbnormalLogin($userId);
        } else {
            self::checkBruteForceAttack($userId);
        }
    }

    /**
     * 记录数据访问事件
     * @param string $resource 资源类型
     * @param int $resourceId 资源ID
     * @param string $action 操作类型
     * @param int|null $userId 用户ID
     * @param array $extra 额外信息
     */
    public static function logDataAccess(string $resource, int $resourceId, string $action, ?int $userId = null, array $extra = []): void
    {
        $eventMap = [
            'read' => self::EVENT_DATA_ACCESS,
            'create' => self::EVENT_DATA_MODIFY,
            'update' => self::EVENT_DATA_MODIFY,
            'delete' => self::EVENT_DATA_DELETE
        ];

        $event = $eventMap[$action] ?? self::EVENT_DATA_ACCESS;
        $riskLevel = $action === 'delete' ? self::RISK_MEDIUM : self::RISK_LOW;

        $data = array_merge([
            'resource' => $resource,
            'resource_id' => $resourceId,
            'action' => $action,
            'timestamp' => time()
        ], $extra);

        self::logEvent($event, $data, $riskLevel, $userId);

        // 检查敏感数据访问
        if (in_array($resource, ['customers', 'loan_applications', 'financial_records'])) {
            self::checkSensitiveDataAccess($resource, $resourceId, $userId);
        }
    }

    /**
     * 记录安全违规事件
     * @param string $violation 违规类型
     * @param array $details 详细信息
     * @param string $riskLevel 风险等级
     * @param int|null $userId 用户ID
     */
    public static function logSecurityViolation(string $violation, array $details = [], string $riskLevel = self::RISK_HIGH, ?int $userId = null): void
    {
        $data = array_merge([
            'violation_type' => $violation,
            'timestamp' => time()
        ], $details);

        self::logEvent(self::EVENT_SECURITY_VIOLATION, $data, $riskLevel, $userId);

        // 自动响应安全违规
        self::handleSecurityViolation($violation, $details, $userId);
    }

    /**
     * 获取审计日志
     * @param array $filters 过滤条件
     * @param int $page 页码
     * @param int $limit 每页数量
     * @return array
     */
    public static function getAuditLogs(array $filters = [], int $page = 1, int $limit = 20): array
    {
        $query = Db::name('security_audit_logs');

        // 应用过滤条件
        if (!empty($filters['event_type'])) {
            $query->where('event_type', $filters['event_type']);
        }

        if (!empty($filters['user_id'])) {
            $query->where('user_id', $filters['user_id']);
        }

        if (!empty($filters['risk_level'])) {
            $query->where('risk_level', $filters['risk_level']);
        }

        if (!empty($filters['ip_address'])) {
            $query->where('ip_address', $filters['ip_address']);
        }

        if (!empty($filters['start_time'])) {
            $query->where('created_at', '>=', $filters['start_time']);
        }

        if (!empty($filters['end_time'])) {
            $query->where('created_at', '<=', $filters['end_time']);
        }

        // 分页查询
        $total = $query->count();
        $logs = $query->order('created_at DESC')
            ->limit(($page - 1) * $limit, $limit)
            ->select()
            ->toArray();

        // 解析事件数据
        foreach ($logs as &$log) {
            $log['event_data'] = json_decode($log['event_data'], true);
        }

        return [
            'total' => $total,
            'page' => $page,
            'limit' => $limit,
            'data' => $logs
        ];
    }

    /**
     * 生成安全报告
     * @param string $startDate 开始日期
     * @param string $endDate 结束日期
     * @return array
     */
    public static function generateSecurityReport(string $startDate, string $endDate): array
    {
        $report = [
            'period' => ['start' => $startDate, 'end' => $endDate],
            'summary' => [],
            'events' => [],
            'risks' => [],
            'recommendations' => []
        ];

        // 事件统计
        $eventStats = Db::name('security_audit_logs')
            ->where('created_at', 'between', [$startDate, $endDate])
            ->field('event_type, risk_level, COUNT(*) as count')
            ->group('event_type, risk_level')
            ->select()
            ->toArray();

        $report['events'] = $eventStats;

        // 风险分析
        $riskStats = Db::name('security_audit_logs')
            ->where('created_at', 'between', [$startDate, $endDate])
            ->field('risk_level, COUNT(*) as count')
            ->group('risk_level')
            ->select()
            ->toArray();

        $report['risks'] = $riskStats;

        // 异常IP统计
        $ipStats = Db::name('security_audit_logs')
            ->where('created_at', 'between', [$startDate, $endDate])
            ->where('risk_level', 'in', [self::RISK_HIGH, self::RISK_CRITICAL])
            ->field('ip_address, COUNT(*) as count')
            ->group('ip_address')
            ->order('count DESC')
            ->limit(10)
            ->select()
            ->toArray();

        $report['suspicious_ips'] = $ipStats;

        // 生成建议
        $report['recommendations'] = self::generateRecommendations($report);

        return $report;
    }

    /**
     * 检查异常登录行为
     * @param int $userId 用户ID
     */
    private static function checkAbnormalLogin(int $userId): void
    {
        $clientIp = self::getClientIp();
        $cacheKey = "login_locations:{$userId}";
        
        // 获取用户历史登录位置
        $locations = Cache::get($cacheKey, []);
        
        // 简单的异地登录检测（基于IP段）
        $ipPrefix = substr($clientIp, 0, strrpos($clientIp, '.'));
        
        if (!in_array($ipPrefix, $locations)) {
            // 记录异地登录事件
            self::logEvent(self::EVENT_SECURITY_VIOLATION, [
                'violation_type' => 'abnormal_location_login',
                'user_id' => $userId,
                'ip_address' => $clientIp,
                'ip_prefix' => $ipPrefix
            ], self::RISK_MEDIUM, $userId);
            
            // 更新位置缓存
            $locations[] = $ipPrefix;
            Cache::set($cacheKey, array_slice($locations, -5), 86400 * 30); // 保留最近5个位置，30天
        }
    }

    /**
     * 检查暴力破解攻击
     * @param int $userId 用户ID
     */
    private static function checkBruteForceAttack(int $userId): void
    {
        $clientIp = self::getClientIp();
        $cacheKey = "failed_login:{$clientIp}:{$userId}";
        
        $failedCount = Cache::get($cacheKey, 0) + 1;
        Cache::set($cacheKey, $failedCount, 3600); // 1小时
        
        if ($failedCount >= 5) {
            self::logEvent(self::EVENT_SECURITY_VIOLATION, [
                'violation_type' => 'brute_force_attack',
                'user_id' => $userId,
                'ip_address' => $clientIp,
                'failed_count' => $failedCount
            ], self::RISK_HIGH, $userId);
            
            // 可以在这里实施IP封禁等措施
        }
    }

    /**
     * 检查敏感数据访问
     * @param string $resource 资源类型
     * @param int $resourceId 资源ID
     * @param int|null $userId 用户ID
     */
    private static function checkSensitiveDataAccess(string $resource, int $resourceId, ?int $userId): void
    {
        $cacheKey = "sensitive_access:{$userId}:{$resource}";
        $accessCount = Cache::get($cacheKey, 0) + 1;
        Cache::set($cacheKey, $accessCount, 3600); // 1小时
        
        // 如果1小时内访问敏感数据超过100次，标记为异常
        if ($accessCount > 100) {
            self::logEvent(self::EVENT_SECURITY_VIOLATION, [
                'violation_type' => 'excessive_sensitive_data_access',
                'resource' => $resource,
                'access_count' => $accessCount,
                'user_id' => $userId
            ], self::RISK_HIGH, $userId);
        }
    }

    /**
     * 处理安全违规
     * @param string $violation 违规类型
     * @param array $details 详细信息
     * @param int|null $userId 用户ID
     */
    private static function handleSecurityViolation(string $violation, array $details, ?int $userId): void
    {
        switch ($violation) {
            case 'brute_force_attack':
                // 可以实施IP临时封禁
                break;
                
            case 'sql_injection_attempt':
            case 'xss_attempt':
                // 可以记录到黑名单
                break;
                
            case 'excessive_sensitive_data_access':
                // 可以暂时限制用户权限
                break;
        }
    }

    /**
     * 发送安全告警
     * @param string $event 事件类型
     * @param array $data 事件数据
     */
    private static function sendAlert(string $event, array $data): void
    {
        // 这里可以集成邮件、短信、钉钉等告警方式
        Log::warning("Security Alert: {$event}", $data);
        
        // 示例：发送到监控系统
        // $this->sendToMonitoringSystem($event, $data);
    }

    /**
     * 生成安全建议
     * @param array $report 报告数据
     * @return array
     */
    private static function generateRecommendations(array $report): array
    {
        $recommendations = [];
        
        // 基于风险统计生成建议
        foreach ($report['risks'] as $risk) {
            if ($risk['risk_level'] === self::RISK_HIGH && $risk['count'] > 10) {
                $recommendations[] = '检测到大量高风险事件，建议加强安全监控';
            }
        }
        
        // 基于可疑IP生成建议
        if (count($report['suspicious_ips']) > 5) {
            $recommendations[] = '检测到多个可疑IP地址，建议启用IP白名单';
        }
        
        return $recommendations;
    }

    /**
     * 获取客户端IP
     * @return string
     */
    private static function getClientIp(): string
    {
        return Request::ip();
    }
}
