<?php
declare (strict_types = 1);

namespace app\common\model;

use think\Model;

/**
 * 还款记录模型
 */
class RepaymentRecord extends Model
{
    protected $name = 'repayment_records';
    
    // 设置字段信息
    protected $schema = [
        'id'                        => 'int',
        'business_flow_no'          => 'string',
        'disbursement_record_id'    => 'int',
        'customer_id'               => 'int',
        'customer_name'             => 'string',
        'repayment_date'            => 'datetime',
        'repayment_method'          => 'string',
        'repayment_amount'          => 'float',
        'repayment_type'            => 'string',
        'payment_screenshot'        => 'string',
        'remark'                    => 'string',
        'overdue_days'              => 'int',
        'overdue_fee'               => 'float',
        'status'                    => 'string',
        'created_by'                => 'int',
        'confirmed_by'              => 'int',
        'confirmed_at'              => 'datetime',
        'created_at'                => 'datetime',
        'updated_at'                => 'datetime',
    ];

    // 只读字段
    protected $readonly = ['business_flow_no', 'created_at'];

    /**
     * 关联放款记录
     */
    public function disbursementRecord()
    {
        return $this->belongsTo(LoanDisbursementRecord::class, 'disbursement_record_id');
    }

    /**
     * 关联客户
     */
    public function customer()
    {
        return $this->belongsTo(Customer::class, 'customer_id');
    }

    /**
     * 关联创建人
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * 关联确认人
     */
    public function confirmer()
    {
        return $this->belongsTo(User::class, 'confirmed_by');
    }

    /**
     * 还款方式中文名
     */
    public function getRepaymentMethodTextAttr(): string
    {
        $methods = [
            'wechat' => '微信',
            'alipay' => '支付宝',
            'bank_card' => '银行卡',
            'cash' => '现金',
            'other' => '其他'
        ];
        
        return $methods[$this->repayment_method] ?? '未知';
    }

    /**
     * 还款形式中文名
     */
    public function getRepaymentTypeTextAttr(): string
    {
        $types = [
            'normal' => '正常还款',
            'partial' => '部分还款',
            'negotiated' => '协商还款',
            'overdue_fee' => '逾期费',
            'early_settlement' => '提前结清'
        ];
        
        return $types[$this->repayment_type] ?? '未知';
    }

    /**
     * 状态中文名
     */
    public function getStatusTextAttr(): string
    {
        $statuses = [
            'pending' => '待确认',
            'confirmed' => '已确认',
            'cancelled' => '已取消'
        ];
        
        return $statuses[$this->status] ?? '未知';
    }

    /**
     * 获取截图完整URL
     */
    public function getScreenshotUrlAttr(): string
    {
        if (empty($this->payment_screenshot)) {
            return '';
        }
        
        // 如果已经是完整URL，直接返回
        if (strpos($this->payment_screenshot, 'http') === 0) {
            return $this->payment_screenshot;
        }
        
        // 否则拼接域名
        return request()->domain() . $this->payment_screenshot;
    }

    /**
     * 确认还款记录
     */
    public function confirm(int $confirmerId): bool
    {
        return $this->save([
            'status' => 'confirmed',
            'confirmed_by' => $confirmerId,
            'confirmed_at' => date('Y-m-d H:i:s')
        ]);
    }

    /**
     * 取消还款记录
     */
    public function cancel(): bool
    {
        return $this->save([
            'status' => 'cancelled'
        ]);
    }
}
