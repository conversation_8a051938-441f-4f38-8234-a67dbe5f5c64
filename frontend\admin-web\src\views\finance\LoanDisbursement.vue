<template>
  <div class="loan-disbursement">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>放款登记</span>
          <el-button type="primary" @click="showCreateDialog">新增放款</el-button>
        </div>
      </template>
      
      <!-- 搜索区域 -->
      <div class="search-area">
        <el-form :inline="true" :model="searchForm">
          <el-form-item label="状态">
            <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
              <el-option label="活跃" value="active"/>
              <el-option label="已完成" value="completed"/>
              <el-option label="逾期" value="overdue"/>
              <el-option label="已结清" value="settled"/>
            </el-select>
          </el-form-item>
          
          <el-form-item label="日期范围">
            <el-date-picker
              v-model="dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
          
          <el-form-item label="关键词">
            <el-input
              v-model="searchForm.keyword"
              placeholder="流水号或客户姓名"
              clearable
            />
          </el-form-item>
          
          <el-form-item>
            <el-button type="primary" @click="handleSearch">搜索</el-button>
            <el-button @click="resetSearch">重置</el-button>
            <el-button type="success" @click="handleExport" :loading="exportLoading">
              <el-icon><Download /></el-icon>
              导出数据
            </el-button>
          </el-form-item>
        </el-form>
      </div>
      
      <!-- 表格 -->
      <el-table
        v-loading="tableLoading"
        :data="tableData"
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55"/>
        
        <el-table-column prop="business_flow_no" label="业务流水号" width="200"/>
        
        <el-table-column prop="loan_date" label="放款日期" width="120"/>
        
        <el-table-column prop="customer_name" label="客户姓名" width="120"/>
        
        <el-table-column prop="loan_amount" label="放款金额" width="120">
          <template #default="scope">
            ¥{{ formatMoney(scope.row.loan_amount) }}
          </template>
        </el-table-column>
        
        <el-table-column prop="repayment_type" label="还款方式" width="100">
          <template #default="scope">
            {{ getRepaymentTypeText(scope.row.repayment_type) }}
          </template>
        </el-table-column>
        
        <el-table-column prop="repayment_cycle" label="还款周期" width="100">
          <template #default="scope">
            {{ scope.row.repayment_cycle }}期
          </template>
        </el-table-column>
        
        <el-table-column prop="total_repaid_amount" label="已回款" width="120">
          <template #default="scope">
            ¥{{ formatMoney(scope.row.total_repaid_amount) }}
          </template>
        </el-table-column>
        
        <el-table-column prop="remaining_amount" label="未回款" width="120">
          <template #default="scope">
            ¥{{ formatMoney(scope.row.remaining_amount) }}
          </template>
        </el-table-column>
        
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="getStatusTagType(scope.row.status)">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <el-button
              type="primary"
              size="small"
              @click="viewDetail(scope.row)"
            >
              详情
            </el-button>
            <el-button
              type="success"
              size="small"
              @click="addRepayment(scope.row)"
            >
              还款
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <el-pagination
        v-model:current-page="pagination.page"
        v-model:page-size="pagination.limit"
        :page-sizes="[10, 20, 50, 100]"
        :total="pagination.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-card>
    
    <!-- 新增放款对话框 -->
    <el-dialog
      v-model="createDialogVisible"
      title="新增放款登记"
      width="800px"
      @close="resetCreateForm"
    >
      <el-form
        ref="createFormRef"
        :model="createForm"
        :rules="createRules"
        label-width="120px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="放款日期" prop="loan_date">
              <el-date-picker
                v-model="createForm.loan_date"
                type="date"
                placeholder="选择放款日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          
          <el-col :span="12">
            <el-form-item label="客户" prop="customer_id">
              <el-select
                v-model="createForm.customer_id"
                placeholder="请选择客户"
                filterable
                remote
                :remote-method="searchCustomers"
                :loading="customerLoading"
                style="width: 100%"
                @change="handleCustomerChange"
              >
                <el-option
                  v-for="customer in customerOptions"
                  :key="customer.id"
                  :label="`${customer.name} (${customer.phone})`"
                  :value="customer.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="放款金额" prop="loan_amount">
              <el-input-number
                v-model="createForm.loan_amount"
                :min="0"
                :precision="2"
                placeholder="请输入放款金额"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          
          <el-col :span="12">
            <el-form-item label="平台费" prop="platform_fee">
              <el-input-number
                v-model="createForm.platform_fee"
                :min="0"
                :precision="2"
                placeholder="请输入平台费"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="还款方式" prop="repayment_type">
              <el-select v-model="createForm.repayment_type" placeholder="请选择还款方式" style="width: 100%">
                <el-option label="每天还款" value="daily"/>
                <el-option label="每周还款" value="weekly"/>
                <el-option label="每月还款" value="monthly"/>
              </el-select>
            </el-form-item>
          </el-col>
          
          <el-col :span="12">
            <el-form-item label="还款周期" prop="repayment_cycle">
              <el-input-number
                v-model="createForm.repayment_cycle"
                :min="1"
                placeholder="请输入还款周期"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="中介姓名">
              <el-input v-model="createForm.agent_name" placeholder="请输入中介姓名"/>
            </el-form-item>
          </el-col>
          
          <el-col :span="12">
            <el-form-item label="中介返点率(%)">
              <el-input-number
                v-model="createForm.agent_commission_rate"
                :min="0"
                :max="100"
                :precision="2"
                placeholder="请输入返点率"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item label="返点类型">
          <el-radio-group v-model="createForm.commission_type">
            <el-radio label="front">前返</el-radio>
            <el-radio label="back">后返</el-radio>
            <el-radio label="both">前后都返</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="createDialogVisible = false">取消</el-button>
        <el-button type="primary" :loading="createLoading" @click="handleCreate">确定</el-button>
      </template>
    </el-dialog>

    <!-- 放款详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="放款详情"
      width="900px"
    >
      <div v-if="detailData">
        <el-descriptions title="基本信息" :column="3" size="small" border>
          <el-descriptions-item label="流水号">{{ detailData.business_flow_no }}</el-descriptions-item>
          <el-descriptions-item label="客户">{{ detailData.customer_name }}</el-descriptions-item>
          <el-descriptions-item label="放款日期">{{ detailData.loan_date }}</el-descriptions-item>
          <el-descriptions-item label="放款金额">¥{{ formatMoney(detailData.loan_amount) }}</el-descriptions-item>
          <el-descriptions-item label="已回款">¥{{ formatMoney(detailData.total_repaid_amount) }}</el-descriptions-item>
          <el-descriptions-item label="未回款">¥{{ formatMoney(detailData.remaining_amount) }}</el-descriptions-item>
        </el-descriptions>

        <el-tabs style="margin-top: 16px;">
          <el-tab-pane label="还款计划">
            <el-table :data="detailData.repayment_schedule || []" size="small">
              <el-table-column prop="period_number" label="期数" width="80"/>
              <el-table-column prop="due_date" label="应还日期" width="120"/>
              <el-table-column prop="due_amount" label="应还金额" width="120">
                <template #default="scope">¥{{ formatMoney(scope.row.due_amount) }}</template>
              </el-table-column>
              <el-table-column prop="paid_amount" label="已还金额" width="120">
                <template #default="scope">¥{{ formatMoney(scope.row.paid_amount) }}</template>
              </el-table-column>
              <el-table-column prop="remaining_amount" label="剩余金额" width="120">
                <template #default="scope">¥{{ formatMoney(scope.row.remaining_amount) }}</template>
              </el-table-column>
              <el-table-column prop="status" label="状态"/>
            </el-table>
          </el-tab-pane>
          <el-tab-pane label="还款记录">
            <el-table :data="detailData.repayment_records || []" size="small">
              <el-table-column prop="business_flow_no" label="流水号" width="180"/>
              <el-table-column prop="repayment_date" label="时间" width="180"/>
              <el-table-column prop="repayment_amount" label="金额" width="120">
                <template #default="scope">¥{{ formatMoney(scope.row.repayment_amount) }}</template>
              </el-table-column>
              <el-table-column prop="repayment_method" label="方式" width="100"/>
              <el-table-column prop="repayment_type" label="类型" width="120"/>
              <el-table-column prop="status" label="状态" width="100"/>
            </el-table>
          </el-tab-pane>
        </el-tabs>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Download } from '@element-plus/icons-vue'
import { financeAPI, customerAPI } from '../../api'

export default {
  name: 'LoanDisbursement',
  components: {
    Download
  },
  setup() {
    const router = useRouter()
    const tableLoading = ref(false)
    const createLoading = ref(false)
    const customerLoading = ref(false)
    const exportLoading = ref(false)
    const createDialogVisible = ref(false)
    const detailDialogVisible = ref(false)
    const createFormRef = ref()
    
    const tableData = ref([])
    const detailData = ref(null)
    const customerOptions = ref([])
    const selectedRows = ref([])
    
    const pagination = reactive({
      page: 1,
      limit: 20,
      total: 0
    })
    
    const searchForm = reactive({
      status: '',
      keyword: ''
    })
    
    const dateRange = ref([])
    
    const createForm = reactive({
      loan_date: '',
      customer_id: '',
      customer_name: '',
      loan_amount: 0,
      platform_fee: 0,
      repayment_type: '',
      repayment_cycle: 1,
      agent_name: '',
      agent_commission_rate: 0,
      commission_type: 'front'
    })
    
    const createRules = {
      loan_date: [{ required: true, message: '请选择放款日期', trigger: 'change' }],
      customer_id: [{ required: true, message: '请选择客户', trigger: 'change' }],
      loan_amount: [{ required: true, message: '请输入放款金额', trigger: 'blur' }],
      repayment_type: [{ required: true, message: '请选择还款方式', trigger: 'change' }],
      repayment_cycle: [{ required: true, message: '请输入还款周期', trigger: 'blur' }]
    }
    
    // 计算属性
    const searchParams = computed(() => {
      const params = {
        page: pagination.page,
        limit: pagination.limit,
        ...searchForm
      }
      
      if (dateRange.value && dateRange.value.length === 2) {
        params.date_from = dateRange.value[0]
        params.date_to = dateRange.value[1]
      }
      
      return params
    })
    
    // 获取放款列表
    const getTableData = async () => {
      try {
        tableLoading.value = true
        const data = await financeAPI.disbursement.getList(searchParams.value)
        
        tableData.value = data.items
        pagination.total = data.pagination.total
        
      } catch (error) {
        console.error('获取放款列表失败:', error)
      } finally {
        tableLoading.value = false
      }
    }
    
    // 搜索客户
    const searchCustomers = async (query) => {
      if (!query) return
      
      try {
        customerLoading.value = true
        const data = await customerAPI.searchCustomers({ keyword: query, limit: 10 })
        customerOptions.value = data
      } catch (error) {
        console.error('搜索客户失败:', error)
      } finally {
        customerLoading.value = false
      }
    }
    
    // 处理客户选择
    const handleCustomerChange = (customerId) => {
      const customer = customerOptions.value.find(c => c.id === customerId)
      if (customer) {
        createForm.customer_name = customer.name
      }
    }
    
    // 显示创建对话框
    const showCreateDialog = () => {
      createDialogVisible.value = true
      createForm.loan_date = new Date().toISOString().split('T')[0]
    }
    
    // 创建放款
    const handleCreate = async () => {
      try {
        await createFormRef.value.validate()
        createLoading.value = true
        
        await financeAPI.disbursement.create(createForm)
        
        ElMessage.success('放款登记成功')
        createDialogVisible.value = false
        getTableData()
        
      } catch (error) {
        console.error('创建放款失败:', error)
      } finally {
        createLoading.value = false
      }
    }
    
    // 重置创建表单
    const resetCreateForm = () => {
      Object.assign(createForm, {
        loan_date: '',
        customer_id: '',
        customer_name: '',
        loan_amount: 0,
        platform_fee: 0,
        repayment_type: '',
        repayment_cycle: 1,
        agent_name: '',
        agent_commission_rate: 0,
        commission_type: 'front'
      })
      customerOptions.value = []
    }
    
    // 搜索
    const handleSearch = () => {
      pagination.page = 1
      getTableData()
    }
    
    // 重置搜索
    const resetSearch = () => {
      Object.assign(searchForm, {
        status: '',
        keyword: ''
      })
      dateRange.value = []
      pagination.page = 1
      getTableData()
    }
    
    // 分页处理
    const handleSizeChange = (size) => {
      pagination.limit = size
      pagination.page = 1
      getTableData()
    }
    
    const handleCurrentChange = (page) => {
      pagination.page = page
      getTableData()
    }
    
    // 选择处理
    const handleSelectionChange = (selection) => {
      selectedRows.value = selection
    }
    
    // 查看详情
    const viewDetail = async (row) => {
      try {
        const data = await financeAPI.disbursement.getDetail(row.id)
        // 兼容后端关系命名（小写下划线）
        detailData.value = {
          ...data,
          repayment_schedule: data.repaymentSchedule || data.repayment_schedule || [],
          repayment_records: data.repaymentRecords || data.repayment_records || []
        }
        detailDialogVisible.value = true
      } catch (e) {
        ElMessage.error('获取详情失败')
      }
    }
    
    // 添加还款：跳转到还款页面并预填放款记录
    const addRepayment = (row) => {
      router.push({ path: '/finance/repayment', query: { disbursement_record_id: row.id } })
    }
    
    // 工具函数
    const formatMoney = (amount) => {
      return (amount || 0).toLocaleString()
    }
    
    const getRepaymentTypeText = (type) => {
      const types = {
        daily: '每天',
        weekly: '每周',
        monthly: '每月'
      }
      return types[type] || type
    }
    
    const getStatusText = (status) => {
      const statuses = {
        active: '活跃',
        completed: '已完成',
        overdue: '逾期',
        settled: '已结清'
      }
      return statuses[status] || status
    }
    
    const getStatusTagType = (status) => {
      const types = {
        active: 'primary',
        completed: 'success',
        overdue: 'danger',
        settled: 'info'
      }
      return types[status] || ''
    }
    
    // 导出数据
    const handleExport = async () => {
      try {
        exportLoading.value = true
        
        // 构建查询参数
        const params = {
          ...searchForm
        }
        
        // 添加日期范围
        if (dateRange.value && dateRange.value.length === 2) {
          params.start_date = dateRange.value[0]
          params.end_date = dateRange.value[1]
        }
        
        // 调用导出API
        const response = await fetch('/api/export/disbursements?' + new URLSearchParams(params), {
          method: 'GET',
          headers: {
            'Authorization': 'Bearer ' + localStorage.getItem('token')
          }
        })
        
        if (!response.ok) {
          throw new Error('导出失败')
        }
        
        // 创建下载链接
        const blob = await response.blob()
        const url = window.URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `放款记录_${new Date().toISOString().split('T')[0]}.csv`
        document.body.appendChild(a)
        a.click()
        window.URL.revokeObjectURL(url)
        document.body.removeChild(a)
        
        ElMessage.success('导出成功')
      } catch (error) {
        console.error('Export error:', error)
        ElMessage.error('导出失败')
      } finally {
        exportLoading.value = false
      }
    }
    
    onMounted(() => {
      getTableData()
    })
    
    return {
      // 响应式数据
      tableLoading,
      createLoading,
      customerLoading,
      exportLoading,
      createDialogVisible,
      detailDialogVisible,
      createFormRef,
      tableData,
      detailData,
      customerOptions,
      selectedRows,
      pagination,
      searchForm,
      dateRange,
      createForm,
      createRules,
      
      // 方法
      getTableData,
      searchCustomers,
      handleCustomerChange,
      showCreateDialog,
      handleCreate,
      resetCreateForm,
      handleSearch,
      resetSearch,
      handleSizeChange,
      handleCurrentChange,
      handleSelectionChange,
      viewDetail,
      addRepayment,
      handleExport,
      formatMoney,
      getRepaymentTypeText,
      getStatusText,
      getStatusTagType
    }
  }
}
</script>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-area {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.el-pagination {
  margin-top: 20px;
  text-align: right;
}
</style>
