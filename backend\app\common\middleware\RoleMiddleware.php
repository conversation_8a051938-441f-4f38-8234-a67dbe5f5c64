<?php
declare (strict_types = 1);

namespace app\common\middleware;

use think\Response;

class RoleMiddleware
{
    /**
     * @param \think\Request $request
     * @param \Closure $next
     * @param array $roles 允许的角色列表
     */
    public function handle($request, \Closure $next, ...$roles)
    {
        $user = $request->user ?? null;
        if (!$user) {
            return json(['code'=>401,'message'=>'未认证用户','data'=>null], 401);
        }

        $userRole = $user['role'] ?? '';

        // 允许 admin 通用访问
        $allowed = in_array('admin', [$userRole, ...$roles], true) || in_array($userRole, $roles, true);
        if (!$allowed) {
            return json(['code'=>403,'message'=>'权限不足','data'=>null], 403);
        }

        return $next($request);
    }
}


