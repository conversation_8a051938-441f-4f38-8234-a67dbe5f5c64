<?php
// 中间件配置
return [
    // 别名或分组
    'alias'    => [
        'cors' => \app\common\middleware\CorsMiddleware::class,
        'auth' => \app\common\middleware\AuthMiddleware::class,
        'role' => \app\common\middleware\RoleMiddleware::class,
        'perm' => \app\common\middleware\PermissionMiddleware::class,
    ],
    // 优先级设置，此数组中的中间件会按照数组中的顺序优先执行
    'priority' => [
        \app\common\middleware\CorsMiddleware::class,
    ],
];
