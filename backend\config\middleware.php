<?php
// 中间件配置
return [
    // 别名或分组
    'alias' => [
        // 跨域中间件
        'cors' => \app\common\middleware\CorsMiddleware::class,

        // 认证相关中间件
        'auth' => \app\common\middleware\AuthMiddleware::class,
        'role' => \app\common\middleware\RoleMiddleware::class,
        'perm' => \app\common\middleware\PermissionMiddleware::class,

        // 安全相关中间件
        'rate_limit' => \app\common\middleware\RateLimitMiddleware::class,
        'ip_filter' => \app\common\middleware\IpFilterMiddleware::class,

        // 日志和监控中间件
        'api_log' => \app\common\middleware\ApiLogMiddleware::class,
        'performance' => \app\common\middleware\PerformanceMiddleware::class,

        // 数据处理中间件
        'data_sync' => \app\common\middleware\DataSyncMiddleware::class,
        'validate' => \app\common\middleware\ValidateMiddleware::class,

        // 缓存中间件
        'cache' => \app\common\middleware\CacheMiddleware::class,
    ],

    // 优先级设置，此数组中的中间件会按照数组中的顺序优先执行
    'priority' => [
        // 1. 跨域处理（最高优先级）
        \app\common\middleware\CorsMiddleware::class,

        // 2. 性能监控
        \app\common\middleware\PerformanceMiddleware::class,

        // 3. 安全防护
        \app\common\middleware\RateLimitMiddleware::class,
        \app\common\middleware\IpFilterMiddleware::class,

        // 4. 请求日志
        \app\common\middleware\ApiLogMiddleware::class,

        // 5. 数据验证
        \app\common\middleware\ValidateMiddleware::class,

        // 6. 认证授权
        \app\common\middleware\AuthMiddleware::class,
        \app\common\middleware\RoleMiddleware::class,
        \app\common\middleware\PermissionMiddleware::class,

        // 7. 缓存处理
        \app\common\middleware\CacheMiddleware::class,

        // 8. 数据同步
        \app\common\middleware\DataSyncMiddleware::class,
    ],
];
