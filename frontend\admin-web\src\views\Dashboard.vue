<template>
  <div class="dashboard">
    <el-container>
      <!-- 侧边栏 -->
      <el-aside width="250px">
        <div class="logo">
          <h3>贷后管理系统</h3>
        </div>
        
        <el-menu
          :default-active="$route.path"
          router
          class="sidebar-menu"
        >
          <el-menu-item index="/">
            <el-icon><House /></el-icon>
            <span>首页</span>
          </el-menu-item>
          
          <el-sub-menu index="finance">
            <template #title>
              <el-icon><Money /></el-icon>
              <span>财务管理</span>
            </template>
            <el-menu-item index="/finance/disbursement">放款登记</el-menu-item>
            <el-menu-item index="/finance/repayment">还款管理</el-menu-item>
          </el-sub-menu>
          
          <el-menu-item index="/customer">
            <el-icon><User /></el-icon>
            <span>客户管理</span>
          </el-menu-item>
          
          <el-menu-item index="/investor">
            <el-icon><UserFilled /></el-icon>
            <span>资方管理</span>
          </el-menu-item>
          
          <el-sub-menu index="system">
            <template #title>
              <el-icon><Setting /></el-icon>
              <span>系统管理</span>
            </template>
            <el-menu-item index="/system/blacklist">黑名单管理</el-menu-item>
            <el-menu-item index="/system/config">配置管理</el-menu-item>
          </el-sub-menu>
        </el-menu>
      </el-aside>
      
      <!-- 主内容区 -->
      <el-container>
        <!-- 顶部导航 -->
        <el-header>
          <div class="header-content">
            <div class="breadcrumb">
              <el-breadcrumb separator="/">
                <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
                <el-breadcrumb-item v-if="$route.meta.title">
                  {{ $route.meta.title }}
                </el-breadcrumb-item>
              </el-breadcrumb>
            </div>
            
            <div class="user-info">
              <span>{{ userInfo.real_name }}</span>
              <el-dropdown @command="handleCommand">
                <el-button type="text">
                  <el-icon><Setting /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="logout">退出登录</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </div>
        </el-header>
        
        <!-- 主体内容 -->
        <el-main>
          <router-view v-if="$route.path !== '/'" />
          
          <!-- 首页统计 -->
          <div v-else class="home-content">
            <el-row :gutter="20">
              <el-col :span="6">
                <el-card class="stat-card">
                  <div class="stat-item">
                    <div class="stat-number">{{ statistics.today_disbursement.count }}</div>
                    <div class="stat-label">今日放款笔数</div>
                  </div>
                </el-card>
              </el-col>
              
              <el-col :span="6">
                <el-card class="stat-card">
                  <div class="stat-item">
                    <div class="stat-number">¥{{ formatMoney(statistics.today_disbursement.amount) }}</div>
                    <div class="stat-label">今日放款金额</div>
                  </div>
                </el-card>
              </el-col>
              
              <el-col :span="6">
                <el-card class="stat-card">
                  <div class="stat-item">
                    <div class="stat-number">{{ statistics.today_repayment.count }}</div>
                    <div class="stat-label">今日回款笔数</div>
                  </div>
                </el-card>
              </el-col>
              
              <el-col :span="6">
                <el-card class="stat-card">
                  <div class="stat-item">
                    <div class="stat-number">¥{{ formatMoney(statistics.today_repayment.amount) }}</div>
                    <div class="stat-label">今日回款金额</div>
                  </div>
                </el-card>
              </el-col>
            </el-row>
          </div>
        </el-main>
      </el-container>
    </el-container>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { House, Money, User, UserFilled, Setting } from '@element-plus/icons-vue'
import { authAPI, financeAPI } from '../api'

export default {
  name: 'DashboardPage',
  components: {
    House,
    Money,
    User,
    UserFilled,
    Setting
  },
  setup() {
    const router = useRouter()
    const userInfo = ref(JSON.parse(localStorage.getItem('userInfo') || '{}'))
    
    const statistics = reactive({
      today_disbursement: { count: 0, amount: 0 },
      today_repayment: { count: 0, amount: 0 },
      today_profit: 0,
      overdue: { count: 0, amount: 0 }
    })
    
    // 获取统计数据
    const getStatistics = async () => {
      try {
        const data = await financeAPI.getStatistics()
        Object.assign(statistics, data)
      } catch (error) {
        console.error('获取统计数据失败:', error)
      }
    }
    
    // 格式化金额
    const formatMoney = (amount) => {
      return (amount || 0).toLocaleString()
    }
    
    // 处理下拉菜单命令
    const handleCommand = async (command) => {
      if (command === 'logout') {
        try {
          await ElMessageBox.confirm('确定要退出登录吗？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          })
          
          await authAPI.logout()
          localStorage.removeItem('token')
          localStorage.removeItem('userInfo')
          router.push('/login')
          ElMessage.success('退出成功')
          
        } catch (error) {
          if (error !== 'cancel') {
            console.error('退出登录失败:', error)
          }
        }
      }
    }
    
    onMounted(() => {
      getStatistics()
    })
    
    return {
      userInfo,
      statistics,
      formatMoney,
      handleCommand
    }
  }
}
</script>

<style scoped>
.dashboard {
  height: 100vh;
}

.el-aside {
  background-color: #304156;
  color: #bfcbd9;
}

.logo {
  padding: 20px;
  text-align: center;
  background-color: #2b3949;
}

.logo h3 {
  margin: 0;
  color: #fff;
  font-size: 18px;
}

.sidebar-menu {
  border: none;
  background-color: #304156;
}

.sidebar-menu .el-menu-item {
  color: #bfcbd9;
}

.sidebar-menu .el-menu-item:hover,
.sidebar-menu .el-menu-item.is-active {
  background-color: #263445;
  color: #409eff;
}

.el-header {
  background-color: #fff;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  align-items: center;
  padding: 0 20px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.el-main {
  background-color: #f0f2f5;
  padding: 20px;
}

.home-content {
  padding: 20px 0;
}

.stat-card {
  text-align: center;
}

.stat-item {
  padding: 20px;
}

.stat-number {
  font-size: 28px;
  font-weight: bold;
  color: #409eff;
  margin-bottom: 8px;
}

.stat-label {
  color: #666;
  font-size: 14px;
}
</style>
