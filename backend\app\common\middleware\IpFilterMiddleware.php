<?php
declare(strict_types=1);

namespace app\common\middleware;

use app\common\service\CacheService;
use app\common\service\ConfigService;
use app\common\service\ResponseService;
use think\facade\Log;
use think\Request;
use think\Response;

/**
 * IP过滤中间件
 */
class IpFilterMiddleware
{
    /**
     * 处理请求
     * @param Request $request
     * @param \Closure $next
     * @return Response
     */
    public function handle(Request $request, \Closure $next): Response
    {
        $clientIp = $this->getClientIp($request);
        
        // 检查IP是否被禁用
        if ($this->isBlacklisted($clientIp)) {
            $this->logBlockedRequest($clientIp, $request, 'blacklist');
            return ResponseService::forbidden('访问被拒绝');
        }
        
        // 检查IP白名单（如果启用）
        if ($this->isWhitelistEnabled() && !$this->isWhitelisted($clientIp)) {
            $this->logBlockedRequest($clientIp, $request, 'whitelist');
            return ResponseService::forbidden('访问被拒绝');
        }
        
        // 检查地理位置限制
        if ($this->isGeoBlocked($clientIp)) {
            $this->logBlockedRequest($clientIp, $request, 'geo_block');
            return ResponseService::forbidden('该地区暂不支持访问');
        }
        
        // 记录访问统计
        $this->recordIpAccess($clientIp, $request);
        
        return $next($request);
    }

    /**
     * 获取客户端真实IP
     * @param Request $request
     * @return string
     */
    private function getClientIp(Request $request): string
    {
        // 检查代理头
        $headers = [
            'HTTP_X_FORWARDED_FOR',
            'HTTP_X_REAL_IP',
            'HTTP_CLIENT_IP',
            'HTTP_X_FORWARDED',
            'HTTP_FORWARDED_FOR',
            'HTTP_FORWARDED'
        ];
        
        foreach ($headers as $header) {
            $ip = $request->server($header);
            if (!empty($ip) && $ip !== 'unknown') {
                // 处理多个IP的情况，取第一个
                $ips = explode(',', $ip);
                $ip = trim($ips[0]);
                if ($this->isValidIp($ip)) {
                    return $ip;
                }
            }
        }
        
        return $request->ip();
    }

    /**
     * 验证IP地址格式
     * @param string $ip
     * @return bool
     */
    private function isValidIp(string $ip): bool
    {
        return filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE) !== false;
    }

    /**
     * 检查IP是否在黑名单中
     * @param string $ip
     * @return bool
     */
    private function isBlacklisted(string $ip): bool
    {
        // 从缓存获取黑名单
        $blacklist = CacheService::get('ip_blacklist', 'config') ?: [];
        
        if (empty($blacklist)) {
            // 从配置获取黑名单
            $blacklist = ConfigService::get('ip_blacklist', [], 'security');
            if (!empty($blacklist)) {
                CacheService::set('ip_blacklist', $blacklist, 3600, 'config');
            }
        }
        
        return $this->matchIpList($ip, $blacklist);
    }

    /**
     * 检查白名单是否启用
     * @return bool
     */
    private function isWhitelistEnabled(): bool
    {
        return ConfigService::get('ip_whitelist_enabled', false, 'security');
    }

    /**
     * 检查IP是否在白名单中
     * @param string $ip
     * @return bool
     */
    private function isWhitelisted(string $ip): bool
    {
        // 从缓存获取白名单
        $whitelist = CacheService::get('ip_whitelist', 'config') ?: [];
        
        if (empty($whitelist)) {
            // 从配置获取白名单
            $whitelist = ConfigService::get('ip_whitelist', [], 'security');
            if (!empty($whitelist)) {
                CacheService::set('ip_whitelist', $whitelist, 3600, 'config');
            }
        }
        
        return $this->matchIpList($ip, $whitelist);
    }

    /**
     * 检查IP是否被地理位置阻止
     * @param string $ip
     * @return bool
     */
    private function isGeoBlocked(string $ip): bool
    {
        // 检查是否启用地理位置过滤
        if (!ConfigService::get('geo_filter_enabled', false, 'security')) {
            return false;
        }
        
        // 获取IP地理位置信息
        $geoInfo = $this->getIpGeoInfo($ip);
        if (!$geoInfo) {
            return false;
        }
        
        // 获取被阻止的国家/地区列表
        $blockedCountries = ConfigService::get('blocked_countries', [], 'security');
        $blockedRegions = ConfigService::get('blocked_regions', [], 'security');
        
        // 检查国家
        if (!empty($blockedCountries) && in_array($geoInfo['country'], $blockedCountries)) {
            return true;
        }
        
        // 检查地区
        if (!empty($blockedRegions) && in_array($geoInfo['region'], $blockedRegions)) {
            return true;
        }
        
        return false;
    }

    /**
     * 获取IP地理位置信息
     * @param string $ip
     * @return array|null
     */
    private function getIpGeoInfo(string $ip): ?array
    {
        $cacheKey = "ip_geo:{$ip}";
        $geoInfo = CacheService::get($cacheKey, 'temp');
        
        if ($geoInfo === null) {
            try {
                // 这里可以集成第三方IP地理位置服务
                // 例如：GeoIP2、IP2Location等
                // 暂时返回模拟数据
                $geoInfo = [
                    'country' => 'CN',
                    'region' => 'Beijing',
                    'city' => 'Beijing',
                    'isp' => 'Unknown'
                ];
                
                // 缓存地理位置信息24小时
                CacheService::set($cacheKey, $geoInfo, 86400, 'temp');
            } catch (\Exception $e) {
                Log::error('获取IP地理位置失败', ['ip' => $ip, 'error' => $e->getMessage()]);
                return null;
            }
        }
        
        return $geoInfo;
    }

    /**
     * 匹配IP列表
     * @param string $ip
     * @param array $ipList
     * @return bool
     */
    private function matchIpList(string $ip, array $ipList): bool
    {
        foreach ($ipList as $pattern) {
            if ($this->matchIpPattern($ip, $pattern)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 匹配IP模式
     * @param string $ip
     * @param string $pattern
     * @return bool
     */
    private function matchIpPattern(string $ip, string $pattern): bool
    {
        // 精确匹配
        if ($ip === $pattern) {
            return true;
        }
        
        // CIDR匹配
        if (strpos($pattern, '/') !== false) {
            return $this->matchCidr($ip, $pattern);
        }
        
        // 通配符匹配
        if (strpos($pattern, '*') !== false) {
            $regex = str_replace(['*', '.'], ['.*', '\.'], $pattern);
            return preg_match("/^{$regex}$/", $ip) === 1;
        }
        
        return false;
    }

    /**
     * CIDR匹配
     * @param string $ip
     * @param string $cidr
     * @return bool
     */
    private function matchCidr(string $ip, string $cidr): bool
    {
        list($subnet, $mask) = explode('/', $cidr);
        
        if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_IPV4)) {
            return (ip2long($ip) & ~((1 << (32 - $mask)) - 1)) === ip2long($subnet);
        }
        
        // IPv6支持
        if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_IPV6)) {
            // 简化的IPv6 CIDR匹配
            return substr($ip, 0, strlen($subnet)) === $subnet;
        }
        
        return false;
    }

    /**
     * 记录访问统计
     * @param string $ip
     * @param Request $request
     */
    private function recordIpAccess(string $ip, Request $request): void
    {
        try {
            $today = date('Y-m-d');
            $hour = date('H');
            
            // 每小时IP访问统计
            $hourlyKey = "ip_access:{$today}:{$hour}:{$ip}";
            $count = CacheService::get($hourlyKey, 'temp') ?: 0;
            CacheService::set($hourlyKey, $count + 1, 3600 * 25, 'temp');
            
            // 每日IP访问统计
            $dailyKey = "ip_access:{$today}:{$ip}";
            $dailyCount = CacheService::get($dailyKey, 'temp') ?: 0;
            CacheService::set($dailyKey, $dailyCount + 1, 3600 * 24 * 7, 'temp');
            
        } catch (\Exception $e) {
            Log::error('记录IP访问统计失败', ['ip' => $ip, 'error' => $e->getMessage()]);
        }
    }

    /**
     * 记录被阻止的请求
     * @param string $ip
     * @param Request $request
     * @param string $reason
     */
    private function logBlockedRequest(string $ip, Request $request, string $reason): void
    {
        Log::warning('IP访问被阻止', [
            'ip' => $ip,
            'reason' => $reason,
            'url' => $request->url(),
            'method' => $request->method(),
            'user_agent' => $request->header('user-agent', ''),
            'timestamp' => date('Y-m-d H:i:s')
        ]);
        
        // 统计被阻止的请求
        $today = date('Y-m-d');
        $blockedKey = "ip_blocked:{$today}:{$ip}";
        $blockedCount = CacheService::get($blockedKey, 'temp') ?: 0;
        CacheService::set($blockedKey, $blockedCount + 1, 3600 * 24 * 7, 'temp');
    }

    /**
     * 获取IP访问统计
     * @param string $ip
     * @param string $date
     * @return array
     */
    public static function getIpStats(string $ip, string $date = ''): array
    {
        if (empty($date)) {
            $date = date('Y-m-d');
        }
        
        $stats = [];
        for ($hour = 0; $hour < 24; $hour++) {
            $key = "ip_access:{$date}:{$hour}:{$ip}";
            $stats[$hour] = CacheService::get($key, 'temp') ?: 0;
        }
        
        return $stats;
    }
}
