# 通知服务使用文档

## 概述

通知服务 (`NotificationService`) 是一个统一的消息推送服务，支持短信、邮件、微信和系统内通知等多种通知方式。

## 功能特性

- **多种通知方式**：短信、邮件、微信、系统通知
- **多服务商支持**：阿里云、腾讯云、华为云短信服务
- **模板管理**：支持自定义消息模板和参数替换
- **批量发送**：支持批量发送通知
- **失败重试**：自动重试失败的发送任务
- **发送统计**：记录发送成功率和失败原因
- **配置灵活**：支持多种配置方式

## 快速开始

### 1. 基本用法

```php
use app\common\service\NotificationService;

// 发送短信
$result = NotificationService::send(
    NotificationService::TYPE_SMS,
    ['13800000000'],
    'repayment_reminder',
    [
        'customer_name' => '张三',
        'amount' => '5000',
        'due_date' => '2024-02-15'
    ]
);

// 发送邮件
$result = NotificationService::send(
    NotificationService::TYPE_EMAIL,
    ['<EMAIL>'],
    'overdue_notice',
    [
        'customer_name' => '李四',
        'amount' => '8000',
        'overdue_days' => '5'
    ]
);
```

### 2. 批量发送

```php
$notifications = [
    [
        'type' => NotificationService::TYPE_SMS,
        'targets' => ['13800000001'],
        'template' => 'repayment_reminder',
        'params' => ['customer_name' => '张三', 'amount' => '5000']
    ],
    [
        'type' => NotificationService::TYPE_EMAIL,
        'targets' => ['<EMAIL>'],
        'template' => 'loan_approved',
        'params' => ['customer_name' => '李四', 'amount' => '10000']
    ]
];

$results = NotificationService::sendBatch($notifications);
```

## 配置说明

### 环境变量配置

在 `.env` 文件中添加以下配置：

```env
# 短信配置
SMS_PROVIDER=aliyun
SMS_ALIYUN_ACCESS_KEY=your_access_key
SMS_ALIYUN_ACCESS_SECRET=your_access_secret
SMS_ALIYUN_SIGN_NAME=your_sign_name

# 邮件配置
MAIL_HOST=smtp.qq.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your_password
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME=贷后管理系统
```

### 详细配置

配置文件位于 `backend/config/notification.php`，包含：

- **短信服务商配置**：阿里云、腾讯云、华为云
- **邮件服务配置**：SMTP 服务器设置
- **微信服务配置**：公众号和企业微信
- **模板配置**：预定义消息模板
- **限流配置**：防止频繁发送

## 支持的通知类型

### 1. 短信通知 (SMS)

支持的服务商：
- **阿里云短信**：`SMS_PROVIDER=aliyun`
- **腾讯云短信**：`SMS_PROVIDER=tencent`
- **华为云短信**：`SMS_PROVIDER=huawei`

```php
NotificationService::send(
    NotificationService::TYPE_SMS,
    ['13800000000'],
    'repayment_reminder',
    ['amount' => '5000', 'due_date' => '2024-02-15']
);
```

### 2. 邮件通知 (Email)

支持 SMTP 协议：

```php
NotificationService::send(
    NotificationService::TYPE_EMAIL,
    ['<EMAIL>'],
    'overdue_notice',
    ['amount' => '8000', 'overdue_days' => '5']
);
```

### 3. 微信通知 (WeChat)

支持公众号模板消息和企业微信：

```php
NotificationService::send(
    NotificationService::TYPE_WECHAT,
    ['openid_or_userid'],
    'loan_approved',
    ['amount' => '10000']
);
```

### 4. 系统通知 (System)

应用内消息通知：

```php
NotificationService::send(
    NotificationService::TYPE_SYSTEM,
    [123], // 用户ID
    'repayment_success',
    ['amount' => '5000']
);
```

## 预定义模板

系统提供以下预定义模板：

| 模板标识 | 说明 | 参数 |
|---------|------|------|
| `repayment_reminder` | 还款提醒 | `customer_name`, `amount`, `due_date` |
| `overdue_notice` | 逾期通知 | `customer_name`, `amount`, `overdue_days` |
| `repayment_success` | 还款成功 | `customer_name`, `amount` |
| `loan_approved` | 放款通过 | `customer_name`, `amount` |
| `verify_code` | 验证码 | `verify_code` |

## API 接口

### 测试接口

系统提供了测试接口，用于验证通知服务配置：

```http
# 测试短信发送
POST /api/notification-examples/test/sms
{
    "phone": "13800000000"
}

# 测试邮件发送
POST /api/notification-examples/test/email
{
    "email": "<EMAIL>"
}

# 获取配置信息
GET /api/notification-examples/config
```

### 业务接口

```http
# 发送还款提醒短信
POST /api/notification-examples/sms/repayment-reminder
{
    "customer_phone": "13800000000",
    "customer_name": "张三",
    "amount": "5000",
    "due_date": "2024-02-15"
}

# 发送逾期通知邮件
POST /api/notification-examples/email/overdue-notice
{
    "customer_email": "<EMAIL>",
    "customer_name": "李四",
    "amount": "8000",
    "overdue_days": "5"
}
```

## 定时任务

### 自动逾期提醒

系统支持定时发送逾期提醒：

```http
POST /api/notification-examples/auto/overdue-reminders
```

可以配置为 Cron 任务：
```bash
# 每天上午9点执行逾期提醒
0 9 * * * curl -X POST "http://your-domain.com/api/notification-examples/auto/overdue-reminders"
```

## 扩展开发

### 添加新的服务商

1. 在 `NotificationService` 中添加新的常量
2. 实现对应的发送方法
3. 在配置文件中添加相关配置

### 自定义模板

在 `getSystemTemplate()` 方法中添加新的模板：

```php
$templates = [
    'your_template' => '您的自定义模板内容 {param1} {param2}',
    // ...
];
```

## 最佳实践

1. **异步发送**：启用 `NOTIFICATION_ASYNC=true` 提高性能
2. **限流控制**：合理设置发送频率限制
3. **模板管理**：使用统一的模板管理，便于维护
4. **错误处理**：监控发送失败，及时处理异常
5. **日志记录**：开启详细日志，便于问题排查

## 故障排除

### 常见问题

1. **短信发送失败**
   - 检查 Access Key 和 Secret 是否正确
   - 确认签名是否已审核通过
   - 验证模板 ID 是否正确

2. **邮件发送失败**
   - 检查 SMTP 服务器配置
   - 确认邮箱密码（可能需要应用专用密码）
   - 检查网络连接和防火墙设置

3. **配置问题**
   - 确认 `.env` 文件配置正确
   - 检查配置文件语法
   - 验证必要的扩展是否已安装

### 调试技巧

- 启用详细日志：`'log' => ['level' => 'debug']`
- 使用测试接口验证配置
- 查看系统日志文件

## 安全考虑

1. **敏感信息保护**：Access Key 等敏感信息存储在环境变量中
2. **权限控制**：通知发送接口需要适当的权限验证
3. **频率限制**：防止短信/邮件轰炸
4. **数据验证**：对用户输入进行严格验证

## 更新日志

- **v1.0.0**：初始版本，支持短信、邮件、系统通知
- **v1.1.0**：添加批量发送功能
- **v1.2.0**：增加微信通知支持（计划中）
