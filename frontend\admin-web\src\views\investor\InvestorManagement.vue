<template>
  <div class="investor-management">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>资方管理</span>
        </div>
      </template>
      
      <!-- 搜索区域 -->
      <div class="search-area">
        <el-form :inline="true" :model="searchForm">
          <el-form-item label="搜索类型">
            <el-select v-model="searchForm.searchType" placeholder="请选择搜索类型">
              <el-option label="客户姓名" value="name"/>
              <el-option label="身份证后4位" value="id_card"/>
            </el-select>
          </el-form-item>
          
          <el-form-item label="关键词">
            <el-input
              v-model="searchForm.keyword"
              placeholder="请输入搜索关键词"
              clearable
            />
          </el-form-item>
          
          <el-form-item>
            <el-button type="primary" @click="handleSearch">查询客户</el-button>
            <el-button @click="resetSearch">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
      
      <!-- 查询结果 -->
      <div v-if="searchResults.length > 0">
        <h3>查询结果</h3>
        <el-table
          :data="searchResults"
          style="width: 100%"
          :loading="searchLoading"
        >
          <el-table-column prop="name" label="客户姓名" width="120"/>
          <el-table-column prop="phone" label="手机号" width="130"/>
          <el-table-column prop="id_card" label="身份证号" width="180">
            <template #default="scope">
              {{ maskIdCard(scope.row.id_card) }}
            </template>
          </el-table-column>
          <el-table-column prop="total_loan_amount" label="总放款金额" width="120">
            <template #default="scope">¥{{ formatMoney(scope.row.total_loan_amount) }}</template>
          </el-table-column>
          <el-table-column prop="total_repaid_amount" label="已还金额" width="120">
            <template #default="scope">¥{{ formatMoney(scope.row.total_repaid_amount) }}</template>
          </el-table-column>
          <el-table-column prop="active_loans_count" label="活跃贷款数" width="100"/>
          <el-table-column prop="last_loan_date" label="最后放款日期" width="120"/>
          <el-table-column label="客户标签" min-width="200">
            <template #default="scope">
              <el-tag
                v-for="tag in scope.row.tags || []"
                :key="tag"
                style="margin-right: 8px;"
                size="small"
              >
                {{ tag }}
              </el-tag>
              <el-button
                size="small"
                type="primary"
                link
                @click="editTags(scope.row)"
              >
                编辑标签
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      
      <!-- 我的客户列表 -->
      <div style="margin-top: 30px;">
        <h3>我的客户</h3>
        <el-table
          :data="myCustomers"
          style="width: 100%"
          :loading="tableLoading"
        >
          <el-table-column prop="name" label="客户姓名" width="120"/>
          <el-table-column prop="phone" label="手机号" width="130"/>
          <el-table-column prop="total_loan_amount" label="总放款金额" width="120">
            <template #default="scope">¥{{ formatMoney(scope.row.total_loan_amount) }}</template>
          </el-table-column>
          <el-table-column prop="total_repaid_amount" label="已还金额" width="120">
            <template #default="scope">¥{{ formatMoney(scope.row.total_repaid_amount) }}</template>
          </el-table-column>
          <el-table-column prop="active_loans_count" label="活跃贷款数" width="100"/>
          <el-table-column prop="last_loan_date" label="最后放款日期" width="120"/>
          <el-table-column label="客户标签" min-width="200">
            <template #default="scope">
              <el-tag
                v-for="tag in scope.row.tags || []"
                :key="tag"
                style="margin-right: 8px;"
                size="small"
              >
                {{ tag }}
              </el-tag>
              <el-button
                size="small"
                type="primary"
                link
                @click="editTags(scope.row)"
              >
                编辑标签
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        
        <!-- 分页 -->
        <div class="pagination-wrapper">
          <el-pagination
            v-model:current-page="pagination.page"
            v-model:page-size="pagination.limit"
            :total="pagination.total"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </el-card>

    <!-- 编辑标签对话框 -->
    <el-dialog
      v-model="tagsDialogVisible"
      title="编辑客户标签"
      width="500px"
    >
      <el-form label-width="80px">
        <el-form-item label="客户姓名">
          <el-input :value="currentCustomer?.name" disabled/>
        </el-form-item>
        <el-form-item label="客户标签">
          <el-tag
            v-for="tag in currentTags"
            :key="tag"
            closable
            style="margin-right: 8px; margin-bottom: 8px;"
            @close="removeTag(tag)"
          >
            {{ tag }}
          </el-tag>
          <el-input
            v-if="tagInputVisible"
            ref="tagInputRef"
            v-model="tagInputValue"
            size="small"
            style="width: 100px;"
            @keyup.enter="addTag"
            @blur="addTag"
          />
          <el-button
            v-else
            size="small"
            @click="showTagInput"
          >
            + 新标签
          </el-button>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="tagsDialogVisible = false">取消</el-button>
        <el-button type="primary" :loading="tagsLoading" @click="saveTags">保存</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { ref, reactive, onMounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'

export default {
  name: 'InvestorManagement',
  setup() {
    const searchLoading = ref(false)
    const tableLoading = ref(false)
    const tagsLoading = ref(false)
    const tagsDialogVisible = ref(false)
    const tagInputVisible = ref(false)
    const tagInputRef = ref()
    const tagInputValue = ref('')
    
    const searchResults = ref([])
    const myCustomers = ref([])
    const currentCustomer = ref(null)
    const currentTags = ref([])
    
    const pagination = reactive({
      page: 1,
      limit: 20,
      total: 0
    })
    
    const searchForm = reactive({
      searchType: 'name',
      keyword: ''
    })
    
    // 获取我的客户列表
    const getMyCustomers = async () => {
      try {
        tableLoading.value = true
        // TODO: 调用实际API
        // const data = await investorAPI.getMyCustomers(pagination)
        // myCustomers.value = data.data
        // pagination.total = data.total
        
        // 模拟数据
        myCustomers.value = [
          {
            id: 1,
            name: '张三',
            phone: '138****5678',
            total_loan_amount: 50000,
            total_repaid_amount: 30000,
            active_loans_count: 2,
            last_loan_date: '2024-01-15',
            tags: ['优质客户', '按时还款']
          }
        ]
        pagination.total = 1
      } catch (error) {
        ElMessage.error('获取客户列表失败')
      } finally {
        tableLoading.value = false
      }
    }
    
    // 搜索客户
    const handleSearch = async () => {
      if (!searchForm.keyword.trim()) {
        ElMessage.warning('请输入搜索关键词')
        return
      }
      
      try {
        searchLoading.value = true
        // TODO: 调用实际API
        // const data = await investorAPI.queryCustomer(searchForm)
        // searchResults.value = data
        
        // 模拟数据
        searchResults.value = [
          {
            id: 2,
            name: '李四',
            phone: '139****1234',
            id_card: '310101199001011234',
            total_loan_amount: 80000,
            total_repaid_amount: 60000,
            active_loans_count: 1,
            last_loan_date: '2024-01-20',
            tags: ['新客户']
          }
        ]
      } catch (error) {
        ElMessage.error('查询失败')
      } finally {
        searchLoading.value = false
      }
    }
    
    // 重置搜索
    const resetSearch = () => {
      Object.assign(searchForm, {
        searchType: 'name',
        keyword: ''
      })
      searchResults.value = []
    }
    
    // 编辑标签
    const editTags = (customer) => {
      currentCustomer.value = customer
      currentTags.value = [...(customer.tags || [])]
      tagsDialogVisible.value = true
    }
    
    // 显示标签输入框
    const showTagInput = () => {
      tagInputVisible.value = true
      nextTick(() => {
        tagInputRef.value?.focus()
      })
    }
    
    // 添加标签
    const addTag = () => {
      const value = tagInputValue.value.trim()
      if (value && !currentTags.value.includes(value)) {
        currentTags.value.push(value)
      }
      tagInputVisible.value = false
      tagInputValue.value = ''
    }
    
    // 移除标签
    const removeTag = (tag) => {
      const index = currentTags.value.indexOf(tag)
      if (index > -1) {
        currentTags.value.splice(index, 1)
      }
    }
    
    // 保存标签
    const saveTags = async () => {
      try {
        tagsLoading.value = true
        // TODO: 调用实际API
        // await investorAPI.updateCustomerTags(currentCustomer.value.id, currentTags.value)
        
        // 更新本地数据
        if (currentCustomer.value) {
          currentCustomer.value.tags = [...currentTags.value]
        }
        
        ElMessage.success('保存成功')
        tagsDialogVisible.value = false
      } catch (error) {
        ElMessage.error('保存失败')
      } finally {
        tagsLoading.value = false
      }
    }
    
    // 分页处理
    const handleSizeChange = (size) => {
      pagination.limit = size
      pagination.page = 1
      getMyCustomers()
    }
    
    const handleCurrentChange = (page) => {
      pagination.page = page
      getMyCustomers()
    }
    
    // 工具函数
    const formatMoney = (amount) => {
      return (amount || 0).toLocaleString()
    }
    
    const maskIdCard = (idCard) => {
      if (!idCard) return ''
      return idCard.substring(0, 6) + '********' + idCard.substring(14)
    }
    
    onMounted(() => {
      getMyCustomers()
    })
    
    return {
      // 响应式数据
      searchLoading,
      tableLoading,
      tagsLoading,
      tagsDialogVisible,
      tagInputVisible,
      tagInputRef,
      tagInputValue,
      searchResults,
      myCustomers,
      currentCustomer,
      currentTags,
      pagination,
      searchForm,
      
      // 方法
      getMyCustomers,
      handleSearch,
      resetSearch,
      editTags,
      showTagInput,
      addTag,
      removeTag,
      saveTags,
      handleSizeChange,
      handleCurrentChange,
      formatMoney,
      maskIdCard
    }
  }
}
</script>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-area {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.pagination-wrapper {
  margin-top: 20px;
  text-align: right;
}
</style>
