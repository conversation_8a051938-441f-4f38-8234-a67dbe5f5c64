# 资方查询小程序系统设计

## 一、资方查询小程序整体架构

### 1. 系统概述

资方查询小程序是一个独立的客户档案查询平台，专为外部资方和机构提供客户信息查询服务。通过严格的入驻审核机制，确保查询方的合法性和可信度。

### 2. 核心功能模块

```
资方查询小程序
├── 登录注册模块
│   ├── 微信授权登录
│   ├── 手机号注册
│   └── 账号绑定管理
├── 入驻申请模块
│   ├── 资质申请
│   ├── 审核流程
│   └── 权限开通
├── 客户查询模块
│   ├── 精确查询（姓名+身份证尾号）
│   ├── 查询结果展示
│   └── 查询历史记录
├── 我的客户模块
│   ├── 客户列表管理
│   ├── 客户标签管理
│   ├── 备注信息管理
│   └── 风险状态追踪
└── 账户管理模块
    ├── 个人信息
    ├── 查询统计
    └── 使用记录
```

## 二、数据库设计

### 1. 资方信息表

```sql
-- 资方信息表 (investors)
CREATE TABLE investors (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    investor_code VARCHAR(20) NOT NULL UNIQUE COMMENT '资方编码',
    company_name VARCHAR(100) NOT NULL COMMENT '公司名称',
    company_type ENUM('bank','finance_company','p2p','private_lending','other') NOT NULL COMMENT '公司类型',
    legal_person VARCHAR(50) NOT NULL COMMENT '法人姓名',
    business_license VARCHAR(30) NOT NULL COMMENT '营业执照号',
    registered_capital DECIMAL(15,2) COMMENT '注册资本',
    contact_person VARCHAR(50) NOT NULL COMMENT '联系人',
    contact_phone VARCHAR(20) NOT NULL COMMENT '联系电话',
    contact_email VARCHAR(100) COMMENT '联系邮箱',
    business_address TEXT COMMENT '经营地址',
    business_scope TEXT COMMENT '经营范围',
    license_photos JSON COMMENT '资质证照',
    apply_reason TEXT COMMENT '申请理由',
    status ENUM('pending','approved','rejected','suspended') DEFAULT 'pending' COMMENT '状态',
    approved_by INT COMMENT '审批人ID',
    approved_at TIMESTAMP NULL COMMENT '审批时间',
    rejection_reason TEXT COMMENT '拒绝原因',
    query_limit INT DEFAULT 100 COMMENT '每月查询限额',
    used_queries INT DEFAULT 0 COMMENT '已使用查询次数',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_company_name (company_name),
    INDEX idx_status (status),
    INDEX idx_contact_phone (contact_phone)
) COMMENT='资方信息表';

-- 资方用户表 (investor_users)
CREATE TABLE investor_users (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    investor_id BIGINT UNSIGNED NOT NULL COMMENT '资方ID',
    openid VARCHAR(100) NOT NULL COMMENT '微信OpenID',
    unionid VARCHAR(100) COMMENT '微信UnionID',
    nickname VARCHAR(100) COMMENT '微信昵称',
    avatar VARCHAR(500) COMMENT '头像',
    phone VARCHAR(20) COMMENT '手机号',
    real_name VARCHAR(50) COMMENT '真实姓名',
    id_card VARCHAR(18) COMMENT '身份证号',
    position VARCHAR(50) COMMENT '职位',
    role ENUM('admin','operator','viewer') DEFAULT 'operator' COMMENT '角色',
    status ENUM('active','inactive') DEFAULT 'active' COMMENT '状态',
    last_login_time TIMESTAMP NULL COMMENT '最后登录时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    UNIQUE KEY unique_openid (openid),
    FOREIGN KEY (investor_id) REFERENCES investors(id),
    INDEX idx_investor_id (investor_id),
    INDEX idx_phone (phone)
) COMMENT='资方用户表';

-- 客户查询记录表 (customer_query_logs)
CREATE TABLE customer_query_logs (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    query_id VARCHAR(32) NOT NULL UNIQUE COMMENT '查询ID',
    investor_id BIGINT UNSIGNED NOT NULL COMMENT '资方ID',
    user_id BIGINT UNSIGNED NOT NULL COMMENT '查询用户ID',
    customer_name VARCHAR(50) NOT NULL COMMENT '查询客户姓名',
    id_card_suffix VARCHAR(4) NOT NULL COMMENT '身份证后4位',
    customer_id BIGINT UNSIGNED COMMENT '匹配到的客户ID',
    query_result ENUM('found','not_found','multiple') NOT NULL COMMENT '查询结果',
    query_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '查询时间',
    ip_address VARCHAR(45) COMMENT 'IP地址',
    user_agent TEXT COMMENT '用户代理',
    
    FOREIGN KEY (investor_id) REFERENCES investors(id),
    FOREIGN KEY (user_id) REFERENCES investor_users(id),
    INDEX idx_investor_id (investor_id),
    INDEX idx_customer_query (customer_name, id_card_suffix),
    INDEX idx_query_time (query_time)
) COMMENT='客户查询记录表';

-- 资方客户管理表 (investor_customers)
CREATE TABLE investor_customers (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    investor_id BIGINT UNSIGNED NOT NULL COMMENT '资方ID',
    user_id BIGINT UNSIGNED NOT NULL COMMENT '用户ID',
    customer_id BIGINT UNSIGNED NOT NULL COMMENT '客户ID',
    query_log_id BIGINT UNSIGNED NOT NULL COMMENT '首次查询记录ID',
    customer_status ENUM('normal','overdue','bad_debt','fraud','blacklist') DEFAULT 'normal' COMMENT '客户状态',
    risk_level ENUM('low','medium','high','extreme') DEFAULT 'medium' COMMENT '风险等级',
    cooperation_status ENUM('none','negotiating','cooperating','ended') DEFAULT 'none' COMMENT '合作状态',
    tags JSON COMMENT '客户标签',
    notes TEXT COMMENT '备注信息',
    last_updated_by BIGINT UNSIGNED COMMENT '最后更新人',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    UNIQUE KEY unique_investor_customer (investor_id, customer_id),
    FOREIGN KEY (investor_id) REFERENCES investors(id),
    FOREIGN KEY (customer_id) REFERENCES customers(id),
    FOREIGN KEY (query_log_id) REFERENCES customer_query_logs(id),
    INDEX idx_investor_id (investor_id),
    INDEX idx_customer_status (customer_status),
    INDEX idx_cooperation_status (cooperation_status)
) COMMENT='资方客户管理表';

-- 客户标签表 (customer_tags)
CREATE TABLE customer_tags (
    id INT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    tag_name VARCHAR(50) NOT NULL COMMENT '标签名称',
    tag_type ENUM('status','risk','business','custom') NOT NULL COMMENT '标签类型',
    tag_color VARCHAR(7) DEFAULT '#1890ff' COMMENT '标签颜色',
    description VARCHAR(200) COMMENT '标签描述',
    is_system TINYINT DEFAULT 0 COMMENT '是否系统标签',
    created_by INT COMMENT '创建人',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE KEY unique_tag_name (tag_name),
    INDEX idx_tag_type (tag_type)
) COMMENT='客户标签表';
```

### 2. 借款周期设计优化

```sql
-- 更新贷款申请表，支持周/天周期
ALTER TABLE loan_applications 
ADD COLUMN term_unit ENUM('day','week','month') NOT NULL DEFAULT 'day' COMMENT '期限单位' AFTER apply_term,
ADD COLUMN actual_term_days INT COMMENT '实际期限天数' AFTER term_unit;

-- 更新触发器，自动计算实际天数
DELIMITER $$
CREATE TRIGGER loan_term_calculation BEFORE INSERT ON loan_applications
FOR EACH ROW
BEGIN
    IF NEW.term_unit = 'day' THEN
        SET NEW.actual_term_days = NEW.apply_term;
    ELSEIF NEW.term_unit = 'week' THEN
        SET NEW.actual_term_days = NEW.apply_term * 7;
    ELSEIF NEW.term_unit = 'month' THEN
        SET NEW.actual_term_days = NEW.apply_term * 30;
    END IF;
END$$
DELIMITER ;

-- 还款计划表
CREATE TABLE repayment_plans (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    application_id BIGINT UNSIGNED NOT NULL COMMENT '申请ID',
    period_number INT NOT NULL COMMENT '期数',
    due_date DATE NOT NULL COMMENT '应还日期',
    principal_amount DECIMAL(10,2) NOT NULL COMMENT '应还本金',
    interest_amount DECIMAL(10,2) NOT NULL COMMENT '应还利息',
    total_amount DECIMAL(10,2) NOT NULL COMMENT '应还总额',
    paid_amount DECIMAL(10,2) DEFAULT 0 COMMENT '已还金额',
    status ENUM('pending','paid','overdue','partial') DEFAULT 'pending' COMMENT '状态',
    paid_date TIMESTAMP NULL COMMENT '实际还款日期',
    overdue_days INT DEFAULT 0 COMMENT '逾期天数',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (application_id) REFERENCES loan_applications(id),
    INDEX idx_application_id (application_id),
    INDEX idx_due_date (due_date),
    INDEX idx_status (status)
) COMMENT='还款计划表';
```

## 三、资方查询小程序后端接口

### 1. 资方入驻管理

```php
// app/investor/controller/ApplyController.php
<?php

namespace app\investor\controller;

use app\common\service\InvestorService;
use app\common\service\FileService;

class ApplyController extends BaseController
{
    /**
     * 提交入驻申请
     */
    public function submitApplication()
    {
        $data = $this->request->param();
        
        try {
            // 数据验证
            $this->validate($data, [
                'company_name' => 'require|max:100',
                'legal_person' => 'require|max:50',
                'business_license' => 'require|max:30',
                'contact_person' => 'require|max:50',
                'contact_phone' => 'require|mobile',
                'contact_email' => 'email',
                'apply_reason' => 'require'
            ]);
            
            // 检查是否已申请
            $exists = Investor::where('business_license', $data['business_license'])
                            ->orWhere('contact_phone', $data['contact_phone'])
                            ->find();
            
            if ($exists) {
                return json(['code' => 400, 'message' => '该企业或联系方式已申请过']);
            }
            
            // 创建申请
            $investor = InvestorService::createApplication($data);
            
            return json([
                'code' => 200,
                'message' => '申请提交成功，请等待审核',
                'data' => ['investor_id' => $investor->id]
            ]);
            
        } catch (\Exception $e) {
            return json(['code' => 500, 'message' => $e->getMessage()]);
        }
    }
    
    /**
     * 上传资质证照
     */
    public function uploadLicense()
    {
        $investorId = $this->request->param('investor_id');
        $file = $this->request->file('license');
        
        try {
            if (!$file) {
                throw new \Exception('请选择要上传的文件');
            }
            
            // 验证文件
            $info = $file->validate(['size' => 5242880, 'ext' => 'jpg,jpeg,png,pdf']);
            if (!$info) {
                throw new \Exception('文件格式不正确或大小超过5MB');
            }
            
            // 保存文件
            $filepath = FileService::upload($file, 'investor/licenses');
            
            // 更新资方信息
            $investor = Investor::find($investorId);
            $licenses = $investor->license_photos ? json_decode($investor->license_photos, true) : [];
            $licenses[] = $filepath;
            $investor->license_photos = json_encode($licenses);
            $investor->save();
            
            return json([
                'code' => 200,
                'message' => '上传成功',
                'data' => ['filepath' => $filepath]
            ]);
            
        } catch (\Exception $e) {
            return json(['code' => 500, 'message' => $e->getMessage()]);
        }
    }
    
    /**
     * 查询申请状态
     */
    public function getApplicationStatus()
    {
        $investorId = $this->request->param('investor_id');
        
        $investor = Investor::find($investorId);
        if (!$investor) {
            return json(['code' => 404, 'message' => '申请记录不存在']);
        }
        
        return json([
            'code' => 200,
            'data' => [
                'status' => $investor->status,
                'company_name' => $investor->company_name,
                'apply_time' => $investor->created_at,
                'approved_time' => $investor->approved_at,
                'rejection_reason' => $investor->rejection_reason
            ]
        ]);
    }
}
```

### 2. 客户查询接口

```php
// app/investor/controller/QueryController.php
<?php

namespace app\investor\controller;

use app\common\service\CustomerQueryService;
use app\common\service\InvestorCustomerService;

class QueryController extends BaseController
{
    /**
     * 查询客户档案
     */
    public function queryCustomer()
    {
        $data = $this->request->param();
        
        try {
            // 验证查询权限
            $user = $this->getCurrentUser();
            if (!InvestorService::checkQueryPermission($user->investor_id)) {
                return json(['code' => 403, 'message' => '查询次数已达上限']);
            }
            
            // 数据验证
            $this->validate($data, [
                'customer_name' => 'require|max:50',
                'id_card_suffix' => 'require|length:4'
            ]);
            
            // 执行查询
            $result = CustomerQueryService::query(
                $data['customer_name'],
                $data['id_card_suffix'],
                $user->investor_id,
                $user->id
            );
            
            return json([
                'code' => 200,
                'message' => '查询完成',
                'data' => $result
            ]);
            
        } catch (\Exception $e) {
            return json(['code' => 500, 'message' => $e->getMessage()]);
        }
    }
    
    /**
     * 获取查询历史
     */
    public function getQueryHistory()
    {
        $page = $this->request->param('page', 1);
        $limit = $this->request->param('limit', 20);
        $user = $this->getCurrentUser();
        
        $query = CustomerQueryLog::where('investor_id', $user->investor_id)
                                ->where('user_id', $user->id)
                                ->order('query_time', 'desc');
        
        $total = $query->count();
        $list = $query->page($page, $limit)->select();
        
        return json([
            'code' => 200,
            'data' => [
                'list' => $list,
                'total' => $total,
                'page' => $page,
                'limit' => $limit
            ]
        ]);
    }
    
    /**
     * 查看查询详情
     */
    public function getQueryDetail()
    {
        $queryId = $this->request->param('query_id');
        $user = $this->getCurrentUser();
        
        $queryLog = CustomerQueryLog::where('query_id', $queryId)
                                  ->where('investor_id', $user->investor_id)
                                  ->find();
        
        if (!$queryLog) {
            return json(['code' => 404, 'message' => '查询记录不存在']);
        }
        
        // 获取该客户的所有查询记录
        $allQueries = CustomerQueryLog::where('customer_name', $queryLog->customer_name)
                                    ->where('id_card_suffix', $queryLog->id_card_suffix)
                                    ->with(['investor', 'user'])
                                    ->order('query_time', 'desc')
                                    ->select();
        
        return json([
            'code' => 200,
            'data' => [
                'query_info' => $queryLog,
                'all_queries' => $allQueries
            ]
        ]);
    }
}
```

### 3. 我的客户管理

```php
// app/investor/controller/MyCustomerController.php
<?php

namespace app\investor\controller;

use app\common\service\InvestorCustomerService;

class MyCustomerController extends BaseController
{
    /**
     * 获取我的客户列表
     */
    public function getMyCustomers()
    {
        $page = $this->request->param('page', 1);
        $limit = $this->request->param('limit', 20);
        $status = $this->request->param('status');
        $keyword = $this->request->param('keyword');
        
        $user = $this->getCurrentUser();
        
        $result = InvestorCustomerService::getCustomerList(
            $user->investor_id,
            $page,
            $limit,
            $status,
            $keyword
        );
        
        return json(['code' => 200, 'data' => $result]);
    }
    
    /**
     * 更新客户状态
     */
    public function updateCustomerStatus()
    {
        $data = $this->request->param();
        $user = $this->getCurrentUser();
        
        try {
            $this->validate($data, [
                'customer_id' => 'require|integer',
                'customer_status' => 'require|in:normal,overdue,bad_debt,fraud,blacklist',
                'risk_level' => 'in:low,medium,high,extreme',
                'cooperation_status' => 'in:none,negotiating,cooperating,ended'
            ]);
            
            $result = InvestorCustomerService::updateCustomerStatus(
                $user->investor_id,
                $data['customer_id'],
                $data,
                $user->id
            );
            
            return json([
                'code' => 200,
                'message' => '更新成功',
                'data' => $result
            ]);
            
        } catch (\Exception $e) {
            return json(['code' => 500, 'message' => $e->getMessage()]);
        }
    }
    
    /**
     * 添加客户标签
     */
    public function addCustomerTags()
    {
        $data = $this->request->param();
        $user = $this->getCurrentUser();
        
        try {
            $this->validate($data, [
                'customer_id' => 'require|integer',
                'tags' => 'require|array'
            ]);
            
            $result = InvestorCustomerService::addCustomerTags(
                $user->investor_id,
                $data['customer_id'],
                $data['tags'],
                $user->id
            );
            
            return json([
                'code' => 200,
                'message' => '标签添加成功',
                'data' => $result
            ]);
            
        } catch (\Exception $e) {
            return json(['code' => 500, 'message' => $e->getMessage()]);
        }
    }
    
    /**
     * 更新客户备注
     */
    public function updateCustomerNotes()
    {
        $data = $this->request->param();
        $user = $this->getCurrentUser();
        
        try {
            $this->validate($data, [
                'customer_id' => 'require|integer',
                'notes' => 'max:1000'
            ]);
            
            $result = InvestorCustomerService::updateCustomerNotes(
                $user->investor_id,
                $data['customer_id'],
                $data['notes'],
                $user->id
            );
            
            return json([
                'code' => 200,
                'message' => '备注更新成功'
            ]);
            
        } catch (\Exception $e) {
            return json(['code' => 500, 'message' => $e->getMessage()]);
        }
    }
    
    /**
     * 获取客户详细信息
     */
    public function getCustomerDetail()
    {
        $customerId = $this->request->param('customer_id');
        $user = $this->getCurrentUser();
        
        $detail = InvestorCustomerService::getCustomerDetail(
            $user->investor_id,
            $customerId
        );
        
        if (!$detail) {
            return json(['code' => 404, 'message' => '客户不存在']);
        }
        
        return json(['code' => 200, 'data' => $detail]);
    }
}
```

## 四、客户查询服务实现

```php
// app/common/service/CustomerQueryService.php
<?php

namespace app\common\service;

use app\common\model\Customer;
use app\common\model\CustomerQueryLog;
use app\common\model\InvestorCustomer;

class CustomerQueryService
{
    /**
     * 执行客户查询
     */
    public static function query($customerName, $idCardSuffix, $investorId, $userId)
    {
        // 生成查询ID
        $queryId = md5($investorId . $userId . time() . rand(1000, 9999));
        
        // 模糊匹配客户
        $customers = Customer::where('name', $customerName)
                            ->where('id_card', 'like', '%' . $idCardSuffix)
                            ->select();
        
        $queryResult = 'not_found';
        $matchedCustomer = null;
        
        if (count($customers) == 1) {
            $queryResult = 'found';
            $matchedCustomer = $customers[0];
        } elseif (count($customers) > 1) {
            $queryResult = 'multiple';
        }
        
        // 记录查询日志
        $queryLog = CustomerQueryLog::create([
            'query_id' => $queryId,
            'investor_id' => $investorId,
            'user_id' => $userId,
            'customer_name' => $customerName,
            'id_card_suffix' => $idCardSuffix,
            'customer_id' => $matchedCustomer ? $matchedCustomer->id : null,
            'query_result' => $queryResult,
            'ip_address' => request()->ip(),
            'user_agent' => request()->header('user-agent')
        ]);
        
        // 更新查询次数
        InvestorService::incrementQueryCount($investorId);
        
        $result = [
            'query_id' => $queryId,
            'query_result' => $queryResult,
            'customer_info' => null,
            'all_queries' => []
        ];
        
        if ($queryResult == 'found') {
            // 自动添加到我的客户
            self::addToMyCustomers($investorId, $userId, $matchedCustomer->id, $queryLog->id);
            
            // 构建客户基本信息（脱敏）
            $result['customer_info'] = [
                'customer_id' => $matchedCustomer->id,
                'name' => $matchedCustomer->name,
                'phone' => EncryptService::maskPhone($matchedCustomer->phone),
                'id_card' => EncryptService::maskIdCard($matchedCustomer->id_card),
                'gender' => $matchedCustomer->gender,
                'age' => self::calculateAge($matchedCustomer->id_card),
                'industry' => $matchedCustomer->industry,
                'risk_level' => $matchedCustomer->risk_level,
                'register_time' => $matchedCustomer->created_at
            ];
            
            // 获取该客户的所有查询记录
            $result['all_queries'] = self::getCustomerQueryHistory($customerName, $idCardSuffix);
        }
        
        return $result;
    }
    
    /**
     * 添加到我的客户
     */
    private static function addToMyCustomers($investorId, $userId, $customerId, $queryLogId)
    {
        $exists = InvestorCustomer::where([
            'investor_id' => $investorId,
            'customer_id' => $customerId
        ])->find();
        
        if (!$exists) {
            InvestorCustomer::create([
                'investor_id' => $investorId,
                'user_id' => $userId,
                'customer_id' => $customerId,
                'query_log_id' => $queryLogId,
                'customer_status' => 'normal',
                'risk_level' => 'medium',
                'cooperation_status' => 'none'
            ]);
        }
    }
    
    /**
     * 获取客户查询历史
     */
    private static function getCustomerQueryHistory($customerName, $idCardSuffix)
    {
        return CustomerQueryLog::where('customer_name', $customerName)
                              ->where('id_card_suffix', $idCardSuffix)
                              ->where('query_result', 'found')
                              ->with(['investor' => function($query) {
                                  $query->field('id,company_name,company_type');
                              }])
                              ->order('query_time', 'desc')
                              ->limit(50)
                              ->select()
                              ->toArray();
    }
    
    /**
     * 计算年龄
     */
    private static function calculateAge($idCard)
    {
        if (strlen($idCard) == 18) {
            $birthYear = substr($idCard, 6, 4);
            $birthMonth = substr($idCard, 10, 2);
            $birthDay = substr($idCard, 12, 2);
            
            $birthDate = $birthYear . '-' . $birthMonth . '-' . $birthDay;
            return floor((time() - strtotime($birthDate)) / (365 * 24 * 3600));
        }
        
        return null;
    }
}

// app/common/service/InvestorCustomerService.php
<?php

namespace app\common\service;

class InvestorCustomerService
{
    /**
     * 获取客户列表
     */
    public static function getCustomerList($investorId, $page = 1, $limit = 20, $status = null, $keyword = null)
    {
        $query = InvestorCustomer::alias('ic')
                                ->join('customers c', 'ic.customer_id = c.id')
                                ->where('ic.investor_id', $investorId);
        
        if ($status) {
            $query->where('ic.customer_status', $status);
        }
        
        if ($keyword) {
            $query->where('c.name|c.phone', 'like', '%' . $keyword . '%');
        }
        
        $total = $query->count();
        
        $list = $query->field([
                      'ic.*',
                      'c.name',
                      'c.phone',
                      'c.id_card',
                      'c.gender',
                      'c.industry',
                      'c.risk_level as customer_risk_level'
                  ])
                  ->order('ic.updated_at', 'desc')
                  ->page($page, $limit)
                  ->select();
        
        // 数据处理
        foreach ($list as &$item) {
            $item['phone'] = EncryptService::maskPhone($item['phone']);
            $item['id_card'] = EncryptService::maskIdCard($item['id_card']);
            $item['tags'] = $item['tags'] ? json_decode($item['tags'], true) : [];
        }
        
        return [
            'list' => $list,
            'total' => $total,
            'page' => $page,
            'limit' => $limit
        ];
    }
    
    /**
     * 更新客户状态
     */
    public static function updateCustomerStatus($investorId, $customerId, $data, $userId)
    {
        $investorCustomer = InvestorCustomer::where([
            'investor_id' => $investorId,
            'customer_id' => $customerId
        ])->find();
        
        if (!$investorCustomer) {
            throw new \Exception('客户不存在');
        }
        
        $updateData = [
            'last_updated_by' => $userId
        ];
        
        if (isset($data['customer_status'])) {
            $updateData['customer_status'] = $data['customer_status'];
        }
        
        if (isset($data['risk_level'])) {
            $updateData['risk_level'] = $data['risk_level'];
        }
        
        if (isset($data['cooperation_status'])) {
            $updateData['cooperation_status'] = $data['cooperation_status'];
        }
        
        $investorCustomer->save($updateData);
        
        // 记录操作日志
        LogService::record('investor_customer', 'update_status', '更新客户状态', [
            'investor_id' => $investorId,
            'customer_id' => $customerId,
            'changes' => $updateData
        ]);
        
        return $investorCustomer;
    }
    
    /**
     * 添加客户标签
     */
    public static function addCustomerTags($investorId, $customerId, $tags, $userId)
    {
        $investorCustomer = InvestorCustomer::where([
            'investor_id' => $investorId,
            'customer_id' => $customerId
        ])->find();
        
        if (!$investorCustomer) {
            throw new \Exception('客户不存在');
        }
        
        $currentTags = $investorCustomer->tags ? json_decode($investorCustomer->tags, true) : [];
        $newTags = array_unique(array_merge($currentTags, $tags));
        
        $investorCustomer->tags = json_encode($newTags);
        $investorCustomer->last_updated_by = $userId;
        $investorCustomer->save();
        
        return $newTags;
    }
    
    /**
     * 更新客户备注
     */
    public static function updateCustomerNotes($investorId, $customerId, $notes, $userId)
    {
        $investorCustomer = InvestorCustomer::where([
            'investor_id' => $investorId,
            'customer_id' => $customerId
        ])->find();
        
        if (!$investorCustomer) {
            throw new \Exception('客户不存在');
        }
        
        $investorCustomer->notes = $notes;
        $investorCustomer->last_updated_by = $userId;
        $investorCustomer->save();
        
        return true;
    }
    
    /**
     * 获取客户详细信息
     */
    public static function getCustomerDetail($investorId, $customerId)
    {
        $detail = InvestorCustomer::alias('ic')
                                ->join('customers c', 'ic.customer_id = c.id')
                                ->where('ic.investor_id', $investorId)
                                ->where('ic.customer_id', $customerId)
                                ->field([
                                    'ic.*',
                                    'c.name',
                                    'c.phone',
                                    'c.id_card',
                                    'c.gender',
                                    'c.industry',
                                    'c.occupation',
                                    'c.monthly_income',
                                    'c.risk_level as customer_risk_level',
                                    'c.created_at as register_time'
                                ])
                                ->find();
        
        if ($detail) {
            $detail['phone'] = EncryptService::maskPhone($detail['phone']);
            $detail['id_card'] = EncryptService::maskIdCard($detail['id_card']);
            $detail['tags'] = $detail['tags'] ? json_decode($detail['tags'], true) : [];
            
            // 获取该客户的贷款统计
            $detail['loan_statistics'] = self::getCustomerLoanStatistics($customerId);
            
            // 获取查询历史
            $detail['query_history'] = CustomerQueryLog::where('customer_id', $customerId)
                                                     ->with(['investor'])
                                                     ->order('query_time', 'desc')
                                                     ->limit(10)
                                                     ->select();
        }
        
        return $detail;
    }
    
    /**
     * 获取客户贷款统计
     */
    private static function getCustomerLoanStatistics($customerId)
    {
        return [
            'total_applications' => LoanApplication::where('customer_id', $customerId)->count(),
            'approved_count' => LoanApplication::where('customer_id', $customerId)->where('status', 'approved')->count(),
            'total_amount' => LoanApplication::where('customer_id', $customerId)->where('status', 'disbursed')->sum('actual_amount'),
            'overdue_count' => LoanApplication::where('customer_id', $customerId)->where('status', 'overdue')->count(),
            'last_loan_time' => LoanApplication::where('customer_id', $customerId)->max('disburse_time')
        ];
    }
}
```

## 五、小程序前端实现

### 1. 主要页面结构

```javascript
// pages/login/index.vue - 登录页
<template>
  <view class="login-container">
    <view class="logo-section">
      <image src="/static/images/logo.png" class="logo" />
      <text class="title">资方查询平台</text>
    </view>
    
    <view class="login-form">
      <button 
        v-if="!hasPhone" 
        class="wechat-login-btn"
        @tap="handleWechatLogin"
        open-type="getUserInfo"
        @getuserinfo="onGetUserInfo"
      >
        <image src="/static/images/wechat.png" class="wechat-icon" />
        微信授权登录
      </button>
      
      <view v-if="hasUserInfo && !hasPhone" class="phone-form">
        <input 
          type="number" 
          placeholder="请输入手机号" 
          v-model="phone"
          maxlength="11"
        />
        <view class="verify-code-row">
          <input 
            type="number" 
            placeholder="验证码" 
            v-model="verifyCode"
            maxlength="6"
          />
          <button 
            :class="['verify-btn', { disabled: verifyCountdown > 0 }]"
            @tap="sendVerifyCode"
            :disabled="verifyCountdown > 0"
          >
            {{ verifyCountdown > 0 ? `${verifyCountdown}s` : '获取验证码' }}
          </button>
        </view>
        <button class="confirm-btn" @tap="bindPhone">确认绑定</button>
      </view>
      
      <view v-if="hasPhone && !isApproved" class="status-section">
        <view class="status-card">
          <view class="status-icon">⏳</view>
          <text class="status-text">{{ getStatusText() }}</text>
          <button v-if="applicationStatus === 'none'" class="apply-btn" @tap="goToApply">
            立即申请
          </button>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      hasUserInfo: false,
      hasPhone: false,
      isApproved: false,
      phone: '',
      verifyCode: '',
      verifyCountdown: 0,
      applicationStatus: 'none', // none, pending, approved, rejected
      userInfo: {}
    }
  },
  
  onLoad() {
    this.checkLoginStatus();
  },
  
  methods: {
    async handleWechatLogin() {
      try {
        const loginRes = await uni.login();
        this.code = loginRes.code;
      } catch (error) {
        uni.showToast({ title: '登录失败', icon: 'none' });
      }
    },
    
    onGetUserInfo(e) {
      if (e.detail.userInfo) {
        this.userInfo = e.detail.userInfo;
        this.hasUserInfo = true;
        this.saveUserInfo();
      }
    },
    
    async sendVerifyCode() {
      if (!this.phone || this.phone.length !== 11) {
        uni.showToast({ title: '请输入正确的手机号', icon: 'none' });
        return;
      }
      
      try {
        await this.$api.sendVerifyCode({ phone: this.phone });
        this.startCountdown();
        uni.showToast({ title: '验证码已发送', icon: 'success' });
      } catch (error) {
        uni.showToast({ title: error.message, icon: 'none' });
      }
    },
    
    async bindPhone() {
      if (!this.verifyCode) {
        uni.showToast({ title: '请输入验证码', icon: 'none' });
        return;
      }
      
      try {
        const res = await this.$api.bindPhone({
          code: this.code,
          phone: this.phone,
          verify_code: this.verifyCode,
          user_info: this.userInfo
        });
        
        uni.setStorageSync('token', res.data.token);
        uni.setStorageSync('userInfo', res.data.user_info);
        
        this.hasPhone = true;
        this.checkApplicationStatus();
        
      } catch (error) {
        uni.showToast({ title: error.message, icon: 'none' });
      }
    },
    
    async checkApplicationStatus() {
      try {
        const res = await this.$api.getApplicationStatus();
        this.applicationStatus = res.data.status;
        this.isApproved = res.data.status === 'approved';
        
        if (this.isApproved) {
          uni.switchTab({ url: '/pages/query/index' });
        }
      } catch (error) {
        console.error('检查申请状态失败:', error);
      }
    },
    
    getStatusText() {
      const statusMap = {
        'none': '您还未提交入驻申请',
        'pending': '您的入驻申请正在审核中，请耐心等待',
        'rejected': '很抱歉，您的入驻申请未通过审核',
        'approved': '恭喜！您的入驻申请已通过'
      };
      return statusMap[this.applicationStatus] || '';
    },
    
    goToApply() {
      uni.navigateTo({ url: '/pages/apply/index' });
    }
  }
}
</script>
```

```javascript
// pages/query/index.vue - 查询页面
<template>
  <view class="query-container">
    <view class="search-section">
      <view class="search-form">
        <view class="form-item">
          <text class="label">客户姓名</text>
          <input 
            type="text" 
            placeholder="请输入客户姓名"
            v-model="searchForm.customerName"
            maxlength="20"
          />
        </view>
        <view class="form-item">
          <text class="label">身份证后4位</text>
          <input 
            type="number" 
            placeholder="请输入身份证后4位"
            v-model="searchForm.idCardSuffix"
            maxlength="4"
          />
        </view>
        <button 
          class="search-btn" 
          @tap="handleSearch"
          :disabled="!canSearch"
          :class="{ disabled: !canSearch }"
        >
          查询客户档案
        </button>
      </view>
      
      <view class="query-stats">
        <text class="stats-text">本月已查询: {{ queryStats.used }} / {{ queryStats.limit }}</text>
      </view>
    </view>
    
    <!-- 查询结果 -->
    <view v-if="queryResult" class="result-section">
      <view v-if="queryResult.query_result === 'found'" class="customer-found">
        <view class="customer-card">
          <view class="customer-header">
            <text class="customer-name">{{ queryResult.customer_info.name }}</text>
            <view class="risk-badge" :class="queryResult.customer_info.risk_level">
              {{ getRiskLevelText(queryResult.customer_info.risk_level) }}
            </view>
          </view>
          
          <view class="customer-info">
            <view class="info-row">
              <text class="info-label">手机号:</text>
              <text class="info-value">{{ queryResult.customer_info.phone }}</text>
            </view>
            <view class="info-row">
              <text class="info-label">身份证:</text>
              <text class="info-value">{{ queryResult.customer_info.id_card }}</text>
            </view>
            <view class="info-row">
              <text class="info-label">行业:</text>
              <text class="info-value">{{ queryResult.customer_info.industry || '--' }}</text>
            </view>
            <view class="info-row">
              <text class="info-label">注册时间:</text>
              <text class="info-value">{{ formatDate(queryResult.customer_info.register_time) }}</text>
            </view>
          </view>
          
          <view class="customer-actions">
            <button class="action-btn primary" @tap="addToMyCustomers">
              添加到我的客户
            </button>
            <button class="action-btn" @tap="viewQueryHistory">
              查看查询记录
            </button>
          </view>
        </view>
        
        <!-- 查询历史 -->
        <view class="query-history">
          <text class="section-title">查询历史记录</text>
          <view 
            v-for="(record, index) in queryResult.all_queries" 
            :key="index"
            class="history-item"
          >
            <view class="history-header">
              <text class="company-name">{{ record.investor.company_name }}</text>
              <text class="query-time">{{ formatDate(record.query_time) }}</text>
            </view>
            <text class="company-type">{{ getCompanyTypeText(record.investor.company_type) }}</text>
          </view>
        </view>
      </view>
      
      <view v-else-if="queryResult.query_result === 'not_found'" class="not-found">
        <view class="not-found-icon">🔍</view>
        <text class="not-found-text">未找到匹配的客户信息</text>
        <text class="not-found-desc">请检查姓名和身份证号是否正确</text>
      </view>
      
      <view v-else-if="queryResult.query_result === 'multiple'" class="multiple-found">
        <view class="multiple-icon">⚠️</view>
        <text class="multiple-text">找到多个匹配的客户</text>
        <text class="multiple-desc">请联系管理员处理数据重复问题</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      searchForm: {
        customerName: '',
        idCardSuffix: ''
      },
      queryResult: null,
      queryStats: {
        used: 0,
        limit: 100
      },
      loading: false
    }
  },
  
  computed: {
    canSearch() {
      return this.searchForm.customerName.trim() && 
             this.searchForm.idCardSuffix.length === 4 &&
             !this.loading;
    }
  },
  
  onLoad() {
    this.loadQueryStats();
  },
  
  methods: {
    async handleSearch() {
      if (!this.canSearch) return;
      
      uni.showLoading({ title: '查询中...' });
      this.loading = true;
      
      try {
        const res = await this.$api.queryCustomer({
          customer_name: this.searchForm.customerName.trim(),
          id_card_suffix: this.searchForm.idCardSuffix
        });
        
        this.queryResult = res.data;
        this.updateQueryStats();
        
        // 记录查询行为
        this.$analytics.track('customer_query', {
          query_result: res.data.query_result,
          customer_name: this.searchForm.customerName
        });
        
      } catch (error) {
        uni.showToast({ title: error.message, icon: 'none' });
      } finally {
        uni.hideLoading();
        this.loading = false;
      }
    },
    
    async addToMyCustomers() {
      if (!this.queryResult || !this.queryResult.customer_info) return;
      
      try {
        await this.$api.addToMyCustomers({
          customer_id: this.queryResult.customer_info.customer_id
        });
        
        uni.showToast({ title: '已添加到我的客户', icon: 'success' });
        
        // 跳转到我的客户页面
        setTimeout(() => {
          uni.switchTab({ url: '/pages/my-customers/index' });
        }, 1500);
        
      } catch (error) {
        uni.showToast({ title: error.message, icon: 'none' });
      }
    },
    
    viewQueryHistory() {
      if (!this.queryResult) return;
      
      uni.navigateTo({
        url: `/pages/query-history/index?query_id=${this.queryResult.query_id}`
      });
    },
    
    async loadQueryStats() {
      try {
        const res = await this.$api.getQueryStats();
        this.queryStats = res.data;
      } catch (error) {
        console.error('加载查询统计失败:', error);
      }
    },
    
    updateQueryStats() {
      this.queryStats.used += 1;
    },
    
    getRiskLevelText(level) {
      const levelMap = {
        'low': '低风险',
        'medium': '中风险', 
        'high': '高风险',
        'extreme': '极高风险'
      };
      return levelMap[level] || '未知';
    },
    
    getCompanyTypeText(type) {
      const typeMap = {
        'bank': '银行',
        'finance_company': '金融公司',
        'p2p': 'P2P平台',
        'private_lending': '民间借贷',
        'other': '其他'
      };
      return typeMap[type] || '未知';
    },
    
    formatDate(dateString) {
      if (!dateString) return '--';
      const date = new Date(dateString);
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
    }
  }
}
</script>
```

## 六、借款周期优化实现

### 1. 借款申请表单组件

```vue
<!-- components/loan-apply-form.vue -->
<template>
  <view class="loan-apply-form">
    <view class="form-section">
      <text class="section-title">借款信息</text>
      
      <view class="form-item">
        <text class="label">申请金额</text>
        <input 
          type="number" 
          placeholder="请输入申请金额"
          v-model="formData.applyAmount"
        />
        <text class="unit">元</text>
      </view>
      
      <view class="form-item">
        <text class="label">借款期限</text>
        <view class="term-input-group">
          <input 
            type="number" 
            placeholder="期限"
            v-model="formData.applyTerm"
          />
          <picker 
            mode="selector"
            :range="termUnitOptions"
            :range-key="label"
            @change="onTermUnitChange"
          >
            <view class="term-unit-picker">
              {{ currentTermUnit.label }}
              <text class="picker-arrow">▼</text>
            </view>
          </picker>
        </view>
        <view class="term-tips">
          <text class="tips-text">
            实际期限: {{ calculateActualDays() }} 天
          </text>
        </view>
      </view>
      
      <view class="form-item">
        <text class="label">借款用途</text>
        <textarea 
          placeholder="请简要说明借款用途"
          v-model="formData.loanPurpose"
          maxlength="200"
        />
      </view>
      
      <view class="form-item">
        <text class="label">还款方式</text>
        <picker 
          mode="selector"
          :range="repaymentMethods"
          :range-key="label"
          @change="onRepaymentMethodChange"
        >
          <view class="picker-item">
            {{ currentRepaymentMethod.label }}
            <text class="picker-arrow">▼</text>
          </view>
        </picker>
      </view>
    </view>
    
    <!-- 费用计算 -->
    <view class="cost-section">
      <text class="section-title">费用计算</text>
      
      <view class="cost-item">
        <text class="cost-label">借款本金:</text>
        <text class="cost-value">{{ formatMoney(formData.applyAmount) }} 元</text>
      </view>
      
      <view class="cost-item">
        <text class="cost-label">利息费用:</text>
        <text class="cost-value">{{ formatMoney(calculateInterest()) }} 元</text>
      </view>
      
      <view class="cost-item">
        <text class="cost-label">服务费用:</text>
        <text class="cost-value">{{ formatMoney(calculateServiceFee()) }} 元</text>
      </view>
      
      <view class="cost-item total">
        <text class="cost-label">应还总额:</text>
        <text class="cost-value highlight">{{ formatMoney(calculateTotalAmount()) }} 元</text>
      </view>
    </view>
    
    <!-- 还款计划预览 -->
    <view class="repayment-plan-section">
      <text class="section-title">还款计划预览</text>
      
      <view 
        v-for="(plan, index) in repaymentPlan" 
        :key="index"
        class="plan-item"
      >
        <view class="plan-header">
          <text class="period-number">第{{ plan.period }}期</text>
          <text class="due-date">{{ plan.dueDate }}</text>
        </view>
        <view class="plan-detail">
          <text class="amount">应还: {{ formatMoney(plan.amount) }}元</text>
          <text class="breakdown">
            (本金{{ formatMoney(plan.principal) }} + 利息{{ formatMoney(plan.interest) }})
          </text>
        </view>
      </view>
    </view>
    
    <view class="form-actions">
      <button class="submit-btn" @tap="handleSubmit" :disabled="!canSubmit">
        提交申请
      </button>
    </view>
  </view>
</template>

<script>
export default {
  props: {
    customerId: {
      type: Number,
      required: true
    }
  },
  
  data() {
    return {
      formData: {
        applyAmount: '',
        applyTerm: '',
        termUnit: 'day',
        loanPurpose: '',
        repaymentMethod: 'lump_sum'
      },
      
      termUnitOptions: [
        { value: 'day', label: '天' },
        { value: 'week', label: '周' },
        { value: 'month', label: '月' }
      ],
      
      repaymentMethods: [
        { value: 'lump_sum', label: '一次性还款' },
        { value: 'installment', label: '分期还款' },
        { value: 'interest_first', label: '先息后本' }
      ],
      
      interestRates: {
        day: 0.0005,   // 日利率 0.05%
        week: 0.003,   // 周利率 0.3%
        month: 0.012   // 月利率 1.2%
      },
      
      serviceFeeRate: 0.02 // 服务费率 2%
    }
  },
  
  computed: {
    currentTermUnit() {
      return this.termUnitOptions.find(item => item.value === this.formData.termUnit) || this.termUnitOptions[0];
    },
    
    currentRepaymentMethod() {
      return this.repaymentMethods.find(item => item.value === this.formData.repaymentMethod) || this.repaymentMethods[0];
    },
    
    canSubmit() {
      return this.formData.applyAmount && 
             this.formData.applyTerm && 
             this.formData.loanPurpose.trim();
    },
    
    repaymentPlan() {
      if (!this.formData.applyAmount || !this.formData.applyTerm) {
        return [];
      }
      
      return this.generateRepaymentPlan();
    }
  },
  
  methods: {
    onTermUnitChange(e) {
      this.formData.termUnit = this.termUnitOptions[e.detail.value].value;
    },
    
    onRepaymentMethodChange(e) {
      this.formData.repaymentMethod = this.repaymentMethods[e.detail.value].value;
    },
    
    calculateActualDays() {
      if (!this.formData.applyTerm) return 0;
      
      const term = parseInt(this.formData.applyTerm);
      switch (this.formData.termUnit) {
        case 'day':
          return term;
        case 'week':
          return term * 7;
        case 'month':
          return term * 30;
        default:
          return 0;
      }
    },
    
    calculateInterest() {
      if (!this.formData.applyAmount || !this.formData.applyTerm) return 0;
      
      const principal = parseFloat(this.formData.applyAmount);
      const term = parseInt(this.formData.applyTerm);
      const rate = this.interestRates[this.formData.termUnit];
      
      return principal * rate * term;
    },
    
    calculateServiceFee() {
      if (!this.formData.applyAmount) return 0;
      
      const principal = parseFloat(this.formData.applyAmount);
      return principal * this.serviceFeeRate;
    },
    
    calculateTotalAmount() {
      const principal = parseFloat(this.formData.applyAmount) || 0;
      const interest = this.calculateInterest();
      const serviceFee = this.calculateServiceFee();
      
      return principal + interest + serviceFee;
    },
    
    generateRepaymentPlan() {
      const principal = parseFloat(this.formData.applyAmount);
      const totalInterest = this.calculateInterest();
      const actualDays = this.calculateActualDays();
      
      const plan = [];
      
      if (this.formData.repaymentMethod === 'lump_sum') {
        // 一次性还款
        const dueDate = this.addDays(new Date(), actualDays);
        plan.push({
          period: 1,
          dueDate: this.formatDate(dueDate),
          principal: principal,
          interest: totalInterest,
          amount: principal + totalInterest
        });
      } else if (this.formData.repaymentMethod === 'installment') {
        // 分期还款 (按周分期)
        const periods = Math.ceil(actualDays / 7);
        const principalPerPeriod = principal / periods;
        const interestPerPeriod = totalInterest / periods;
        
        for (let i = 1; i <= periods; i++) {
          const dueDate = this.addDays(new Date(), i * 7);
          plan.push({
            period: i,
            dueDate: this.formatDate(dueDate),
            principal: principalPerPeriod,
            interest: interestPerPeriod,
            amount: principalPerPeriod + interestPerPeriod
          });
        }
      } else if (this.formData.repaymentMethod === 'interest_first') {
        // 先息后本
        const periods = Math.ceil(actualDays / 7);
        const interestPerPeriod = totalInterest / periods;
        
        // 前几期只还利息
        for (let i = 1; i < periods; i++) {
          const dueDate = this.addDays(new Date(), i * 7);
          plan.push({
            period: i,
            dueDate: this.formatDate(dueDate),
            principal: 0,
            interest: interestPerPeriod,
            amount: interestPerPeriod
          });
        }
        
        // 最后一期还本金
        const finalDueDate = this.addDays(new Date(), actualDays);
        plan.push({
          period: periods,
          dueDate: this.formatDate(finalDueDate),
          principal: principal,
          interest: interestPerPeriod,
          amount: principal + interestPerPeriod
        });
      }
      
      return plan;
    },
    
    addDays(date, days) {
      const result = new Date(date);
      result.setDate(result.getDate() + days);
      return result;
    },
    
    formatDate(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    },
    
    formatMoney(amount) {
      if (!amount) return '0.00';
      return parseFloat(amount).toFixed(2);
    },
    
    async handleSubmit() {
      if (!this.canSubmit) return;
      
      uni.showLoading({ title: '提交中...' });
      
      try {
        const submitData = {
          customer_id: this.customerId,
          apply_amount: parseFloat(this.formData.applyAmount),
          apply_term: parseInt(this.formData.applyTerm),
          term_unit: this.formData.termUnit,
          actual_term_days: this.calculateActualDays(),
          loan_purpose: this.formData.loanPurpose.trim(),
          repayment_method: this.formData.repaymentMethod,
          interest_amount: this.calculateInterest(),
          service_fee: this.calculateServiceFee(),
          total_amount: this.calculateTotalAmount(),
          repayment_plan: this.repaymentPlan
        };
        
        const res = await this.$api.submitLoanApplication(submitData);
        
        uni.hideLoading();
        uni.showToast({ title: '申请提交成功', icon: 'success' });
        
        // 跳转到申请详情页
        setTimeout(() => {
          uni.navigateTo({
            url: `/pages/loan-detail/index?id=${res.data.application_id}`
          });
        }, 1500);
        
      } catch (error) {
        uni.hideLoading();
        uni.showToast({ title: error.message, icon: 'none' });
      }
    }
  }
}
</script>
```

这个整合方案包含了：

1. **借款周期优化**：支持天、周、月为单位，自动计算实际天数和还款计划
2. **资方查询小程序**：完整的入驻申请、客户查询、标签管理功能
3. **客户档案管理**：查询历史、客户标签、备注管理
4. **完整的前后端实现**：包括数据库设计、API接口、小程序页面

这套方案完全满足您提到的需求，并且与之前的多端配置系统、小程序装修系统形成了完整的解决方案。

<function_calls>
<invoke name="todo_write">
<parameter name="merge">true
