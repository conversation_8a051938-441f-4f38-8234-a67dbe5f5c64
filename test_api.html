<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API测试页面</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .result { margin: 10px 0; padding: 10px; background: #f5f5f5; border-radius: 3px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        button { padding: 8px 16px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #0056b3; }
    </style>
</head>
<body>
    <h1>民间空放贷后管理系统 - API测试</h1>
    
    <div class="test-section">
        <h3>1. 健康检查</h3>
        <button onclick="testHealth()">测试健康检查</button>
        <div id="health-result" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>2. 用户登录</h3>
        <button onclick="testLogin()">测试登录 (admin)</button>
        <div id="login-result" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>3. 获取客户列表</h3>
        <button onclick="testCustomers()">获取客户列表</button>
        <div id="customers-result" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>4. 获取财务统计</h3>
        <button onclick="testStatistics()">获取财务统计</button>
        <div id="statistics-result" class="result"></div>
    </div>

    <script>
        let authToken = '';
        const API_BASE = 'http://localhost:8000/api/v1';
        
        async function makeRequest(url, options = {}) {
            try {
                const response = await fetch(url, {
                    ...options,
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': authToken ? `Bearer ${authToken}` : '',
                        ...options.headers
                    }
                });
                
                const data = await response.json();
                return { success: response.ok, data, status: response.status };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }
        
        function displayResult(elementId, result) {
            const element = document.getElementById(elementId);
            element.className = result.success ? 'result success' : 'result error';
            element.innerHTML = `<pre>${JSON.stringify(result, null, 2)}</pre>`;
        }
        
        async function testHealth() {
            console.log('测试健康检查...');
            const result = await makeRequest(`${API_BASE}/../health`);
            displayResult('health-result', result);
        }
        
        async function testLogin() {
            console.log('测试登录...');
            const result = await makeRequest(`${API_BASE}/auth/login`, {
                method: 'POST',
                body: JSON.stringify({
                    username: 'admin',
                    password: 'password'
                })
            });
            
            if (result.success && result.data.data && result.data.data.token) {
                authToken = result.data.data.token;
                console.log('登录成功，Token:', authToken);
            }
            
            displayResult('login-result', result);
        }
        
        async function testCustomers() {
            if (!authToken) {
                displayResult('customers-result', { success: false, error: '请先登录' });
                return;
            }
            
            console.log('获取客户列表...');
            const result = await makeRequest(`${API_BASE}/customers`);
            displayResult('customers-result', result);
        }
        
        async function testStatistics() {
            if (!authToken) {
                displayResult('statistics-result', { success: false, error: '请先登录' });
                return;
            }
            
            console.log('获取财务统计...');
            const result = await makeRequest(`${API_BASE}/finance/statistics`);
            displayResult('statistics-result', result);
        }
    </script>
</body>
</html>
