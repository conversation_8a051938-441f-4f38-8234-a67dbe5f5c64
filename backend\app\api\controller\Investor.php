<?php
declare (strict_types = 1);

namespace app\api\controller;

use app\common\model\Customer;
use think\Request;
use think\Response;

class Investor
{
    /**
     * 资方客户查询
     */
    public function queryCustomer(Request $request): Response
    {
        $name = (string) $request->param('customer_name', '');
        $idCardLast4 = (string) $request->param('id_card_last4', '');

        if (mb_strlen($name) < 1 || mb_strlen($idCardLast4) !== 4) {
            return json(['code' => 400, 'message' => '参数错误', 'data' => null], 400);
        }

        $customer = Customer::where('name', $name)
            ->whereLike('id_card', "%{$idCardLast4}")
            ->find();

        if (!$customer) {
            return json(['code' => 200, 'message' => '查询成功', 'data' => ['found' => false]]);
        }

        $masked = substr($customer->id_card, 0, 6) . '****' . substr($customer->id_card, -4);

        return json([
            'code' => 200,
            'message' => '查询成功',
            'data' => [
                'found' => true,
                'customer_info' => [
                    'name' => $customer->name,
                    'id_card_masked' => $masked,
                    'risk_tags' => [$customer->getRiskLevel()],
                ]
            ]
        ]);
    }

    /**
     * 我的客户列表（示例：按资方ID过滤，可扩展）
     */
    public function myCustomers(Request $request): Response
    {
        $page = (int) $request->param('page', 1);
        $limit = (int) $request->param('limit', 20);

        $result = Customer::order('created_at', 'desc')->paginate([
            'list_rows' => $limit,
            'page' => $page,
        ]);

        return json([
            'code' => 200,
            'message' => '获取成功',
            'data' => [
                'items' => $result->items(),
                'pagination' => [
                    'page' => $page,
                    'limit' => $limit,
                    'total' => $result->total(),
                    'pages' => $result->lastPage(),
                ]
            ]
        ]);
    }

    /**
     * 更新客户标签
     */
    public function updateCustomerTags(Request $request, int $id): Response
    {
        $customer = Customer::find($id);
        if (!$customer) {
            return json(['code'=>404,'message'=>'客户不存在','data'=>null],404);
        }
        $tags = $request->param('tags', []);
        $remark = (string) $request->param('remark','');
        if (!is_array($tags)) {
            return json(['code'=>400,'message'=>'参数 tags 必须为数组','data'=>null],400);
        }
        $customer->save(['tags' => $tags]);
        return json(['code'=>200,'message'=>'更新成功','data'=>['id'=>$customer->id,'tags'=>$customer->tags,'remark'=>$remark]]);
    }
}


