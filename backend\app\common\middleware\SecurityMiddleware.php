<?php
declare(strict_types=1);

namespace app\common\middleware;

use think\Request;
use think\Response;
use think\facade\Log;
use think\facade\Cache;
use think\facade\Config;

/**
 * 安全中间件
 * 提供全面的安全防护功能
 */
class SecurityMiddleware
{
    /**
     * 安全配置
     */
    private $config = [
        // XSS防护
        'xss_protection' => true,
        'xss_clean_input' => true,
        
        // CSRF防护
        'csrf_protection' => true,
        'csrf_token_name' => '_token',
        'csrf_header_name' => 'X-CSRF-TOKEN',
        
        // SQL注入防护
        'sql_injection_protection' => true,
        'sql_keywords' => ['union', 'select', 'insert', 'update', 'delete', 'drop', 'create', 'alter'],
        
        // 文件上传安全
        'upload_security' => true,
        'allowed_extensions' => ['jpg', 'jpeg', 'png', 'gif', 'pdf', 'doc', 'docx', 'xls', 'xlsx'],
        'max_file_size' => 10485760, // 10MB
        
        // 请求频率限制
        'rate_limiting' => true,
        'rate_limit_requests' => 100,
        'rate_limit_window' => 3600, // 1小时
        
        // IP白名单/黑名单
        'ip_filtering' => true,
        'ip_whitelist' => [],
        'ip_blacklist' => [],
        
        // 安全头设置
        'security_headers' => true,
        'headers' => [
            'X-Frame-Options' => 'DENY',
            'X-Content-Type-Options' => 'nosniff',
            'X-XSS-Protection' => '1; mode=block',
            'Strict-Transport-Security' => 'max-age=31536000; includeSubDomains',
            'Referrer-Policy' => 'strict-origin-when-cross-origin',
            'Content-Security-Policy' => "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self'; frame-ancestors 'none';"
        ]
    ];

    /**
     * 处理请求
     * @param Request $request
     * @param \Closure $next
     * @return Response
     */
    public function handle(Request $request, \Closure $next): Response
    {
        // 加载配置
        $this->loadConfig();
        
        // IP过滤检查
        if (!$this->checkIpFilter($request)) {
            return $this->securityResponse('IP地址被禁止访问', 403);
        }
        
        // 请求频率限制
        if (!$this->checkRateLimit($request)) {
            return $this->securityResponse('请求过于频繁，请稍后再试', 429);
        }
        
        // SQL注入检查
        if (!$this->checkSqlInjection($request)) {
            return $this->securityResponse('检测到恶意请求', 400);
        }
        
        // XSS检查
        if (!$this->checkXss($request)) {
            return $this->securityResponse('检测到XSS攻击', 400);
        }
        
        // CSRF检查
        if (!$this->checkCsrf($request)) {
            return $this->securityResponse('CSRF令牌验证失败', 403);
        }
        
        // 文件上传安全检查
        if (!$this->checkFileUpload($request)) {
            return $this->securityResponse('文件上传安全检查失败', 400);
        }
        
        // 执行下一个中间件
        $response = $next($request);
        
        // 添加安全头
        $this->addSecurityHeaders($response);
        
        return $response;
    }

    /**
     * 加载安全配置
     */
    private function loadConfig(): void
    {
        $config = Config::get('security', []);
        $this->config = array_merge($this->config, $config);
    }

    /**
     * IP过滤检查
     * @param Request $request
     * @return bool
     */
    private function checkIpFilter(Request $request): bool
    {
        if (!$this->config['ip_filtering']) {
            return true;
        }

        $clientIp = $this->getClientIp($request);
        
        // 检查黑名单
        if (!empty($this->config['ip_blacklist'])) {
            foreach ($this->config['ip_blacklist'] as $blockedIp) {
                if ($this->ipMatch($clientIp, $blockedIp)) {
                    $this->logSecurityEvent('IP_BLOCKED', [
                        'ip' => $clientIp,
                        'blocked_ip' => $blockedIp,
                        'url' => $request->url(),
                        'user_agent' => $request->header('user-agent')
                    ]);
                    return false;
                }
            }
        }
        
        // 检查白名单
        if (!empty($this->config['ip_whitelist'])) {
            foreach ($this->config['ip_whitelist'] as $allowedIp) {
                if ($this->ipMatch($clientIp, $allowedIp)) {
                    return true;
                }
            }
            return false;
        }
        
        return true;
    }

    /**
     * 请求频率限制检查
     * @param Request $request
     * @return bool
     */
    private function checkRateLimit(Request $request): bool
    {
        if (!$this->config['rate_limiting']) {
            return true;
        }

        $clientIp = $this->getClientIp($request);
        $cacheKey = "rate_limit:{$clientIp}";
        
        $requests = Cache::get($cacheKey, 0);
        
        if ($requests >= $this->config['rate_limit_requests']) {
            $this->logSecurityEvent('RATE_LIMIT_EXCEEDED', [
                'ip' => $clientIp,
                'requests' => $requests,
                'limit' => $this->config['rate_limit_requests'],
                'url' => $request->url()
            ]);
            return false;
        }
        
        Cache::set($cacheKey, $requests + 1, $this->config['rate_limit_window']);
        
        return true;
    }

    /**
     * SQL注入检查
     * @param Request $request
     * @return bool
     */
    private function checkSqlInjection(Request $request): bool
    {
        if (!$this->config['sql_injection_protection']) {
            return true;
        }

        $data = array_merge($request->get(), $request->post());
        
        foreach ($data as $key => $value) {
            if (is_string($value) && $this->containsSqlKeywords($value)) {
                $this->logSecurityEvent('SQL_INJECTION_ATTEMPT', [
                    'ip' => $this->getClientIp($request),
                    'field' => $key,
                    'value' => $value,
                    'url' => $request->url()
                ]);
                return false;
            }
        }
        
        return true;
    }

    /**
     * XSS检查
     * @param Request $request
     * @return bool
     */
    private function checkXss(Request $request): bool
    {
        if (!$this->config['xss_protection']) {
            return true;
        }

        $data = array_merge($request->get(), $request->post());
        
        foreach ($data as $key => $value) {
            if (is_string($value) && $this->containsXssPatterns($value)) {
                $this->logSecurityEvent('XSS_ATTEMPT', [
                    'ip' => $this->getClientIp($request),
                    'field' => $key,
                    'value' => $value,
                    'url' => $request->url()
                ]);
                return false;
            }
        }
        
        return true;
    }

    /**
     * CSRF检查
     * @param Request $request
     * @return bool
     */
    private function checkCsrf(Request $request): bool
    {
        if (!$this->config['csrf_protection']) {
            return true;
        }

        // 只对POST、PUT、DELETE请求进行CSRF检查
        if (!in_array($request->method(), ['POST', 'PUT', 'DELETE', 'PATCH'])) {
            return true;
        }
        
        // API接口可能使用JWT等其他认证方式，跳过CSRF检查
        if (strpos($request->pathinfo(), '/api/') === 0) {
            return true;
        }
        
        $token = $request->post($this->config['csrf_token_name']) 
                ?: $request->header($this->config['csrf_header_name']);
        
        if (empty($token)) {
            $this->logSecurityEvent('CSRF_TOKEN_MISSING', [
                'ip' => $this->getClientIp($request),
                'url' => $request->url(),
                'method' => $request->method()
            ]);
            return false;
        }
        
        // 验证CSRF令牌
        if (!$this->validateCsrfToken($token)) {
            $this->logSecurityEvent('CSRF_TOKEN_INVALID', [
                'ip' => $this->getClientIp($request),
                'token' => $token,
                'url' => $request->url()
            ]);
            return false;
        }
        
        return true;
    }

    /**
     * 文件上传安全检查
     * @param Request $request
     * @return bool
     */
    private function checkFileUpload(Request $request): bool
    {
        if (!$this->config['upload_security']) {
            return true;
        }

        $files = $request->file();
        
        foreach ($files as $file) {
            if (!$file) continue;
            
            // 检查文件扩展名
            $extension = strtolower($file->getOriginalExtension());
            if (!in_array($extension, $this->config['allowed_extensions'])) {
                $this->logSecurityEvent('INVALID_FILE_EXTENSION', [
                    'ip' => $this->getClientIp($request),
                    'filename' => $file->getOriginalName(),
                    'extension' => $extension,
                    'allowed' => $this->config['allowed_extensions']
                ]);
                return false;
            }
            
            // 检查文件大小
            if ($file->getSize() > $this->config['max_file_size']) {
                $this->logSecurityEvent('FILE_SIZE_EXCEEDED', [
                    'ip' => $this->getClientIp($request),
                    'filename' => $file->getOriginalName(),
                    'size' => $file->getSize(),
                    'max_size' => $this->config['max_file_size']
                ]);
                return false;
            }
            
            // 检查文件内容
            if (!$this->validateFileContent($file)) {
                $this->logSecurityEvent('MALICIOUS_FILE_CONTENT', [
                    'ip' => $this->getClientIp($request),
                    'filename' => $file->getOriginalName(),
                    'mime_type' => $file->getMime()
                ]);
                return false;
            }
        }
        
        return true;
    }

    /**
     * 添加安全头
     * @param Response $response
     */
    private function addSecurityHeaders(Response $response): void
    {
        if (!$this->config['security_headers']) {
            return;
        }

        foreach ($this->config['headers'] as $name => $value) {
            $response->header($name, $value);
        }
    }

    /**
     * 获取客户端IP
     * @param Request $request
     * @return string
     */
    private function getClientIp(Request $request): string
    {
        $headers = [
            'HTTP_CF_CONNECTING_IP',     // Cloudflare
            'HTTP_X_FORWARDED_FOR',      // 代理
            'HTTP_X_FORWARDED',          // 代理
            'HTTP_X_CLUSTER_CLIENT_IP',  // 集群
            'HTTP_FORWARDED_FOR',        // 代理
            'HTTP_FORWARDED',            // 代理
            'REMOTE_ADDR'                // 标准
        ];
        
        foreach ($headers as $header) {
            $ip = $request->server($header);
            if (!empty($ip) && $ip !== 'unknown') {
                // 处理多个IP的情况
                if (strpos($ip, ',') !== false) {
                    $ip = trim(explode(',', $ip)[0]);
                }
                
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }
        
        return $request->ip();
    }

    /**
     * IP匹配检查
     * @param string $ip
     * @param string $pattern
     * @return bool
     */
    private function ipMatch(string $ip, string $pattern): bool
    {
        // 支持CIDR格式
        if (strpos($pattern, '/') !== false) {
            list($subnet, $mask) = explode('/', $pattern);
            return (ip2long($ip) & ~((1 << (32 - $mask)) - 1)) === ip2long($subnet);
        }
        
        // 支持通配符
        if (strpos($pattern, '*') !== false) {
            $pattern = str_replace('*', '.*', $pattern);
            return preg_match('/^' . $pattern . '$/', $ip);
        }
        
        // 精确匹配
        return $ip === $pattern;
    }

    /**
     * 检查是否包含SQL关键词
     * @param string $value
     * @return bool
     */
    private function containsSqlKeywords(string $value): bool
    {
        $value = strtolower($value);
        
        foreach ($this->config['sql_keywords'] as $keyword) {
            if (strpos($value, $keyword) !== false) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * 检查是否包含XSS模式
     * @param string $value
     * @return bool
     */
    private function containsXssPatterns(string $value): bool
    {
        $patterns = [
            '/<script[^>]*>.*?<\/script>/is',
            '/<iframe[^>]*>.*?<\/iframe>/is',
            '/javascript:/i',
            '/vbscript:/i',
            '/onload\s*=/i',
            '/onerror\s*=/i',
            '/onclick\s*=/i',
            '/onmouseover\s*=/i'
        ];
        
        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $value)) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * 验证CSRF令牌
     * @param string $token
     * @return bool
     */
    private function validateCsrfToken(string $token): bool
    {
        // 这里应该实现CSRF令牌验证逻辑
        // 可以使用session存储或JWT等方式
        return !empty($token) && strlen($token) >= 32;
    }

    /**
     * 验证文件内容
     * @param \think\file\UploadedFile $file
     * @return bool
     */
    private function validateFileContent($file): bool
    {
        // 检查文件头
        $handle = fopen($file->getPathname(), 'rb');
        $header = fread($handle, 1024);
        fclose($handle);
        
        // 检查是否包含恶意代码
        $maliciousPatterns = [
            '<?php',
            '<%',
            '<script',
            'eval(',
            'exec(',
            'system(',
            'shell_exec('
        ];
        
        foreach ($maliciousPatterns as $pattern) {
            if (stripos($header, $pattern) !== false) {
                return false;
            }
        }
        
        return true;
    }

    /**
     * 记录安全事件
     * @param string $event
     * @param array $data
     */
    private function logSecurityEvent(string $event, array $data): void
    {
        Log::warning("Security Event: {$event}", $data);
        
        // 可以发送到安全监控系统
        // $this->sendToSecurityMonitor($event, $data);
    }

    /**
     * 安全响应
     * @param string $message
     * @param int $code
     * @return Response
     */
    private function securityResponse(string $message, int $code): Response
    {
        return json([
            'success' => false,
            'code' => $code,
            'message' => $message,
            'timestamp' => time()
        ], $code);
    }
}
