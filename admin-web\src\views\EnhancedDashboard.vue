<template>
  <div class="enhanced-dashboard">
    <!-- 顶部欢迎区域 -->
    <div class="welcome-banner">
      <div class="welcome-content">
        <div class="welcome-text">
          <h1 class="welcome-title">
            {{ getGreeting() }}，{{ userInfo.name || '管理员' }}
          </h1>
          <p class="welcome-subtitle">
            今天是 {{ currentDate }}，系统运行正常
            <el-tag size="small" type="success" class="status-tag">
              <el-icon><CircleCheckFilled /></el-icon>
              在线
            </el-tag>
          </p>
        </div>
        <div class="welcome-actions">
          <el-button-group>
            <el-button type="primary" :icon="Plus" @click="quickAction('new-loan')">
              新增借款
            </el-button>
            <el-button type="success" :icon="Search" @click="quickAction('search')">
              快速查询
            </el-button>
            <el-button type="warning" :icon="Bell" @click="quickAction('notifications')">
              通知中心
              <el-badge :value="notificationCount" :hidden="notificationCount === 0" />
            </el-button>
          </el-button-group>
        </div>
      </div>
    </div>

    <!-- 核心指标面板 -->
    <div class="metrics-panel">
      <el-row :gutter="24">
        <el-col :span="6" v-for="metric in coreMetrics" :key="metric.key">
          <div class="metric-card" :class="[`metric-${metric.type}`, { 'metric-loading': metric.loading }]">
            <div class="metric-header">
              <div class="metric-icon">
                <el-icon size="32">
                  <component :is="metric.icon" />
                </el-icon>
              </div>
              <div class="metric-trend" v-if="!metric.loading">
                <span class="trend-value" :class="metric.trendDirection">
                  {{ metric.trendValue }}
                </span>
                <el-icon size="14" :class="metric.trendDirection">
                  <ArrowUp v-if="metric.trendDirection === 'up'" />
                  <ArrowDown v-else />
                </el-icon>
              </div>
            </div>
            <div class="metric-body">
              <div class="metric-value" v-if="!metric.loading">
                {{ formatNumber(metric.value) }}
              </div>
              <el-skeleton v-else animated>
                <template #template>
                  <el-skeleton-item variant="text" style="width: 60%" />
                </template>
              </el-skeleton>
              <div class="metric-label">{{ metric.label }}</div>
              <div class="metric-description">{{ metric.description }}</div>
            </div>
            <div class="metric-progress" v-if="metric.progress">
              <el-progress 
                :percentage="metric.progress" 
                :stroke-width="4" 
                :show-text="false"
                :color="metric.progressColor"
              />
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 图表分析区域 -->
    <el-row :gutter="24" class="charts-section">
      <el-col :span="16">
        <el-card class="chart-card">
          <template #header>
            <div class="chart-header">
              <div class="chart-title">
                <h3>业务趋势分析</h3>
                <el-tag size="small" type="info">实时更新</el-tag>
              </div>
              <div class="chart-controls">
                <el-radio-group v-model="chartTimeRange" size="small" @change="updateChartData">
                  <el-radio-button label="7d">近7天</el-radio-button>
                  <el-radio-button label="30d">近30天</el-radio-button>
                  <el-radio-button label="90d">近90天</el-radio-button>
                </el-radio-group>
                <el-button size="small" :icon="Refresh" @click="refreshChartData" circle />
              </div>
            </div>
          </template>
          <div class="chart-container" v-loading="chartLoading">
            <div class="chart-wrapper">
              <!-- 这里应该集成图表组件，如ECharts -->
              <div class="chart-placeholder">
                <el-icon size="48" color="#ddd"><TrendCharts /></el-icon>
                <p>业务趋势图表</p>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :span="8">
        <el-card class="chart-card">
          <template #header>
            <h3>风险分布</h3>
          </template>
          <div class="risk-distribution">
            <div class="risk-item" v-for="risk in riskDistribution" :key="risk.level">
              <div class="risk-info">
                <span class="risk-label">{{ risk.label }}</span>
                <span class="risk-count">{{ risk.count }}</span>
              </div>
              <el-progress 
                :percentage="risk.percentage" 
                :color="risk.color"
                :stroke-width="8"
              />
            </div>
          </div>
        </el-card>

        <el-card class="chart-card" style="margin-top: 24px;">
          <template #header>
            <h3>系统性能</h3>
          </template>
          <div class="performance-stats">
            <div class="perf-item" v-for="perf in performanceStats" :key="perf.key">
              <div class="perf-label">{{ perf.label }}</div>
              <div class="perf-value" :class="perf.status">{{ perf.value }}</div>
              <div class="perf-bar">
                <div class="perf-fill" :style="{ width: perf.percentage + '%', backgroundColor: perf.color }"></div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 待办事项和活动日志 -->
    <el-row :gutter="24" class="bottom-section">
      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>待办事项</span>
              <div class="header-actions">
                <el-badge :value="todoList.length" type="primary" />
                <el-button size="small" text @click="refreshTodos">
                  <el-icon><Refresh /></el-icon>
                </el-button>
              </div>
            </div>
          </template>
          <div class="todo-container" v-loading="todoLoading">
            <div class="todo-list">
              <div 
                v-for="todo in todoList" 
                :key="todo.id"
                class="todo-item"
                :class="{ 'todo-urgent': todo.urgent }"
                @click="handleTodoClick(todo)"
              >
                <div class="todo-icon">
                  <el-icon :color="todo.color">
                    <component :is="todo.icon" />
                  </el-icon>
                </div>
                <div class="todo-content">
                  <div class="todo-title">{{ todo.title }}</div>
                  <div class="todo-desc">{{ todo.description }}</div>
                  <div class="todo-meta">
                    <el-tag size="small" :type="todo.priority">{{ todo.priorityText }}</el-tag>
                    <span class="todo-time">{{ formatTime(todo.createdAt) }}</span>
                  </div>
                </div>
                <div class="todo-action">
                  <el-badge :value="todo.count" :type="todo.badgeType" />
                </div>
              </div>
            </div>
            <div class="todo-empty" v-if="todoList.length === 0">
              <el-empty description="暂无待办事项" />
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :span="12">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>系统活动</span>
              <div class="header-actions">
                <el-switch 
                  v-model="autoRefresh" 
                  size="small"
                  active-text="自动刷新"
                  inactive-text=""
                />
                <el-button size="small" text @click="refreshActivities">
                  <el-icon><Refresh /></el-icon>
                </el-button>
              </div>
            </div>
          </template>
          <div class="activity-container" v-loading="activityLoading">
            <el-timeline class="activity-timeline">
              <el-timeline-item
                v-for="activity in activities"
                :key="activity.id"
                :timestamp="formatTime(activity.timestamp)"
                :type="activity.type"
                :icon="activity.icon"
              >
                <div class="activity-content">
                  <div class="activity-title">{{ activity.title }}</div>
                  <div class="activity-desc">{{ activity.description }}</div>
                  <div class="activity-meta">
                    <el-tag size="small" :type="activity.tagType">{{ activity.module }}</el-tag>
                    <span class="activity-user">{{ activity.user }}</span>
                  </div>
                </div>
              </el-timeline-item>
            </el-timeline>
            <div class="activity-empty" v-if="activities.length === 0">
              <el-empty description="暂无系统活动" />
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 快速操作浮动按钮 -->
    <el-backtop :right="100" :bottom="100">
      <div class="backtop-content">
        <el-icon><CaretTop /></el-icon>
      </div>
    </el-backtop>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElNotification } from 'element-plus'
import {
  Plus, Search, Bell, Refresh, ArrowUp, ArrowDown,
  CircleCheckFilled, TrendCharts, CaretTop
} from '@element-plus/icons-vue'

const router = useRouter()

// 响应式数据
const currentDate = ref('')
const chartTimeRange = ref('30d')
const chartLoading = ref(false)
const todoLoading = ref(false)
const activityLoading = ref(false)
const autoRefresh = ref(true)
const notificationCount = ref(5)

// 用户信息
const userInfo = reactive({
  name: '系统管理员',
  avatar: '',
  role: 'admin'
})

// 核心指标数据
const coreMetrics = ref([
  {
    key: 'total_loans',
    label: '总借款笔数',
    value: 1234,
    icon: 'DocumentCopy',
    type: 'primary',
    trendDirection: 'up',
    trendValue: '+12.5%',
    description: '较上月增长',
    progress: 75,
    progressColor: '#409eff',
    loading: false
  },
  {
    key: 'total_amount',
    label: '放款总金额',
    value: 5678900,
    icon: 'Money',
    type: 'success',
    trendDirection: 'up',
    trendValue: '+8.3%',
    description: '较上月增长',
    progress: 82,
    progressColor: '#67c23a',
    loading: false
  },
  {
    key: 'overdue_rate',
    label: '逾期率',
    value: 2.3,
    icon: 'Warning',
    type: 'warning',
    trendDirection: 'down',
    trendValue: '-0.5%',
    description: '较上月下降',
    progress: 23,
    progressColor: '#e6a23c',
    loading: false
  },
  {
    key: 'risk_score',
    label: '风险评分',
    value: 85.6,
    icon: 'Shield',
    type: 'info',
    trendDirection: 'up',
    trendValue: '+2.1%',
    description: '系统健康度',
    progress: 86,
    progressColor: '#909399',
    loading: false
  }
])

// 风险分布数据
const riskDistribution = ref([
  { level: 'low', label: '低风险', count: 856, percentage: 68, color: '#67c23a' },
  { level: 'medium', label: '中风险', count: 234, percentage: 19, color: '#e6a23c' },
  { level: 'high', label: '高风险', count: 123, percentage: 10, color: '#f56c6c' },
  { level: 'very_high', label: '极高风险', count: 37, percentage: 3, color: '#f56c6c' }
])

// 性能统计数据
const performanceStats = ref([
  { key: 'cpu', label: 'CPU使用率', value: '45%', percentage: 45, color: '#409eff', status: 'normal' },
  { key: 'memory', label: '内存使用率', value: '62%', percentage: 62, color: '#67c23a', status: 'normal' },
  { key: 'disk', label: '磁盘使用率', value: '78%', percentage: 78, color: '#e6a23c', status: 'warning' },
  { key: 'network', label: '网络延迟', value: '23ms', percentage: 15, color: '#67c23a', status: 'good' }
])

// 待办事项数据
const todoList = ref([
  {
    id: 1,
    title: '待审核借款申请',
    description: '有15笔借款申请等待审核',
    count: 15,
    urgent: true,
    priority: 'danger',
    priorityText: '紧急',
    badgeType: 'danger',
    icon: 'DocumentChecked',
    color: '#f56c6c',
    createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000)
  },
  {
    id: 2,
    title: '逾期客户跟进',
    description: '8位客户逾期需要跟进处理',
    count: 8,
    urgent: false,
    priority: 'warning',
    priorityText: '重要',
    badgeType: 'warning',
    icon: 'Clock',
    color: '#e6a23c',
    createdAt: new Date(Date.now() - 4 * 60 * 60 * 1000)
  },
  {
    id: 3,
    title: '系统维护提醒',
    description: '定期系统维护计划',
    count: 1,
    urgent: false,
    priority: 'info',
    priorityText: '一般',
    badgeType: 'info',
    icon: 'Tools',
    color: '#909399',
    createdAt: new Date(Date.now() - 6 * 60 * 60 * 1000)
  }
])

// 活动日志数据
const activities = ref([
  {
    id: 1,
    title: '新增借款申请',
    description: '客户张三提交了借款申请，金额50000元',
    timestamp: new Date(Date.now() - 30 * 60 * 1000),
    type: 'primary',
    icon: 'Plus',
    module: '借款管理',
    tagType: 'primary',
    user: '张三'
  },
  {
    id: 2,
    title: '风控审核完成',
    description: 'AI风控系统完成了客户李四的风险评估',
    timestamp: new Date(Date.now() - 60 * 60 * 1000),
    type: 'success',
    icon: 'CircleCheck',
    module: '风控管理',
    tagType: 'success',
    user: '系统'
  },
  {
    id: 3,
    title: '还款提醒发送',
    description: '系统向100位客户发送了还款提醒',
    timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
    type: 'info',
    icon: 'Bell',
    module: '还款管理',
    tagType: 'info',
    user: '系统'
  }
])

// 计算属性
const greeting = computed(() => {
  const hour = new Date().getHours()
  if (hour < 6) return '夜深了'
  if (hour < 9) return '早上好'
  if (hour < 12) return '上午好'
  if (hour < 14) return '中午好'
  if (hour < 18) return '下午好'
  if (hour < 22) return '晚上好'
  return '夜深了'
})

// 方法
const getGreeting = () => greeting.value

const formatNumber = (num: number) => {
  if (num >= 10000) {
    return (num / 10000).toFixed(1) + '万'
  }
  return num.toLocaleString()
}

const formatTime = (date: Date) => {
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  const minutes = Math.floor(diff / 60000)
  const hours = Math.floor(diff / 3600000)
  const days = Math.floor(diff / 86400000)
  
  if (minutes < 1) return '刚刚'
  if (minutes < 60) return `${minutes}分钟前`
  if (hours < 24) return `${hours}小时前`
  if (days < 7) return `${days}天前`
  return date.toLocaleDateString()
}

const quickAction = (action: string) => {
  switch (action) {
    case 'new-loan':
      router.push('/loans/create')
      break
    case 'search':
      router.push('/customers')
      break
    case 'notifications':
      ElNotification({
        title: '通知中心',
        message: '您有5条未读通知',
        type: 'info'
      })
      break
  }
}

const updateChartData = () => {
  chartLoading.value = true
  setTimeout(() => {
    chartLoading.value = false
    ElMessage.success('图表数据已更新')
  }, 1000)
}

const refreshChartData = () => {
  updateChartData()
}

const refreshTodos = () => {
  todoLoading.value = true
  setTimeout(() => {
    todoLoading.value = false
    ElMessage.success('待办事项已刷新')
  }, 500)
}

const refreshActivities = () => {
  activityLoading.value = true
  setTimeout(() => {
    activityLoading.value = false
    ElMessage.success('活动日志已刷新')
  }, 500)
}

const handleTodoClick = (todo: any) => {
  ElMessage.info(`处理待办事项: ${todo.title}`)
}

// 生命周期
onMounted(() => {
  currentDate.value = new Date().toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    weekday: 'long'
  })

  // 自动刷新
  const interval = setInterval(() => {
    if (autoRefresh.value) {
      // 这里可以添加自动刷新逻辑
    }
  }, 30000)

  onUnmounted(() => {
    clearInterval(interval)
  })
})
</script>

<style scoped>
.enhanced-dashboard {
  padding: 24px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 60px);
}

/* 欢迎横幅 */
.welcome-banner {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16px;
  padding: 32px;
  margin-bottom: 24px;
  color: white;
  position: relative;
  overflow: hidden;
}

.welcome-banner::before {
  content: '';
  position: absolute;
  top: -50%;
  right: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
  animation: float 6s ease-in-out infinite;
}

.welcome-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  z-index: 1;
}

.welcome-title {
  font-size: 28px;
  font-weight: 600;
  margin: 0 0 8px 0;
}

.welcome-subtitle {
  font-size: 16px;
  opacity: 0.9;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 12px;
}

.status-tag {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

/* 指标面板 */
.metrics-panel {
  margin-bottom: 24px;
}

.metric-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.metric-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.metric-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: var(--metric-color, #409eff);
}

.metric-card.metric-primary::before { background: #409eff; }
.metric-card.metric-success::before { background: #67c23a; }
.metric-card.metric-warning::before { background: #e6a23c; }
.metric-card.metric-info::before { background: #909399; }

.metric-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.metric-icon {
  width: 56px;
  height: 56px;
  border-radius: 12px;
  background: linear-gradient(135deg, #409eff, #667eea);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.metric-trend {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 14px;
  font-weight: 600;
}

.trend-value.up { color: #67c23a; }
.trend-value.down { color: #f56c6c; }

.metric-value {
  font-size: 32px;
  font-weight: 700;
  color: #303133;
  margin-bottom: 8px;
}

.metric-label {
  font-size: 16px;
  color: #606266;
  margin-bottom: 4px;
}

.metric-description {
  font-size: 12px;
  color: #909399;
}

.metric-progress {
  margin-top: 16px;
}

/* 图表区域 */
.charts-section {
  margin-bottom: 24px;
}

.chart-card {
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  border-radius: 12px;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chart-title {
  display: flex;
  align-items: center;
  gap: 12px;
}

.chart-title h3 {
  margin: 0;
  font-size: 18px;
  color: #303133;
}

.chart-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

.chart-container {
  padding: 20px 0;
}

.chart-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  color: #909399;
}

.chart-placeholder p {
  margin: 12px 0 0 0;
  font-size: 14px;
}

/* 风险分布 */
.risk-distribution {
  padding: 20px 0;
}

.risk-item {
  margin-bottom: 20px;
}

.risk-item:last-child {
  margin-bottom: 0;
}

.risk-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.risk-label {
  font-size: 14px;
  color: #606266;
}

.risk-count {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

/* 性能统计 */
.performance-stats {
  padding: 20px 0;
}

.perf-item {
  margin-bottom: 20px;
}

.perf-item:last-child {
  margin-bottom: 0;
}

.perf-label {
  font-size: 14px;
  color: #606266;
  margin-bottom: 8px;
}

.perf-value {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 8px;
}

.perf-value.normal { color: #67c23a; }
.perf-value.warning { color: #e6a23c; }
.perf-value.danger { color: #f56c6c; }
.perf-value.good { color: #67c23a; }

.perf-bar {
  height: 6px;
  background: #f5f7fa;
  border-radius: 3px;
  overflow: hidden;
}

.perf-fill {
  height: 100%;
  border-radius: 3px;
  transition: width 0.3s ease;
}

/* 底部区域 */
.bottom-section {
  margin-bottom: 24px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

/* 待办事项 */
.todo-container {
  max-height: 400px;
  overflow-y: auto;
}

.todo-list {
  padding: 0;
}

.todo-item {
  display: flex;
  align-items: center;
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 12px;
  background: #fafbfc;
  border: 1px solid #e4e7ed;
  cursor: pointer;
  transition: all 0.3s ease;
}

.todo-item:hover {
  background: #f0f9ff;
  border-color: #409eff;
  transform: translateX(4px);
}

.todo-item.todo-urgent {
  border-left: 4px solid #f56c6c;
  background: #fef0f0;
}

.todo-icon {
  margin-right: 16px;
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background: #f5f7fa;
  display: flex;
  align-items: center;
  justify-content: center;
}

.todo-content {
  flex: 1;
}

.todo-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.todo-desc {
  font-size: 14px;
  color: #606266;
  margin-bottom: 8px;
}

.todo-meta {
  display: flex;
  align-items: center;
  gap: 12px;
}

.todo-time {
  font-size: 12px;
  color: #909399;
}

/* 活动日志 */
.activity-container {
  max-height: 400px;
  overflow-y: auto;
}

.activity-timeline {
  padding: 0;
}

.activity-content {
  padding-left: 0;
}

.activity-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.activity-desc {
  font-size: 14px;
  color: #606266;
  margin-bottom: 8px;
}

.activity-meta {
  display: flex;
  align-items: center;
  gap: 12px;
}

.activity-user {
  font-size: 12px;
  color: #909399;
}

/* 返回顶部 */
.backtop-content {
  width: 40px;
  height: 40px;
  background: #409eff;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
}

/* 动画 */
@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-10px) rotate(180deg); }
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .enhanced-dashboard {
    padding: 16px;
  }

  .welcome-content {
    flex-direction: column;
    gap: 20px;
    text-align: center;
  }

  .metric-card {
    margin-bottom: 16px;
  }
}

@media (max-width: 768px) {
  .welcome-banner {
    padding: 20px;
  }

  .welcome-title {
    font-size: 24px;
  }

  .metric-value {
    font-size: 24px;
  }

  .chart-header {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }

  .todo-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
}
</style>
