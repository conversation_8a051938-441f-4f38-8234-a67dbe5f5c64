<?php
declare (strict_types = 1);

namespace app\common\model;

use think\Model;

class AppConfig extends Model
{
    protected $name = 'app_configs';

    protected $schema = [
        'id'          => 'int',
        'platform'    => 'string',
        'app_type'    => 'string',
        'config_key'  => 'string',
        'config_value'=> 'string',
        'config_type' => 'string',
        'description' => 'string',
        'is_active'   => 'int',
        'created_at'  => 'datetime',
        'updated_at'  => 'datetime',
    ];
}

// 简易的 updateOrCreate 静态方法（ThinkORM 无原生方法时的替代）
namespace app\common\model;

use think\db\exception\DataNotFoundException;
use think\db\exception\ModelNotFoundException;
use think\Exception;

class AppConfigHelper
{
    public static function updateOrCreate(array $where, array $data)
    {
        /** @var AppConfig $model */
        $model = AppConfig::where($where)->find();
        if ($model) {
            $model->save($data);
            return $model;
        }
        return AppConfig::create(array_merge($where, $data));
    }
}


