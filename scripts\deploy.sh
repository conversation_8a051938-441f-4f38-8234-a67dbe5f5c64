#!/bin/bash

# 部署脚本
# 民间空放贷后管理系统 v2.0

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置参数
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
DEPLOY_ENV="${1:-production}"
BACKUP_DIR="/var/backups/iapp"
LOG_FILE="/var/log/iapp-deploy.log"

# 日志函数
log() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" | tee -a "$LOG_FILE"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$LOG_FILE"
}

# 错误处理函数
error_exit() {
    error "$1"
    exit 1
}

# 显示帮助信息
show_help() {
    echo "部署脚本 - 民间空放贷后管理系统 v2.0"
    echo ""
    echo "用法: $0 [环境] [选项]"
    echo ""
    echo "环境:"
    echo "  production   生产环境 (默认)"
    echo "  staging      测试环境"
    echo "  development  开发环境"
    echo ""
    echo "选项:"
    echo "  --backup     部署前创建备份"
    echo "  --no-build   跳过构建步骤"
    echo "  --no-migrate 跳过数据库迁移"
    echo "  --rollback   回滚到上一个版本"
    echo "  --help       显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 production --backup"
    echo "  $0 staging --no-build"
    echo "  $0 --rollback"
    echo ""
}

# 检查环境
check_environment() {
    log "检查部署环境..."
    
    # 检查Docker
    if ! command -v docker &> /dev/null; then
        error_exit "Docker未安装"
    fi
    
    # 检查Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        error_exit "Docker Compose未安装"
    fi
    
    # 检查Git
    if ! command -v git &> /dev/null; then
        error_exit "Git未安装"
    fi
    
    # 检查项目目录
    if [ ! -f "$PROJECT_ROOT/docker-compose.yml" ]; then
        error_exit "项目配置文件不存在"
    fi
    
    success "环境检查通过"
}

# 创建备份
create_backup() {
    log "创建系统备份..."
    
    local backup_timestamp=$(date +%Y%m%d_%H%M%S)
    local backup_path="$BACKUP_DIR/backup_$backup_timestamp"
    
    mkdir -p "$backup_path"
    
    # 备份数据库
    log "备份数据库..."
    docker-compose exec -T mysql mysqldump -u root -proot_password iapp > "$backup_path/database.sql"
    
    # 备份上传文件
    log "备份上传文件..."
    if [ -d "$PROJECT_ROOT/backend/uploads" ]; then
        cp -r "$PROJECT_ROOT/backend/uploads" "$backup_path/"
    fi
    
    # 备份配置文件
    log "备份配置文件..."
    cp "$PROJECT_ROOT/.env" "$backup_path/" 2>/dev/null || true
    cp "$PROJECT_ROOT/docker-compose.yml" "$backup_path/"
    
    # 创建备份信息文件
    cat > "$backup_path/backup_info.txt" << EOF
备份时间: $(date)
环境: $DEPLOY_ENV
Git提交: $(git rev-parse HEAD)
Git分支: $(git branch --show-current)
备份路径: $backup_path
EOF
    
    # 压缩备份
    cd "$BACKUP_DIR"
    tar -czf "backup_$backup_timestamp.tar.gz" "backup_$backup_timestamp"
    rm -rf "backup_$backup_timestamp"
    
    success "备份创建完成: $BACKUP_DIR/backup_$backup_timestamp.tar.gz"
    
    # 清理旧备份（保留最近10个）
    ls -t "$BACKUP_DIR"/backup_*.tar.gz | tail -n +11 | xargs -r rm -f
}

# 构建应用
build_application() {
    log "构建应用..."
    
    cd "$PROJECT_ROOT"
    
    # 拉取最新代码
    log "拉取最新代码..."
    git fetch origin
    
    case $DEPLOY_ENV in
        production)
            git checkout main
            git pull origin main
            ;;
        staging)
            git checkout develop
            git pull origin develop
            ;;
        development)
            git checkout develop
            git pull origin develop
            ;;
    esac
    
    # 构建Docker镜像
    log "构建Docker镜像..."
    docker-compose build --no-cache
    
    success "应用构建完成"
}

# 部署应用
deploy_application() {
    log "部署应用..."
    
    cd "$PROJECT_ROOT"
    
    # 停止旧容器
    log "停止旧容器..."
    docker-compose down --remove-orphans
    
    # 启动新容器
    log "启动新容器..."
    docker-compose up -d
    
    # 等待服务启动
    log "等待服务启动..."
    sleep 30
    
    # 检查服务状态
    check_services
    
    success "应用部署完成"
}

# 运行数据库迁移
run_migrations() {
    log "运行数据库迁移..."
    
    # 等待数据库就绪
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if docker-compose exec -T app php think migrate:status > /dev/null 2>&1; then
            break
        fi
        
        log "等待数据库就绪... ($attempt/$max_attempts)"
        sleep 2
        attempt=$((attempt + 1))
    done
    
    if [ $attempt -gt $max_attempts ]; then
        error_exit "数据库连接超时"
    fi
    
    # 运行迁移
    docker-compose exec -T app php think migrate:run
    
    success "数据库迁移完成"
}

# 检查服务状态
check_services() {
    log "检查服务状态..."
    
    local services=("app" "mysql" "redis")
    local failed_services=()
    
    for service in "${services[@]}"; do
        if ! docker-compose ps "$service" | grep -q "Up"; then
            failed_services+=("$service")
        fi
    done
    
    if [ ${#failed_services[@]} -gt 0 ]; then
        error "以下服务启动失败: ${failed_services[*]}"
        
        # 显示服务日志
        for service in "${failed_services[@]}"; do
            error "=== $service 服务日志 ==="
            docker-compose logs --tail=50 "$service"
        done
        
        error_exit "服务启动失败"
    fi
    
    # 健康检查
    log "执行健康检查..."
    local health_check_url="http://localhost/health"
    local max_attempts=10
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -f "$health_check_url" > /dev/null 2>&1; then
            success "健康检查通过"
            return 0
        fi
        
        log "健康检查失败，重试中... ($attempt/$max_attempts)"
        sleep 5
        attempt=$((attempt + 1))
    done
    
    warning "健康检查失败，但服务可能仍在启动中"
}

# 回滚部署
rollback_deployment() {
    log "回滚部署..."
    
    # 查找最新的备份
    local latest_backup=$(ls -t "$BACKUP_DIR"/backup_*.tar.gz | head -n 1)
    
    if [ -z "$latest_backup" ]; then
        error_exit "未找到备份文件"
    fi
    
    log "使用备份文件: $latest_backup"
    
    # 解压备份
    local backup_dir=$(mktemp -d)
    tar -xzf "$latest_backup" -C "$backup_dir"
    local backup_content=$(ls "$backup_dir")
    
    # 停止当前服务
    docker-compose down
    
    # 恢复数据库
    log "恢复数据库..."
    docker-compose up -d mysql
    sleep 10
    docker-compose exec -T mysql mysql -u root -proot_password iapp < "$backup_dir/$backup_content/database.sql"
    
    # 恢复上传文件
    log "恢复上传文件..."
    if [ -d "$backup_dir/$backup_content/uploads" ]; then
        rm -rf "$PROJECT_ROOT/backend/uploads"
        cp -r "$backup_dir/$backup_content/uploads" "$PROJECT_ROOT/backend/"
    fi
    
    # 恢复配置文件
    if [ -f "$backup_dir/$backup_content/.env" ]; then
        cp "$backup_dir/$backup_content/.env" "$PROJECT_ROOT/"
    fi
    
    # 启动服务
    docker-compose up -d
    
    # 清理临时文件
    rm -rf "$backup_dir"
    
    success "回滚完成"
}

# 清理资源
cleanup() {
    log "清理资源..."
    
    # 清理未使用的Docker镜像
    docker image prune -f
    
    # 清理未使用的Docker卷
    docker volume prune -f
    
    # 清理未使用的Docker网络
    docker network prune -f
    
    success "资源清理完成"
}

# 发送通知
send_notification() {
    local status="$1"
    local message="$2"
    
    # 这里可以集成钉钉、企业微信、邮件等通知方式
    log "部署通知: $status - $message"
    
    # 示例：发送到钉钉机器人
    # curl -X POST "https://oapi.dingtalk.com/robot/send?access_token=YOUR_TOKEN" \
    #      -H 'Content-Type: application/json' \
    #      -d "{\"msgtype\": \"text\", \"text\": {\"content\": \"$message\"}}"
}

# 主函数
main() {
    local backup=false
    local no_build=false
    local no_migrate=false
    local rollback=false
    
    # 解析参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            --backup)
                backup=true
                shift
                ;;
            --no-build)
                no_build=true
                shift
                ;;
            --no-migrate)
                no_migrate=true
                shift
                ;;
            --rollback)
                rollback=true
                shift
                ;;
            --help|-h)
                show_help
                exit 0
                ;;
            production|staging|development)
                DEPLOY_ENV="$1"
                shift
                ;;
            *)
                error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 创建日志目录
    mkdir -p "$(dirname "$LOG_FILE")"
    
    # 显示开始信息
    echo ""
    echo "========================================"
    echo "  民间空放贷后管理系统 - 部署脚本"
    echo "========================================"
    echo "环境: $DEPLOY_ENV"
    echo "时间: $(date)"
    echo "========================================"
    echo ""
    
    # 检查环境
    check_environment
    
    if [ "$rollback" = true ]; then
        # 回滚部署
        rollback_deployment
        send_notification "SUCCESS" "系统回滚完成 - 环境: $DEPLOY_ENV"
    else
        # 正常部署流程
        if [ "$backup" = true ]; then
            create_backup
        fi
        
        if [ "$no_build" = false ]; then
            build_application
        fi
        
        deploy_application
        
        if [ "$no_migrate" = false ]; then
            run_migrations
        fi
        
        cleanup
        
        send_notification "SUCCESS" "系统部署完成 - 环境: $DEPLOY_ENV"
    fi
    
    # 显示结果
    echo ""
    echo "========================================"
    success "部署完成！"
    echo "环境: $DEPLOY_ENV"
    echo "时间: $(date)"
    echo "日志: $LOG_FILE"
    echo "========================================"
    echo ""
}

# 执行主函数
main "$@"
