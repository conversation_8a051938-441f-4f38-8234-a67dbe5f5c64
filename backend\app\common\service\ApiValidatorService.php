<?php
declare(strict_types=1);

namespace app\common\service;

use think\exception\ValidateException;
use think\facade\Validate;

/**
 * API验证器服务
 * 提供统一的API参数验证功能
 */
class ApiValidatorService
{
    /**
     * 验证规则定义
     */
    const VALIDATION_RULES = [
        // 认证相关验证规则
        'auth.login' => [
            'username' => 'require|length:2,50',
            'password' => 'require|length:6,50',
            'remember' => 'boolean'
        ],
        
        // 客户管理验证规则
        'customer.create' => [
            'name' => 'require|length:2,50|chs',
            'phone' => 'require|mobile',
            'id_card' => 'require|idCard',
            'gender' => 'in:male,female,unknown',
            'birth_date' => 'date',
            'education' => 'in:primary,middle,high,college,university,graduate',
            'marital_status' => 'in:single,married,divorced,widowed',
            'industry' => 'length:0,100',
            'occupation' => 'length:0,100',
            'monthly_income' => 'number|egt:0',
            'residence_address' => 'length:0,500',
            'work_address' => 'length:0,500',
            'emergency_contact_name' => 'length:0,50',
            'emergency_contact_phone' => 'mobile',
            'emergency_contact_relation' => 'length:0,50'
        ],
        
        'customer.update' => [
            'name' => 'length:2,50|chs',
            'phone' => 'mobile',
            'gender' => 'in:male,female,unknown',
            'birth_date' => 'date',
            'education' => 'in:primary,middle,high,college,university,graduate',
            'marital_status' => 'in:single,married,divorced,widowed',
            'industry' => 'length:0,100',
            'occupation' => 'length:0,100',
            'monthly_income' => 'number|egt:0',
            'residence_address' => 'length:0,500',
            'work_address' => 'length:0,500',
            'emergency_contact_name' => 'length:0,50',
            'emergency_contact_phone' => 'mobile',
            'emergency_contact_relation' => 'length:0,50'
        ],
        
        // 借款申请验证规则
        'loan.create' => [
            'customer_id' => 'require|integer|gt:0',
            'amount' => 'require|number|between:1000,200000',
            'cycle' => 'require|integer|between:7,365',
            'interest_rate' => 'number|between:0.01,0.36',
            'repayment_method' => 'in:equal_installment,equal_principal,interest_first,bullet',
            'purpose' => 'require|length:1,500',
            'collateral_info' => 'length:0,1000'
        ],
        
        'loan.approve' => [
            'application_id' => 'require|integer|gt:0',
            'decision' => 'require|in:approve,reject',
            'approved_amount' => 'number|between:1000,200000',
            'approved_rate' => 'number|between:0.01,0.36',
            'review_notes' => 'length:0,1000',
            'conditions' => 'length:0,1000'
        ],
        
        // 风险评估验证规则
        'risk.assessment' => [
            'customer_id' => 'require|integer|gt:0',
            'application_id' => 'integer|gt:0',
            'assessment_type' => 'in:manual,ai,rule,model',
            'force_refresh' => 'boolean'
        ],
        
        // 分页查询验证规则
        'common.pagination' => [
            'page' => 'integer|egt:1',
            'limit' => 'integer|between:1,100',
            'sort' => 'alphaNum',
            'order' => 'in:asc,desc'
        ],
        
        // 搜索验证规则
        'common.search' => [
            'keyword' => 'length:0,100',
            'status' => 'alphaNum',
            'start_date' => 'date',
            'end_date' => 'date|after:start_date'
        ]
    ];

    /**
     * 验证消息定义
     */
    const VALIDATION_MESSAGES = [
        'require' => ':attribute不能为空',
        'length' => ':attribute长度必须在:1到:2之间',
        'between' => ':attribute必须在:1到:2之间',
        'number' => ':attribute必须是数字',
        'integer' => ':attribute必须是整数',
        'boolean' => ':attribute必须是布尔值',
        'mobile' => ':attribute格式不正确',
        'idCard' => ':attribute格式不正确',
        'date' => ':attribute必须是有效日期',
        'after' => ':attribute必须在:1之后',
        'in' => ':attribute必须在:1范围内',
        'gt' => ':attribute必须大于:1',
        'egt' => ':attribute必须大于等于:1',
        'chs' => ':attribute只能是中文字符',
        'alphaNum' => ':attribute只能是字母和数字'
    ];

    /**
     * 字段名称映射
     */
    const FIELD_NAMES = [
        'username' => '用户名',
        'password' => '密码',
        'remember' => '记住登录',
        'name' => '姓名',
        'phone' => '手机号',
        'id_card' => '身份证号',
        'gender' => '性别',
        'birth_date' => '出生日期',
        'education' => '学历',
        'marital_status' => '婚姻状况',
        'industry' => '行业',
        'occupation' => '职业',
        'monthly_income' => '月收入',
        'residence_address' => '居住地址',
        'work_address' => '工作地址',
        'emergency_contact_name' => '紧急联系人',
        'emergency_contact_phone' => '紧急联系人电话',
        'emergency_contact_relation' => '紧急联系人关系',
        'customer_id' => '客户ID',
        'amount' => '金额',
        'cycle' => '周期',
        'interest_rate' => '利率',
        'repayment_method' => '还款方式',
        'purpose' => '借款用途',
        'collateral_info' => '抵押信息',
        'application_id' => '申请ID',
        'decision' => '审批决定',
        'approved_amount' => '批准金额',
        'approved_rate' => '批准利率',
        'review_notes' => '审核备注',
        'conditions' => '放款条件',
        'assessment_type' => '评估类型',
        'force_refresh' => '强制刷新',
        'page' => '页码',
        'limit' => '每页数量',
        'sort' => '排序字段',
        'order' => '排序方向',
        'keyword' => '关键词',
        'status' => '状态',
        'start_date' => '开始日期',
        'end_date' => '结束日期'
    ];

    /**
     * 验证数据
     * @param array $data 待验证数据
     * @param string $scene 验证场景
     * @param array $customRules 自定义规则
     * @param array $customMessages 自定义消息
     * @return array 验证后的数据
     * @throws ValidateException
     */
    public static function validate(
        array $data, 
        string $scene, 
        array $customRules = [], 
        array $customMessages = []
    ): array {
        // 获取验证规则
        $rules = self::VALIDATION_RULES[$scene] ?? [];
        if (!empty($customRules)) {
            $rules = array_merge($rules, $customRules);
        }

        if (empty($rules)) {
            return $data;
        }

        // 获取验证消息
        $messages = array_merge(self::VALIDATION_MESSAGES, $customMessages);

        // 创建验证器
        $validate = Validate::rule($rules)
            ->message($messages)
            ->field(self::FIELD_NAMES);

        // 执行验证
        if (!$validate->check($data)) {
            throw new ValidateException($validate->getError());
        }

        // 返回过滤后的数据（只返回规则中定义的字段）
        return array_intersect_key($data, $rules);
    }

    /**
     * 验证分页参数
     * @param array $params 分页参数
     * @return array 验证后的分页参数
     */
    public static function validatePagination(array $params): array
    {
        $validated = self::validate($params, 'common.pagination');
        
        return [
            'page' => $validated['page'] ?? 1,
            'limit' => $validated['limit'] ?? 20,
            'sort' => $validated['sort'] ?? 'id',
            'order' => $validated['order'] ?? 'desc'
        ];
    }

    /**
     * 验证搜索参数
     * @param array $params 搜索参数
     * @return array 验证后的搜索参数
     */
    public static function validateSearch(array $params): array
    {
        return self::validate($params, 'common.search');
    }

    /**
     * 验证ID参数
     * @param mixed $id ID值
     * @param string $fieldName 字段名称
     * @return int 验证后的ID
     * @throws ValidateException
     */
    public static function validateId($id, string $fieldName = 'id'): int
    {
        $data = [$fieldName => $id];
        $rules = [$fieldName => 'require|integer|gt:0'];
        $messages = [
            $fieldName . '.require' => $fieldName . '不能为空',
            $fieldName . '.integer' => $fieldName . '必须是整数',
            $fieldName . '.gt' => $fieldName . '必须大于0'
        ];

        $validate = Validate::rule($rules)->message($messages);
        
        if (!$validate->check($data)) {
            throw new ValidateException($validate->getError());
        }

        return (int)$id;
    }

    /**
     * 批量验证
     * @param array $dataList 数据列表
     * @param string $scene 验证场景
     * @return array 验证后的数据列表
     * @throws ValidateException
     */
    public static function batchValidate(array $dataList, string $scene): array
    {
        $validated = [];
        $errors = [];

        foreach ($dataList as $index => $data) {
            try {
                $validated[] = self::validate($data, $scene);
            } catch (ValidateException $e) {
                $errors[] = "第" . ($index + 1) . "条数据: " . $e->getError();
            }
        }

        if (!empty($errors)) {
            throw new ValidateException(implode('; ', $errors));
        }

        return $validated;
    }

    /**
     * 添加自定义验证规则
     * @param string $scene 场景名称
     * @param array $rules 验证规则
     * @param array $messages 验证消息
     */
    public static function addValidationRules(string $scene, array $rules, array $messages = []): void
    {
        self::VALIDATION_RULES[$scene] = $rules;
        if (!empty($messages)) {
            self::VALIDATION_MESSAGES = array_merge(self::VALIDATION_MESSAGES, $messages);
        }
    }

    /**
     * 获取验证规则
     * @param string $scene 场景名称
     * @return array 验证规则
     */
    public static function getValidationRules(string $scene): array
    {
        return self::VALIDATION_RULES[$scene] ?? [];
    }

    /**
     * 获取所有验证场景
     * @return array 验证场景列表
     */
    public static function getValidationScenes(): array
    {
        return array_keys(self::VALIDATION_RULES);
    }
}
