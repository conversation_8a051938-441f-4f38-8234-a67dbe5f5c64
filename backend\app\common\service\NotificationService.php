<?php

namespace app\common\service;

use app\common\model\Notification;
use think\Exception;
use think\facade\Log;

/**
 * 通知服务类
 * 支持短信、邮件等多种通知方式
 */
class NotificationService
{
    // 通知类型常量
    const TYPE_SMS = 'sms';
    const TYPE_EMAIL = 'email';
    const TYPE_WECHAT = 'wechat';
    const TYPE_SYSTEM = 'system';

    // 短信提供商
    const SMS_PROVIDER_ALIYUN = 'aliyun';
    const SMS_PROVIDER_TENCENT = 'tencent';
    const SMS_PROVIDER_HUAWEI = 'huawei';

    // 邮件提供商
    const EMAIL_PROVIDER_SMTP = 'smtp';
    const EMAIL_PROVIDER_SENDMAIL = 'sendmail';

    /**
     * 发送通知
     * 
     * @param string $type 通知类型 (sms|email|wechat|system)
     * @param array $targets 目标用户/手机号/邮箱
     * @param string $template 模板标识
     * @param array $params 模板参数
     * @param array $options 额外选项
     * @return array 发送结果
     */
    public static function send($type, $targets, $template, $params = [], $options = [])
    {
        $result = [
            'success' => false,
            'sent_count' => 0,
            'success_count' => 0,
            'failed_targets' => [],
            'message' => '',
            'data' => []
        ];

        try {
            // 验证参数
            if (empty($targets)) {
                throw new Exception('目标用户不能为空');
            }

            if (!is_array($targets)) {
                $targets = [$targets];
            }

            // 根据类型选择发送方法
            switch ($type) {
                case self::TYPE_SMS:
                    $result = self::sendSms($targets, $template, $params, $options);
                    break;
                case self::TYPE_EMAIL:
                    $result = self::sendEmail($targets, $template, $params, $options);
                    break;
                case self::TYPE_WECHAT:
                    $result = self::sendWechat($targets, $template, $params, $options);
                    break;
                case self::TYPE_SYSTEM:
                    $result = self::sendSystemNotification($targets, $template, $params, $options);
                    break;
                default:
                    throw new Exception('不支持的通知类型: ' . $type);
            }

            // 记录发送日志
            self::logNotification($type, $targets, $template, $params, $result);

        } catch (Exception $e) {
            $result['message'] = $e->getMessage();
            Log::error('通知发送失败: ' . $e->getMessage(), [
                'type' => $type,
                'targets' => $targets,
                'template' => $template,
                'params' => $params
            ]);
        }

        return $result;
    }

    /**
     * 发送短信
     */
    private static function sendSms($targets, $template, $params, $options)
    {
        $config = self::getSmsConfig();
        $provider = $config['provider'] ?? self::SMS_PROVIDER_ALIYUN;

        $result = [
            'success' => false,
            'sent_count' => count($targets),
            'success_count' => 0,
            'failed_targets' => [],
            'message' => '',
            'data' => []
        ];

        foreach ($targets as $phone) {
            try {
                // 验证手机号格式
                if (!self::validatePhone($phone)) {
                    $result['failed_targets'][] = $phone;
                    continue;
                }

                // 根据提供商发送短信
                $sendResult = false;
                switch ($provider) {
                    case self::SMS_PROVIDER_ALIYUN:
                        $sendResult = self::sendAliyunSms($phone, $template, $params, $config);
                        break;
                    case self::SMS_PROVIDER_TENCENT:
                        $sendResult = self::sendTencentSms($phone, $template, $params, $config);
                        break;
                    case self::SMS_PROVIDER_HUAWEI:
                        $sendResult = self::sendHuaweiSms($phone, $template, $params, $config);
                        break;
                    default:
                        throw new Exception('不支持的短信提供商: ' . $provider);
                }

                if ($sendResult) {
                    $result['success_count']++;
                } else {
                    $result['failed_targets'][] = $phone;
                }

            } catch (Exception $e) {
                $result['failed_targets'][] = $phone;
                Log::error('短信发送失败: ' . $e->getMessage(), ['phone' => $phone]);
            }
        }

        $result['success'] = $result['success_count'] > 0;
        $result['message'] = sprintf('发送完成，成功 %d 条，失败 %d 条', 
            $result['success_count'], 
            count($result['failed_targets'])
        );

        return $result;
    }

    /**
     * 发送邮件
     */
    private static function sendEmail($targets, $template, $params, $options)
    {
        $config = self::getEmailConfig();

        $result = [
            'success' => false,
            'sent_count' => count($targets),
            'success_count' => 0,
            'failed_targets' => [],
            'message' => '',
            'data' => []
        ];

        foreach ($targets as $email) {
            try {
                // 验证邮箱格式
                if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                    $result['failed_targets'][] = $email;
                    continue;
                }

                // 发送邮件（这里是模拟实现）
                $sendResult = self::sendSmtpEmail($email, $template, $params, $config);

                if ($sendResult) {
                    $result['success_count']++;
                } else {
                    $result['failed_targets'][] = $email;
                }

            } catch (Exception $e) {
                $result['failed_targets'][] = $email;
                Log::error('邮件发送失败: ' . $e->getMessage(), ['email' => $email]);
            }
        }

        $result['success'] = $result['success_count'] > 0;
        $result['message'] = sprintf('发送完成，成功 %d 条，失败 %d 条', 
            $result['success_count'], 
            count($result['failed_targets'])
        );

        return $result;
    }

    /**
     * 发送微信通知
     */
    private static function sendWechat($targets, $template, $params, $options)
    {
        // TODO: 实现微信通知发送
        return [
            'success' => false,
            'sent_count' => count($targets),
            'success_count' => 0,
            'failed_targets' => $targets,
            'message' => '微信通知功能暂未实现',
            'data' => []
        ];
    }

    /**
     * 发送系统内通知
     */
    private static function sendSystemNotification($targets, $template, $params, $options)
    {
        $result = [
            'success' => false,
            'sent_count' => count($targets),
            'success_count' => 0,
            'failed_targets' => [],
            'message' => '',
            'data' => []
        ];

        // 获取模板内容
        $templateContent = self::getSystemTemplate($template, $params);
        
        foreach ($targets as $userId) {
            try {
                // 创建系统通知记录
                // TODO: 实现系统通知存储逻辑
                $result['success_count']++;
            } catch (Exception $e) {
                $result['failed_targets'][] = $userId;
                Log::error('系统通知发送失败: ' . $e->getMessage(), ['user_id' => $userId]);
            }
        }

        $result['success'] = $result['success_count'] > 0;
        $result['message'] = sprintf('发送完成，成功 %d 条，失败 %d 条', 
            $result['success_count'], 
            count($result['failed_targets'])
        );

        return $result;
    }

    /**
     * 阿里云短信发送
     */
    private static function sendAliyunSms($phone, $template, $params, $config)
    {
        // TODO: 集成阿里云短信 SDK
        // 这里是模拟实现
        Log::info('模拟发送阿里云短信', [
            'phone' => $phone,
            'template' => $template,
            'params' => $params
        ]);
        
        // 模拟成功
        return true;
    }

    /**
     * 腾讯云短信发送
     */
    private static function sendTencentSms($phone, $template, $params, $config)
    {
        // TODO: 集成腾讯云短信 SDK
        Log::info('模拟发送腾讯云短信', [
            'phone' => $phone,
            'template' => $template,
            'params' => $params
        ]);
        
        return true;
    }

    /**
     * 华为云短信发送
     */
    private static function sendHuaweiSms($phone, $template, $params, $config)
    {
        // TODO: 集成华为云短信 SDK
        Log::info('模拟发送华为云短信', [
            'phone' => $phone,
            'template' => $template,
            'params' => $params
        ]);
        
        return true;
    }

    /**
     * SMTP 邮件发送
     */
    private static function sendSmtpEmail($email, $template, $params, $config)
    {
        // TODO: 集成邮件发送库（如 PHPMailer）
        Log::info('模拟发送SMTP邮件', [
            'email' => $email,
            'template' => $template,
            'params' => $params
        ]);
        
        return true;
    }

    /**
     * 获取短信配置
     */
    private static function getSmsConfig()
    {
        // 从配置文件或数据库获取短信配置
        return [
            'provider' => config('notification.sms.provider', self::SMS_PROVIDER_ALIYUN),
            'access_key' => config('notification.sms.access_key', ''),
            'access_secret' => config('notification.sms.access_secret', ''),
            'sign_name' => config('notification.sms.sign_name', ''),
            'region' => config('notification.sms.region', 'cn-hangzhou'),
        ];
    }

    /**
     * 获取邮件配置
     */
    private static function getEmailConfig()
    {
        return [
            'host' => config('notification.email.host', ''),
            'port' => config('notification.email.port', 587),
            'username' => config('notification.email.username', ''),
            'password' => config('notification.email.password', ''),
            'from_email' => config('notification.email.from_email', ''),
            'from_name' => config('notification.email.from_name', ''),
            'encryption' => config('notification.email.encryption', 'tls'),
        ];
    }

    /**
     * 获取系统通知模板
     */
    private static function getSystemTemplate($template, $params)
    {
        $templates = [
            'repayment_reminder' => '您好，您有一笔金额为 {amount} 元的贷款将于 {due_date} 到期，请及时还款。',
            'overdue_notice' => '您好，您有一笔金额为 {amount} 元的贷款已逾期 {overdue_days} 天，请尽快还款。',
            'repayment_success' => '您好，您的还款 {amount} 元已成功到账，感谢您的配合。',
            'loan_approved' => '恭喜！您的贷款申请已通过审核，放款金额 {amount} 元。',
        ];

        $content = $templates[$template] ?? $template;
        
        // 替换模板参数
        foreach ($params as $key => $value) {
            $content = str_replace('{' . $key . '}', $value, $content);
        }

        return $content;
    }

    /**
     * 验证手机号格式
     */
    private static function validatePhone($phone)
    {
        return preg_match('/^1[3-9]\d{9}$/', $phone);
    }

    /**
     * 记录通知日志
     */
    private static function logNotification($type, $targets, $template, $params, $result)
    {
        try {
            // 创建通知记录
            $notification = new Notification();
            $notification->save([
                'title' => '系统通知',
                'content' => self::getSystemTemplate($template, $params),
                'type' => 'system',
                'level' => 'info',
                'send_type' => 'immediate',
                'target_type' => 'custom',
                'target_users' => json_encode($targets),
                'sent_count' => $result['sent_count'],
                'success_count' => $result['success_count'],
                'status' => $result['success'] ? 'sent' : 'failed',
                'template_id' => $template,
                'template_params' => json_encode($params),
                'created_by' => request()->user_id ?? 0,
            ]);
        } catch (Exception $e) {
            Log::error('通知记录保存失败: ' . $e->getMessage());
        }
    }

    /**
     * 批量发送通知
     */
    public static function sendBatch($notifications)
    {
        $results = [];
        
        foreach ($notifications as $notification) {
            $result = self::send(
                $notification['type'],
                $notification['targets'],
                $notification['template'],
                $notification['params'] ?? [],
                $notification['options'] ?? []
            );
            
            $results[] = $result;
        }
        
        return $results;
    }

    /**
     * 获取支持的短信提供商列表
     */
    public static function getSmsProviders()
    {
        return [
            self::SMS_PROVIDER_ALIYUN => '阿里云短信',
            self::SMS_PROVIDER_TENCENT => '腾讯云短信',
            self::SMS_PROVIDER_HUAWEI => '华为云短信',
        ];
    }

    /**
     * 获取支持的通知类型
     */
    public static function getNotificationTypes()
    {
        return [
            self::TYPE_SMS => '短信通知',
            self::TYPE_EMAIL => '邮件通知',
            self::TYPE_WECHAT => '微信通知',
            self::TYPE_SYSTEM => '系统通知',
        ];
    }
}
