# 民间空放贷后管理系统

基于开发文档开发的完整贷后管理系统，采用ThinkPHP 8 + Vue 3技术栈。

## 🏗️ **项目架构**

### 后端技术栈
- ThinkPHP 8.x - PHP Web框架
- MySQL 8.0 - 数据库
- Redis - 缓存和会话
- JWT - 用户认证
- Firebase JWT - Token处理

### 前端技术栈  
- Vue 3 - 前端框架
- Element Plus - UI组件库
- Vue Router 4 - 路由管理
- Axios - HTTP客户端

## 📦 **项目结构**

```
daihou/
├── backend/                    # 后端代码
│   ├── app/                   # 应用目录
│   │   ├── admin/            # 管理端应用
│   │   ├── api/              # API应用
│   │   ├── finance/          # 财务端应用
│   │   ├── customer/         # 客户端应用
│   │   ├── collection/       # 催收端应用
│   │   ├── investor/         # 资方端应用
│   │   └── common/           # 公共模块
│   │       ├── model/        # 数据模型
│   │       ├── service/      # 业务服务
│   │       └── middleware/   # 中间件
│   ├── config/               # 配置文件
│   ├── route/                # 路由配置
│   ├── database/             # 数据库配置
│   └── public/               # 公共资源
├── frontend/                  # 前端代码
│   └── admin-web/            # 管理端Web应用
│       ├── src/
│       │   ├── views/        # 页面组件
│       │   ├── api/          # API接口
│       │   ├── utils/        # 工具函数
│       │   └── router/       # 路由配置
│       └── public/           # 静态资源
└── database/                 # 数据库脚本
    └── init_simple.sql       # 数据库初始化脚本
```

## 🚀 **快速开始**

### 环境要求
- PHP >= 8.0
- Node.js >= 16.0
- MySQL >= 8.0
- Composer
- NPM

### 后端启动

1. **安装依赖**
   ```bash
   cd backend
   composer install
   ```

2. **数据库配置**
   - 修改 `config/database.php` 中的数据库连接信息
   - 执行数据库初始化脚本：
   ```bash
   mysql -u root -p 你的密码 daihou < ../database/init_simple.sql
   ```

3. **启动服务**
   ```bash
   php think run
   ```
   
   访问: http://localhost:8000

### 前端启动

1. **安装依赖**
   ```bash
   cd frontend/admin-web
   npm install
   ```

2. **启动开发服务器**
   ```bash
   npm run serve
   ```
   
   访问: http://localhost:8080

## 🔐 **默认账号**

系统预置了以下测试账号：

| 用户名 | 密码 | 角色 | 说明 |
|--------|------|------|------|
| admin | password | 管理员 | 系统管理员，拥有所有权限 |
| finance001 | password | 财务 | 财务人员，负责放款和还款管理 |
| risk001 | password | 风控 | 风控人员，负责风险评估 |

*注：密码为password对应的hash值*

## 📋 **核心功能**

### 1. 用户认证系统
- [x] JWT Token认证
- [x] 用户登录/登出
- [x] 权限控制
- [x] 会话管理

### 2. 客户管理
- [x] 客户信息录入
- [x] 客户信息查询
- [x] 客户信息编辑
- [x] 风险评分
- [x] 黑名单管理

### 3. 财务管理
- [x] 放款登记
- [x] 还款登记
- [x] 还款计划生成
- [x] 中介返点管理
- [x] 财务统计

### 4. 业务流程
- [x] 放款审批流程
- [x] 还款管理流程
- [x] 逾期处理流程
- [x] 结清处理流程

## 🛠️ **API接口**

### 认证接口
- `POST /api/v1/auth/login` - 用户登录
- `GET /api/v1/auth/user` - 获取用户信息
- `POST /api/v1/auth/logout` - 用户登出

### 客户管理接口
- `GET /api/v1/customers` - 客户列表
- `POST /api/v1/customers` - 创建客户
- `GET /api/v1/customers/{id}` - 客户详情
- `PUT /api/v1/customers/{id}` - 更新客户
- `DELETE /api/v1/customers/{id}` - 删除客户
- `GET /api/v1/customers/search` - 客户搜索

### 财务管理接口
- `GET /api/v1/finance/disbursements` - 放款列表
- `POST /api/v1/finance/disbursements` - 创建放款
- `GET /api/v1/finance/disbursements/{id}` - 放款详情
- `GET /api/v1/finance/repayments` - 还款列表
- `POST /api/v1/finance/repayments` - 创建还款
- `POST /api/v1/finance/upload-screenshot` - 上传截图
- `GET /api/v1/finance/statistics` - 财务统计

## 📊 **数据库设计**

### 核心表结构
- `users` - 用户表
- `customers` - 客户表
- `loan_disbursement_records` - 放款记录表
- `repayment_records` - 还款记录表
- `repayment_schedule_details` - 还款计划详细表

详细的数据库设计请参考 `database/init_simple.sql` 文件。

## 🔧 **开发说明**

### 后端开发
- 使用ThinkPHP 8多应用模式
- RESTful API设计
- JWT身份认证
- 业务逻辑封装在Service层
- 统一的响应格式

### 前端开发
- Vue 3 Composition API
- Element Plus组件库
- Axios请求拦截器处理认证
- 路由守卫保护页面
- 响应式设计

### 代码规范
- 后端遵循PSR标准
- 前端使用ESLint
- 统一的注释规范
- Git提交规范

## 🚧 **待开发功能**

- [ ] 催收管理模块
- [ ] 资方查询小程序
- [ ] 报表统计功能
- [ ] 消息推送系统
- [ ] 移动端适配
- [ ] 数据导入导出
- [ ] 系统日志管理
- [ ] 权限管理优化

## 📄 **许可证**

本项目仅用于学习和演示目的。

## 🤝 **贡献指南**

1. Fork 本仓库
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📞 **联系方式**

如有问题，请提交Issue或联系开发团队。

---

**注意**: 这是一个演示项目，实际生产环境需要考虑更多的安全性和性能优化措施。
