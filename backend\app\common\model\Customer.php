<?php
declare (strict_types = 1);

namespace app\common\model;

use think\Model;

/**
 * 客户模型
 */
class Customer extends Model
{
    protected $name = 'customers';
    
    // 设置字段信息
    protected $schema = [
        'id'                    => 'int',
        'name'                  => 'string',
        'phone'                 => 'string',
        'id_card'               => 'string',
        'tags'                  => 'json',
        'age'                   => 'int',
        'gender'                => 'string',
        'address'               => 'string',
        'industry'              => 'string',
        'monthly_income'        => 'float',
        'emergency_contact'     => 'string',
        'emergency_phone'       => 'string',
        'risk_score'            => 'int',
        'total_loan_amount'     => 'float',
        'total_repaid_amount'   => 'float',
        'active_loans_count'    => 'int',
        'last_loan_date'        => 'date',
        'last_repayment_date'   => 'date',
        'customer_profit'       => 'float',
        'risk_assessment_score' => 'int',
        'status'                => 'string',
        'created_at'            => 'datetime',
        'updated_at'            => 'datetime',
    ];

    protected $json = ['tags'];

    // 只读字段
    protected $readonly = ['created_at'];

    /**
     * 身份证号读取器（掩码显示）
     * @param $value
     * @return string
     */
    public function getIdCardMaskedAttr(): string
    {
        if (strlen($this->id_card) === 18) {
            return substr($this->id_card, 0, 6) . '****' . substr($this->id_card, -4);
        }
        return $this->id_card;
    }

    /**
     * 手机号读取器（掩码显示）
     * @param $value
     * @return string
     */
    public function getPhoneMaskedAttr(): string
    {
        if (strlen($this->phone) === 11) {
            return substr($this->phone, 0, 3) . '****' . substr($this->phone, -4);
        }
        return $this->phone;
    }

    /**
     * 根据身份证计算年龄
     * @param string $idCard
     * @return int|null
     */
    public static function calculateAge(string $idCard): ?int
    {
        if (strlen($idCard) !== 18) {
            return null;
        }

        $birthYear = substr($idCard, 6, 4);
        $birthMonth = substr($idCard, 10, 2);
        $birthDay = substr($idCard, 12, 2);
        
        $birthDate = "$birthYear-$birthMonth-$birthDay";
        $currentDate = date('Y-m-d');
        
        $diff = date_diff(date_create($birthDate), date_create($currentDate));
        return $diff->y;
    }

    /**
     * 根据身份证获取性别
     * @param string $idCard
     * @return string|null
     */
    public static function getGenderFromIdCard(string $idCard): ?string
    {
        if (strlen($idCard) !== 18) {
            return null;
        }

        $genderCode = substr($idCard, 16, 1);
        return ($genderCode % 2 === 0) ? 'female' : 'male';
    }

    /**
     * 创建前自动计算年龄和性别
     * @param \think\Model $model
     */
    public static function onBeforeInsert($model)
    {
        if ($model->id_card) {
            $model->age = self::calculateAge($model->id_card);
            $model->gender = self::getGenderFromIdCard($model->id_card);
        }
    }

    /**
     * 更新前自动计算年龄和性别
     * @param \think\Model $model
     */
    public static function onBeforeUpdate($model)
    {
        if ($model->isChange('id_card') && $model->id_card) {
            $model->age = self::calculateAge($model->id_card);
            $model->gender = self::getGenderFromIdCard($model->id_card);
        }
    }

    /**
     * 更新客户统计信息
     * @param int $customerId
     * @param array $data
     */
    public static function updateCustomerStats(int $customerId, array $data): void
    {
        $customer = self::find($customerId);
        if ($customer) {
            $customer->save($data);
        }
    }

    /**
     * 检查客户是否在黑名单
     * @return bool
     */
    public function isInBlacklist(): bool
    {
        // 这里可以检查黑名单表
        return $this->status === 'blacklist';
    }

    /**
     * 获取客户风险等级
     * @return string
     */
    public function getRiskLevel(): string
    {
        $score = $this->risk_score;
        
        if ($score >= 80) {
            return 'low';
        } elseif ($score >= 60) {
            return 'medium';
        } else {
            return 'high';
        }
    }
}
