<?php
declare (strict_types = 1);

namespace app\common\middleware;

class PermissionMiddleware
{
    /**
     * 校验权限代码（支持多个），admin 角色放行，支持 '*' 万能权限
     */
    public function handle($request, \Closure $next, ...$permissions)
    {
        $user = $request->user ?? null;
        if (!$user) {
            return json(['code'=>401,'message'=>'未认证用户','data'=>null], 401);
        }

        if (($user['role'] ?? '') === 'admin') {
            return $next($request);
        }

        $userPerms = $user['permissions'] ?? [];
        if (in_array('*', $userPerms, true)) {
            return $next($request);
        }

        $allowed = false;
        foreach ($permissions as $perm) {
            if (in_array($perm, $userPerms, true)) {
                $allowed = true;
                break;
            }
        }

        if (!$allowed) {
            return json(['code'=>403,'message'=>'权限不足','data'=>null], 403);
        }

        return $next($request);
    }
}


