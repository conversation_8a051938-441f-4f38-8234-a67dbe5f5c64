<?php

namespace app\api\controller;

use app\BaseController;
use app\common\service\NotificationService;
use think\Response;

/**
 * 通知示例控制器
 * 演示如何使用通知服务
 */
class NotificationExample extends BaseController
{
    /**
     * 发送还款提醒短信
     */
    public function sendRepaymentReminder()
    {
        $params = $this->request->param();
        
        // 验证参数
        $this->validate($params, [
            'customer_phone' => 'require|mobile',
            'customer_name' => 'require',
            'amount' => 'require|number',
            'due_date' => 'require|date',
        ]);
        
        try {
            // 发送短信提醒
            $result = NotificationService::send(
                NotificationService::TYPE_SMS,
                [$params['customer_phone']],
                'repayment_reminder',
                [
                    'customer_name' => $params['customer_name'],
                    'amount' => $params['amount'],
                    'due_date' => $params['due_date'],
                ]
            );
            
            if ($result['success']) {
                return json(['code' => 200, 'message' => '发送成功', 'data' => $result]);
            } else {
                return json(['code' => 400, 'message' => $result['message'], 'data' => $result]);
            }
            
        } catch (\Exception $e) {
            return json(['code' => 500, 'message' => '发送失败: ' . $e->getMessage()]);
        }
    }
    
    /**
     * 发送逾期通知邮件
     */
    public function sendOverdueEmail()
    {
        $params = $this->request->param();
        
        // 验证参数
        $this->validate($params, [
            'customer_email' => 'require|email',
            'customer_name' => 'require',
            'amount' => 'require|number',
            'overdue_days' => 'require|number',
        ]);
        
        try {
            // 发送邮件通知
            $result = NotificationService::send(
                NotificationService::TYPE_EMAIL,
                [$params['customer_email']],
                'overdue_notice',
                [
                    'customer_name' => $params['customer_name'],
                    'amount' => $params['amount'],
                    'overdue_days' => $params['overdue_days'],
                ]
            );
            
            if ($result['success']) {
                return json(['code' => 200, 'message' => '发送成功', 'data' => $result]);
            } else {
                return json(['code' => 400, 'message' => $result['message'], 'data' => $result]);
            }
            
        } catch (\Exception $e) {
            return json(['code' => 500, 'message' => '发送失败: ' . $e->getMessage()]);
        }
    }
    
    /**
     * 批量发送通知
     */
    public function sendBatchNotifications()
    {
        $params = $this->request->param();
        
        // 验证参数
        $this->validate($params, [
            'notifications' => 'require|array',
        ]);
        
        try {
            $results = NotificationService::sendBatch($params['notifications']);
            
            $successCount = 0;
            $totalCount = count($results);
            
            foreach ($results as $result) {
                if ($result['success']) {
                    $successCount++;
                }
            }
            
            return json([
                'code' => 200,
                'message' => sprintf('批量发送完成，成功 %d/%d', $successCount, $totalCount),
                'data' => [
                    'total_count' => $totalCount,
                    'success_count' => $successCount,
                    'results' => $results
                ]
            ]);
            
        } catch (\Exception $e) {
            return json(['code' => 500, 'message' => '批量发送失败: ' . $e->getMessage()]);
        }
    }
    
    /**
     * 获取通知配置信息
     */
    public function getNotificationConfig()
    {
        try {
            $config = [
                'sms_providers' => NotificationService::getSmsProviders(),
                'notification_types' => NotificationService::getNotificationTypes(),
                'current_sms_provider' => config('notification.sms.provider'),
                'sms_enabled' => !empty(config('notification.sms.access_key')),
                'email_enabled' => !empty(config('notification.email.username')),
                'wechat_enabled' => config('notification.wechat.enabled', false),
                'system_enabled' => config('notification.system.enabled', true),
                'template_vars' => config('notification.template_vars', [])
            ];
            
            return json(['code' => 200, 'message' => '获取成功', 'data' => $config]);
            
        } catch (\Exception $e) {
            return json(['code' => 500, 'message' => '获取失败: ' . $e->getMessage()]);
        }
    }
    
    /**
     * 测试短信发送
     */
    public function testSms()
    {
        $params = $this->request->param();
        
        // 验证参数
        $this->validate($params, [
            'phone' => 'require|mobile',
        ]);
        
        try {
            // 发送测试短信
            $result = NotificationService::send(
                NotificationService::TYPE_SMS,
                [$params['phone']],
                'verify_code',
                [
                    'verify_code' => rand(100000, 999999),
                    'company_name' => config('notification.template_vars.company_name', '贷后管理系统')
                ]
            );
            
            if ($result['success']) {
                return json(['code' => 200, 'message' => '测试短信发送成功', 'data' => $result]);
            } else {
                return json(['code' => 400, 'message' => $result['message'], 'data' => $result]);
            }
            
        } catch (\Exception $e) {
            return json(['code' => 500, 'message' => '测试失败: ' . $e->getMessage()]);
        }
    }
    
    /**
     * 测试邮件发送
     */
    public function testEmail()
    {
        $params = $this->request->param();
        
        // 验证参数
        $this->validate($params, [
            'email' => 'require|email',
        ]);
        
        try {
            // 发送测试邮件
            $result = NotificationService::send(
                NotificationService::TYPE_EMAIL,
                [$params['email']],
                'password_reset',
                [
                    'customer_name' => '测试用户',
                    'verify_code' => rand(100000, 999999),
                    'company_name' => config('notification.template_vars.company_name', '贷后管理系统')
                ]
            );
            
            if ($result['success']) {
                return json(['code' => 200, 'message' => '测试邮件发送成功', 'data' => $result]);
            } else {
                return json(['code' => 400, 'message' => $result['message'], 'data' => $result]);
            }
            
        } catch (\Exception $e) {
            return json(['code' => 500, 'message' => '测试失败: ' . $e->getMessage()]);
        }
    }
    
    /**
     * 自动发送逾期提醒（定时任务调用）
     */
    public function autoSendOverdueReminders()
    {
        try {
            // 查询逾期的贷款记录
            $overdueLoans = \app\common\model\LoanDisbursementRecord::where('status', 'overdue')
                ->with(['customer'])
                ->select();
            
            $notifications = [];
            
            foreach ($overdueLoans as $loan) {
                if (empty($loan->customer->phone)) {
                    continue;
                }
                
                // 计算逾期天数
                $overdueDays = (time() - strtotime($loan->loan_date)) / (24 * 3600);
                
                $notifications[] = [
                    'type' => NotificationService::TYPE_SMS,
                    'targets' => [$loan->customer->phone],
                    'template' => 'overdue_notice',
                    'params' => [
                        'customer_name' => $loan->customer->name,
                        'amount' => $loan->remaining_amount,
                        'overdue_days' => (int)$overdueDays,
                        'contact_phone' => config('notification.template_vars.contact_phone', '************')
                    ]
                ];
            }
            
            if (empty($notifications)) {
                return json(['code' => 200, 'message' => '暂无逾期记录需要提醒']);
            }
            
            // 批量发送
            $results = NotificationService::sendBatch($notifications);
            
            $successCount = 0;
            foreach ($results as $result) {
                if ($result['success']) {
                    $successCount++;
                }
            }
            
            return json([
                'code' => 200, 
                'message' => sprintf('逾期提醒发送完成，共处理 %d 条记录，成功 %d 条', 
                    count($notifications), $successCount),
                'data' => ['total' => count($notifications), 'success' => $successCount]
            ]);
            
        } catch (\Exception $e) {
            return json(['code' => 500, 'message' => '自动提醒失败: ' . $e->getMessage()]);
        }
    }
}
