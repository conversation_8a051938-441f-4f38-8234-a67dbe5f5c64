<?php
declare (strict_types = 1);

namespace app\common\model;

use think\Model;

/**
 * 用户模型
 */
class User extends Model
{
    protected $name = 'users';
    
    // 设置字段信息
    protected $schema = [
        'id'              => 'int',
        'username'        => 'string',
        'password'        => 'string',
        'real_name'       => 'string',
        'phone'           => 'string',
        'email'           => 'string',
        'role'            => 'string',
        'department'      => 'string',
        'permissions'     => 'json',
        'status'          => 'string',
        'last_login_at'   => 'datetime',
        'created_at'      => 'datetime',
        'updated_at'      => 'datetime',
    ];

    // JSON字段
    protected $json = ['permissions'];

    // 隐藏字段
    protected $hidden = ['password'];

    // 只读字段
    protected $readonly = ['username', 'created_at'];

    /**
     * 密码修改器
     * @param $value
     * @return string
     */
    public function setPasswordAttr($value): string
    {
        return password_hash($value, PASSWORD_DEFAULT);
    }

    /**
     * 权限读取器
     * @param $value
     * @return array
     */
    public function getPermissionsAttr($value): array
    {
        if (is_string($value)) {
            return json_decode($value, true) ?: [];
        }
        return $value ?: [];
    }

    /**
     * 验证密码
     * @param string $password
     * @return bool
     */
    public function verifyPassword(string $password): bool
    {
        return password_verify($password, $this->getData('password'));
    }

    /**
     * 根据用户名查找用户
     * @param string $username
     * @return User|null
     */
    public static function findByUsername(string $username): ?self
    {
        return self::where('username', $username)->find();
    }

    /**
     * 更新最后登录时间
     */
    public function updateLastLoginTime(): void
    {
        $this->last_login_at = date('Y-m-d H:i:s');
        $this->save();
    }

    /**
     * 检查用户权限
     * @param string $permission
     * @return bool
     */
    public function hasPermission(string $permission): bool
    {
        $permissions = $this->permissions;
        return in_array($permission, $permissions) || in_array('*', $permissions);
    }

    /**
     * 检查用户角色
     * @param string|array $roles
     * @return bool
     */
    public function hasRole($roles): bool
    {
        if (is_string($roles)) {
            $roles = [$roles];
        }
        return in_array($this->role, $roles);
    }
}
