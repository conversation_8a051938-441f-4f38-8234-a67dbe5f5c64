<?php
declare(strict_types=1);

namespace tests\Unit\Service;

use tests\TestCase;
use app\common\service\StandardResponseService;
use think\Response;

/**
 * 响应服务测试
 */
class ResponseServiceTest extends TestCase
{
    /**
     * 测试成功响应
     */
    public function testSuccessResponse(): void
    {
        $data = ['id' => 1, 'name' => '测试'];
        $message = '操作成功';
        
        $response = StandardResponseService::success($data, $message);
        
        $this->assertInstanceOf(Response::class, $response);
        
        $responseData = json_decode($response->getContent(), true);
        
        $this->assertTrue($responseData['success']);
        $this->assertEquals(200, $responseData['code']);
        $this->assertEquals($message, $responseData['message']);
        $this->assertEquals($data, $responseData['data']);
        $this->assertArrayHasKey('timestamp', $responseData);
        $this->assertArrayHasKey('request_id', $responseData);
    }

    /**
     * 测试错误响应
     */
    public function testErrorResponse(): void
    {
        $message = '操作失败';
        $code = 400;
        
        $response = StandardResponseService::error($message, $code);
        
        $this->assertInstanceOf(Response::class, $response);
        
        $responseData = json_decode($response->getContent(), true);
        
        $this->assertFalse($responseData['success']);
        $this->assertEquals($code, $responseData['code']);
        $this->assertEquals($message, $responseData['message']);
        $this->assertArrayHasKey('timestamp', $responseData);
        $this->assertArrayHasKey('request_id', $responseData);
    }

    /**
     * 测试分页响应
     */
    public function testPaginateResponse(): void
    {
        $data = [
            ['id' => 1, 'name' => '测试1'],
            ['id' => 2, 'name' => '测试2']
        ];
        
        $pagination = [
            'total' => 10,
            'per_page' => 2,
            'current_page' => 1,
            'last_page' => 5
        ];
        
        $response = StandardResponseService::paginate($data, '获取成功', $pagination);
        
        $this->assertInstanceOf(Response::class, $response);
        
        $responseData = json_decode($response->getContent(), true);
        
        $this->assertTrue($responseData['success']);
        $this->assertEquals(200, $responseData['code']);
        $this->assertEquals($data, $responseData['data']);
        $this->assertArrayHasKey('meta', $responseData);
        $this->assertArrayHasKey('pagination', $responseData['meta']);
        $this->assertEquals($pagination, $responseData['meta']['pagination']);
    }

    /**
     * 测试创建成功响应
     */
    public function testCreatedResponse(): void
    {
        $data = ['id' => 1, 'name' => '新创建的项目'];
        
        $response = StandardResponseService::created($data);
        
        $this->assertInstanceOf(Response::class, $response);
        $this->assertEquals(201, $response->getCode());
        
        $responseData = json_decode($response->getContent(), true);
        
        $this->assertTrue($responseData['success']);
        $this->assertEquals(201, $responseData['code']);
        $this->assertEquals('创建成功', $responseData['message']);
        $this->assertEquals($data, $responseData['data']);
    }

    /**
     * 测试未授权响应
     */
    public function testUnauthorizedResponse(): void
    {
        $message = '未授权访问';
        
        $response = StandardResponseService::unauthorized($message);
        
        $this->assertInstanceOf(Response::class, $response);
        $this->assertEquals(401, $response->getCode());
        
        $responseData = json_decode($response->getContent(), true);
        
        $this->assertFalse($responseData['success']);
        $this->assertEquals(401, $responseData['code']);
        $this->assertEquals($message, $responseData['message']);
    }

    /**
     * 测试资源不存在响应
     */
    public function testNotFoundResponse(): void
    {
        $message = '资源不存在';
        
        $response = StandardResponseService::notFound($message);
        
        $this->assertInstanceOf(Response::class, $response);
        $this->assertEquals(404, $response->getCode());
        
        $responseData = json_decode($response->getContent(), true);
        
        $this->assertFalse($responseData['success']);
        $this->assertEquals(404, $responseData['code']);
        $this->assertEquals($message, $responseData['message']);
    }

    /**
     * 测试验证错误响应
     */
    public function testValidationErrorResponse(): void
    {
        $message = '数据验证失败';
        $errors = [
            'name' => '姓名不能为空',
            'phone' => '手机号格式不正确'
        ];
        
        $response = StandardResponseService::validationError($message, $errors);
        
        $this->assertInstanceOf(Response::class, $response);
        $this->assertEquals(422, $response->getCode());
        
        $responseData = json_decode($response->getContent(), true);
        
        $this->assertFalse($responseData['success']);
        $this->assertEquals(422, $responseData['code']);
        $this->assertEquals($message, $responseData['message']);
        $this->assertArrayHasKey('errors', $responseData);
        $this->assertEquals($errors, $responseData['errors']);
    }

    /**
     * 测试服务器错误响应
     */
    public function testServerErrorResponse(): void
    {
        $message = '服务器内部错误';
        
        $response = StandardResponseService::serverError($message);
        
        $this->assertInstanceOf(Response::class, $response);
        $this->assertEquals(500, $response->getCode());
        
        $responseData = json_decode($response->getContent(), true);
        
        $this->assertFalse($responseData['success']);
        $this->assertEquals(500, $responseData['code']);
        $this->assertEquals($message, $responseData['message']);
    }

    /**
     * 测试默认消息
     */
    public function testDefaultMessages(): void
    {
        // 测试成功响应默认消息
        $response = StandardResponseService::success();
        $responseData = json_decode($response->getContent(), true);
        $this->assertEquals('操作成功', $responseData['message']);

        // 测试错误响应默认消息
        $response = StandardResponseService::error();
        $responseData = json_decode($response->getContent(), true);
        $this->assertEquals('请求参数错误', $responseData['message']);

        // 测试未授权响应默认消息
        $response = StandardResponseService::unauthorized();
        $responseData = json_decode($response->getContent(), true);
        $this->assertEquals('未授权访问', $responseData['message']);
    }

    /**
     * 测试响应格式一致性
     */
    public function testResponseFormatConsistency(): void
    {
        $responses = [
            StandardResponseService::success(),
            StandardResponseService::error(),
            StandardResponseService::created(),
            StandardResponseService::unauthorized(),
            StandardResponseService::notFound(),
            StandardResponseService::validationError(),
            StandardResponseService::serverError()
        ];

        foreach ($responses as $response) {
            $responseData = json_decode($response->getContent(), true);
            
            // 检查必需字段
            $this->assertArrayHasKey('success', $responseData);
            $this->assertArrayHasKey('code', $responseData);
            $this->assertArrayHasKey('message', $responseData);
            $this->assertArrayHasKey('data', $responseData);
            $this->assertArrayHasKey('timestamp', $responseData);
            $this->assertArrayHasKey('request_id', $responseData);
            
            // 检查字段类型
            $this->assertIsBool($responseData['success']);
            $this->assertIsInt($responseData['code']);
            $this->assertIsString($responseData['message']);
            $this->assertIsInt($responseData['timestamp']);
            $this->assertIsString($responseData['request_id']);
        }
    }
}
