# 开发文档不足分析与改进建议

## 一、当前文档缺失的重要模块

### 1. 缺失模块列表

#### 1.1 系统基础设施
- ❌ **数据库设计文档**：缺少完整的数据表结构设计
- ❌ **接口文档**：缺少标准化的API接口文档
- ❌ **权限管理系统**：缺少RBAC权限控制设计
- ❌ **数据字典**：缺少业务字段标准化定义
- ❌ **部署架构**：缺少生产环境部署方案

#### 1.2 业务流程设计
- ❌ **完整业务流程图**：缺少端到端业务流程设计
- ❌ **状态机设计**：缺少申请状态、审核状态等状态流转设计
- ❌ **工作流引擎**：缺少灵活的审批流程配置
- ❌ **消息通知系统**：缺少短信、邮件、APP推送等通知机制
- ❌ **日志审计系统**：缺少操作日志和审计追踪

#### 1.3 高级功能模块
- ❌ **报表系统**：缺少动态报表生成和导出功能
- ❌ **导入导出**：缺少数据批量导入导出功能
- ❌ **文件管理**：缺少文件存储、预览、下载管理
- ❌ **定时任务**：缺少自动化任务调度系统
- ❌ **缓存策略**：缺少Redis缓存使用策略

#### 1.4 安全与合规
- ❌ **数据加密**：缺少敏感数据加密存储方案
- ❌ **访问控制**：缺少IP白名单、设备绑定等安全措施
- ❌ **合规审计**：缺少金融行业合规要求实现
- ❌ **数据备份**：缺少数据备份恢复方案
- ❌ **灾难恢复**：缺少业务连续性保障方案

### 2. 技术架构不足

#### 2.1 微服务架构缺失
- 当前设计仍是单体应用架构
- 缺少服务拆分和微服务治理
- 缺少服务注册发现机制
- 缺少分布式事务处理

#### 2.2 性能优化不足
- 缺少数据库优化策略
- 缺少接口性能监控
- 缺少前端性能优化方案
- 缺少CDN和静态资源优化

#### 2.3 扩展性设计不足
- 缺少插件化架构设计
- 缺少多租户支持
- 缺少国际化支持
- 缺少主题定制能力

## 二、详细补充设计方案

### 1. 数据库设计补充

```sql
-- 用户表
CREATE TABLE users (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    password VARCHAR(255) NOT NULL COMMENT '密码',
    real_name VARCHAR(50) NOT NULL COMMENT '真实姓名',
    email VARCHAR(100) COMMENT '邮箱',
    phone VARCHAR(20) COMMENT '手机号',
    avatar VARCHAR(500) COMMENT '头像',
    status ENUM('active','inactive','locked') DEFAULT 'active' COMMENT '状态',
    last_login_time TIMESTAMP NULL COMMENT '最后登录时间',
    last_login_ip VARCHAR(45) COMMENT '最后登录IP',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_username (username),
    INDEX idx_phone (phone),
    INDEX idx_status (status)
) COMMENT='用户表';

-- 角色表
CREATE TABLE roles (
    id INT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(50) NOT NULL UNIQUE COMMENT '角色名称',
    code VARCHAR(50) NOT NULL UNIQUE COMMENT '角色代码',
    description TEXT COMMENT '角色描述',
    status TINYINT DEFAULT 1 COMMENT '状态',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) COMMENT='角色表';

-- 权限表
CREATE TABLE permissions (
    id INT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(50) NOT NULL COMMENT '权限名称',
    code VARCHAR(100) NOT NULL UNIQUE COMMENT '权限代码',
    type ENUM('menu','button','api') NOT NULL COMMENT '权限类型',
    parent_id INT DEFAULT 0 COMMENT '父权限ID',
    path VARCHAR(200) COMMENT '菜单路径',
    icon VARCHAR(50) COMMENT '图标',
    sort_order INT DEFAULT 0 COMMENT '排序',
    status TINYINT DEFAULT 1 COMMENT '状态',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_parent_id (parent_id),
    INDEX idx_type (type)
) COMMENT='权限表';

-- 用户角色关联表
CREATE TABLE user_roles (
    user_id BIGINT UNSIGNED NOT NULL,
    role_id INT UNSIGNED NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    PRIMARY KEY (user_id, role_id),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE
) COMMENT='用户角色关联表';

-- 角色权限关联表
CREATE TABLE role_permissions (
    role_id INT UNSIGNED NOT NULL,
    permission_id INT UNSIGNED NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    PRIMARY KEY (role_id, permission_id),
    FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE,
    FOREIGN KEY (permission_id) REFERENCES permissions(id) ON DELETE CASCADE
) COMMENT='角色权限关联表';

-- 客户表
CREATE TABLE customers (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    customer_no VARCHAR(20) NOT NULL UNIQUE COMMENT '客户编号',
    name VARCHAR(50) NOT NULL COMMENT '客户姓名',
    id_card VARCHAR(18) NOT NULL COMMENT '身份证号',
    phone VARCHAR(20) NOT NULL COMMENT '手机号',
    gender ENUM('male','female') COMMENT '性别',
    birthday DATE COMMENT '生日',
    education ENUM('primary','junior','senior','bachelor','master','doctor') COMMENT '学历',
    marital_status ENUM('single','married','divorced','widowed') COMMENT '婚姻状况',
    industry VARCHAR(50) COMMENT '行业',
    occupation VARCHAR(50) COMMENT '职业',
    monthly_income DECIMAL(10,2) COMMENT '月收入',
    company_name VARCHAR(100) COMMENT '公司名称',
    company_address TEXT COMMENT '公司地址',
    residence_address TEXT COMMENT '居住地址',
    residence_type ENUM('own','rent','family') COMMENT '居住性质',
    emergency_contact_name VARCHAR(50) COMMENT '紧急联系人姓名',
    emergency_contact_phone VARCHAR(20) COMMENT '紧急联系人电话',
    emergency_contact_relation VARCHAR(20) COMMENT '紧急联系人关系',
    source ENUM('web','app','agent','phone') COMMENT '客户来源',
    agent_id INT COMMENT '中介ID',
    status ENUM('normal','blacklist','potential') DEFAULT 'normal' COMMENT '客户状态',
    risk_level ENUM('low','medium','high') DEFAULT 'medium' COMMENT '风险等级',
    created_by INT COMMENT '创建人ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    UNIQUE KEY unique_id_card (id_card),
    UNIQUE KEY unique_phone (phone),
    INDEX idx_customer_no (customer_no),
    INDEX idx_name (name),
    INDEX idx_status (status),
    INDEX idx_risk_level (risk_level),
    INDEX idx_agent_id (agent_id)
) COMMENT='客户表';

-- 贷款申请表
CREATE TABLE loan_applications (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    application_no VARCHAR(30) NOT NULL UNIQUE COMMENT '申请编号',
    customer_id BIGINT UNSIGNED NOT NULL COMMENT '客户ID',
    apply_amount DECIMAL(10,2) NOT NULL COMMENT '申请金额',
    loan_purpose VARCHAR(200) COMMENT '借款用途',
    apply_term INT NOT NULL COMMENT '申请期限(天)',
    interest_rate DECIMAL(5,4) COMMENT '利息率',
    service_fee DECIMAL(10,2) DEFAULT 0 COMMENT '服务费',
    total_amount DECIMAL(10,2) COMMENT '总金额',
    status ENUM('pending','reviewing','approved','rejected','disbursed','completed','overdue') DEFAULT 'pending' COMMENT '申请状态',
    submit_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '提交时间',
    review_time TIMESTAMP NULL COMMENT '审核时间',
    approval_time TIMESTAMP NULL COMMENT '批准时间',
    disburse_time TIMESTAMP NULL COMMENT '放款时间',
    due_time TIMESTAMP NULL COMMENT '到期时间',
    repay_time TIMESTAMP NULL COMMENT '还款时间',
    actual_amount DECIMAL(10,2) COMMENT '实际放款金额',
    reviewer_id INT COMMENT '审核人ID',
    approver_id INT COMMENT '批准人ID',
    review_notes TEXT COMMENT '审核备注',
    rejection_reason TEXT COMMENT '拒绝原因',
    risk_score INT COMMENT 'AI风控评分',
    risk_assessment JSON COMMENT '风控评估结果',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (customer_id) REFERENCES customers(id),
    INDEX idx_application_no (application_no),
    INDEX idx_customer_id (customer_id),
    INDEX idx_status (status),
    INDEX idx_submit_time (submit_time),
    INDEX idx_due_time (due_time)
) COMMENT='贷款申请表';

-- 还款记录表
CREATE TABLE repayment_records (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    application_id BIGINT UNSIGNED NOT NULL COMMENT '申请ID',
    customer_id BIGINT UNSIGNED NOT NULL COMMENT '客户ID',
    repay_amount DECIMAL(10,2) NOT NULL COMMENT '还款金额',
    repay_type ENUM('principal','interest','penalty','service_fee') NOT NULL COMMENT '还款类型',
    repay_method ENUM('online','bank_transfer','cash','pos') NOT NULL COMMENT '还款方式',
    transaction_no VARCHAR(50) COMMENT '交易流水号',
    repay_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '还款时间',
    operator_id INT COMMENT '操作员ID',
    notes TEXT COMMENT '备注',
    status ENUM('pending','success','failed') DEFAULT 'success' COMMENT '状态',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (application_id) REFERENCES loan_applications(id),
    FOREIGN KEY (customer_id) REFERENCES customers(id),
    INDEX idx_application_id (application_id),
    INDEX idx_customer_id (customer_id),
    INDEX idx_repay_time (repay_time),
    INDEX idx_transaction_no (transaction_no)
) COMMENT='还款记录表';

-- 催收记录表
CREATE TABLE collection_records (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    application_id BIGINT UNSIGNED NOT NULL COMMENT '申请ID',
    customer_id BIGINT UNSIGNED NOT NULL COMMENT '客户ID',
    collector_id INT NOT NULL COMMENT '催收员ID',
    collection_type ENUM('phone','sms','visit','email') NOT NULL COMMENT '催收方式',
    collection_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '催收时间',
    contact_result ENUM('connected','not_connected','refused','invalid') COMMENT '联系结果',
    payment_promise ENUM('immediate','today','tomorrow','week','month','refuse') COMMENT '还款承诺',
    promised_amount DECIMAL(10,2) COMMENT '承诺金额',
    promised_time TIMESTAMP NULL COMMENT '承诺时间',
    collection_notes TEXT COMMENT '催收记录',
    next_collection_time TIMESTAMP NULL COMMENT '下次催收时间',
    status ENUM('pending','completed','invalid') DEFAULT 'completed' COMMENT '状态',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (application_id) REFERENCES loan_applications(id),
    FOREIGN KEY (customer_id) REFERENCES customers(id),
    INDEX idx_application_id (application_id),
    INDEX idx_customer_id (customer_id),
    INDEX idx_collector_id (collector_id),
    INDEX idx_collection_time (collection_time)
) COMMENT='催收记录表';

-- 下户记录表
CREATE TABLE visit_records (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    application_id BIGINT UNSIGNED NOT NULL COMMENT '申请ID',
    customer_id BIGINT UNSIGNED NOT NULL COMMENT '客户ID',
    visitor_id INT NOT NULL COMMENT '下户人员ID',
    visit_type ENUM('pre_loan','collection','investigation') NOT NULL COMMENT '下户类型',
    visit_address TEXT NOT NULL COMMENT '下户地址',
    visit_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '下户时间',
    contact_person VARCHAR(50) COMMENT '联系人',
    contact_result ENUM('met_customer','met_family','met_neighbor','not_met') COMMENT '联系结果',
    visit_photos JSON COMMENT '现场照片',
    visit_notes TEXT COMMENT '下户记录',
    expenses DECIMAL(8,2) DEFAULT 0 COMMENT '下户费用',
    expense_details JSON COMMENT '费用明细',
    latitude DECIMAL(10,6) COMMENT '纬度',
    longitude DECIMAL(10,6) COMMENT '经度',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (application_id) REFERENCES loan_applications(id),
    FOREIGN KEY (customer_id) REFERENCES customers(id),
    INDEX idx_application_id (application_id),
    INDEX idx_customer_id (customer_id),
    INDEX idx_visitor_id (visitor_id),
    INDEX idx_visit_time (visit_time)
) COMMENT='下户记录表';

-- 系统配置表
CREATE TABLE system_configs (
    id INT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    config_group VARCHAR(50) NOT NULL COMMENT '配置分组',
    config_key VARCHAR(100) NOT NULL COMMENT '配置键',
    config_value TEXT COMMENT '配置值',
    config_type ENUM('string','number','boolean','json') DEFAULT 'string' COMMENT '配置类型',
    description VARCHAR(255) COMMENT '配置描述',
    is_system TINYINT DEFAULT 0 COMMENT '是否系统配置',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    UNIQUE KEY unique_config (config_group, config_key),
    INDEX idx_config_group (config_group)
) COMMENT='系统配置表';

-- 操作日志表
CREATE TABLE operation_logs (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    user_id INT COMMENT '操作用户ID',
    username VARCHAR(50) COMMENT '用户名',
    module VARCHAR(50) NOT NULL COMMENT '操作模块',
    action VARCHAR(50) NOT NULL COMMENT '操作动作',
    description TEXT COMMENT '操作描述',
    request_url VARCHAR(500) COMMENT '请求URL',
    request_method VARCHAR(10) COMMENT '请求方法',
    request_params TEXT COMMENT '请求参数',
    response_data TEXT COMMENT '响应数据',
    ip_address VARCHAR(45) COMMENT 'IP地址',
    user_agent TEXT COMMENT '用户代理',
    execution_time INT COMMENT '执行时间(毫秒)',
    status ENUM('success','error') DEFAULT 'success' COMMENT '执行状态',
    error_message TEXT COMMENT '错误信息',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_user_id (user_id),
    INDEX idx_module (module),
    INDEX idx_created_at (created_at),
    INDEX idx_ip_address (ip_address)
) COMMENT='操作日志表';

-- 消息通知表
CREATE TABLE notifications (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(200) NOT NULL COMMENT '通知标题',
    content TEXT NOT NULL COMMENT '通知内容',
    type ENUM('system','business','marketing') NOT NULL COMMENT '通知类型',
    level ENUM('info','warning','error','success') DEFAULT 'info' COMMENT '通知级别',
    send_type ENUM('sms','email','app_push','system_msg') NOT NULL COMMENT '发送方式',
    target_type ENUM('all','user','role','custom') NOT NULL COMMENT '目标类型',
    target_users JSON COMMENT '目标用户',
    send_time TIMESTAMP NULL COMMENT '发送时间',
    sent_count INT DEFAULT 0 COMMENT '发送数量',
    success_count INT DEFAULT 0 COMMENT '成功数量',
    status ENUM('draft','sending','sent','failed') DEFAULT 'draft' COMMENT '状态',
    template_id VARCHAR(50) COMMENT '模板ID',
    template_params JSON COMMENT '模板参数',
    created_by INT COMMENT '创建人ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_type (type),
    INDEX idx_send_type (send_type),
    INDEX idx_status (status),
    INDEX idx_send_time (send_time)
) COMMENT='消息通知表';

-- 文件管理表
CREATE TABLE file_management (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    original_name VARCHAR(255) NOT NULL COMMENT '原始文件名',
    file_name VARCHAR(255) NOT NULL COMMENT '存储文件名',
    file_path VARCHAR(500) NOT NULL COMMENT '文件路径',
    file_size BIGINT NOT NULL COMMENT '文件大小(字节)',
    file_type VARCHAR(50) NOT NULL COMMENT '文件类型',
    mime_type VARCHAR(100) COMMENT 'MIME类型',
    file_hash VARCHAR(64) COMMENT '文件哈希值',
    storage_type ENUM('local','oss','cos','qiniu') DEFAULT 'local' COMMENT '存储类型',
    business_type VARCHAR(50) COMMENT '业务类型',
    business_id BIGINT COMMENT '业务ID',
    uploaded_by INT COMMENT '上传者ID',
    download_count INT DEFAULT 0 COMMENT '下载次数',
    is_public TINYINT DEFAULT 0 COMMENT '是否公开',
    status ENUM('active','deleted') DEFAULT 'active' COMMENT '状态',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_file_hash (file_hash),
    INDEX idx_business (business_type, business_id),
    INDEX idx_uploaded_by (uploaded_by),
    INDEX idx_created_at (created_at)
) COMMENT='文件管理表';
```

### 2. 权限管理系统设计

```php
// app/common/service/AuthService.php
<?php

namespace app\common\service;

use app\common\model\User;
use app\common\model\Role;
use app\common\model\Permission;
use think\facade\Cache;
use think\facade\Session;

class AuthService
{
    /**
     * 用户登录
     */
    public static function login($username, $password, $remember = false)
    {
        $user = User::where('username', $username)
                   ->where('status', 'active')
                   ->find();
                   
        if (!$user || !password_verify($password, $user->password)) {
            throw new \Exception('用户名或密码错误');
        }
        
        // 更新登录信息
        $user->last_login_time = date('Y-m-d H:i:s');
        $user->last_login_ip = request()->ip();
        $user->save();
        
        // 设置会话
        Session::set('user_id', $user->id);
        Session::set('username', $user->username);
        
        // 记住登录
        if ($remember) {
            $token = self::generateRememberToken($user);
            cookie('remember_token', $token, 3600 * 24 * 30);
        }
        
        // 记录登录日志
        LogService::record('auth', 'login', '用户登录');
        
        return $user;
    }
    
    /**
     * 检查权限
     */
    public static function checkPermission($permission)
    {
        $userId = Session::get('user_id');
        if (!$userId) {
            return false;
        }
        
        $cacheKey = "user_permissions:{$userId}";
        $permissions = Cache::remember($cacheKey, function() use ($userId) {
            return self::getUserPermissions($userId);
        }, 3600);
        
        return in_array($permission, $permissions);
    }
    
    /**
     * 获取用户权限
     */
    public static function getUserPermissions($userId)
    {
        $user = User::with(['roles.permissions'])->find($userId);
        $permissions = [];
        
        foreach ($user->roles as $role) {
            foreach ($role->permissions as $permission) {
                $permissions[] = $permission->code;
            }
        }
        
        return array_unique($permissions);
    }
    
    /**
     * 获取用户菜单
     */
    public static function getUserMenus($userId)
    {
        $permissions = self::getUserPermissions($userId);
        
        $menuPermissions = Permission::where('type', 'menu')
                                   ->whereIn('code', $permissions)
                                   ->order('sort_order', 'asc')
                                   ->select();
        
        return self::buildMenuTree($menuPermissions->toArray());
    }
    
    /**
     * 构建菜单树
     */
    private static function buildMenuTree($permissions, $parentId = 0)
    {
        $tree = [];
        foreach ($permissions as $permission) {
            if ($permission['parent_id'] == $parentId) {
                $children = self::buildMenuTree($permissions, $permission['id']);
                if ($children) {
                    $permission['children'] = $children;
                }
                $tree[] = $permission;
            }
        }
        return $tree;
    }
}
```

### 3. 工作流引擎设计

```php
// app/common/service/WorkflowService.php
<?php

namespace app\common\service;

class WorkflowService
{
    /**
     * 贷款申请工作流配置
     */
    const LOAN_WORKFLOW = [
        'states' => [
            'pending' => '待审核',
            'risk_review' => '风控审核',
            'manager_review' => '经理审核',
            'final_review' => '终审',
            'approved' => '已批准',
            'rejected' => '已拒绝',
            'disbursed' => '已放款'
        ],
        'transitions' => [
            'pending' => ['risk_review', 'rejected'],
            'risk_review' => ['manager_review', 'rejected'],
            'manager_review' => ['final_review', 'approved', 'rejected'],
            'final_review' => ['approved', 'rejected'],
            'approved' => ['disbursed'],
            'rejected' => [],
            'disbursed' => []
        ],
        'rules' => [
            'risk_review' => [
                'condition' => 'risk_score >= 60',
                'auto_approve' => 'risk_score >= 80',
                'assignees' => ['risk_reviewer']
            ],
            'manager_review' => [
                'condition' => 'apply_amount <= 100000',
                'assignees' => ['manager']
            ],
            'final_review' => [
                'condition' => 'apply_amount > 100000',
                'assignees' => ['director']
            ]
        ]
    ];
    
    /**
     * 启动工作流
     */
    public static function startWorkflow($type, $businessId, $data = [])
    {
        $workflow = self::getWorkflowConfig($type);
        $initialState = array_keys($workflow['states'])[0];
        
        // 创建工作流实例
        $instance = WorkflowInstance::create([
            'workflow_type' => $type,
            'business_id' => $businessId,
            'current_state' => $initialState,
            'workflow_data' => json_encode($data),
            'status' => 'running'
        ]);
        
        // 执行初始状态
        self::executeState($instance, $initialState);
        
        return $instance;
    }
    
    /**
     * 流转到下一状态
     */
    public static function transition($instanceId, $toState, $userId, $comment = '')
    {
        $instance = WorkflowInstance::find($instanceId);
        $workflow = self::getWorkflowConfig($instance->workflow_type);
        
        // 检查是否可以流转
        $allowedStates = $workflow['transitions'][$instance->current_state] ?? [];
        if (!in_array($toState, $allowedStates)) {
            throw new \Exception('不允许的状态流转');
        }
        
        // 检查权限
        if (!self::checkTransitionPermission($instance, $toState, $userId)) {
            throw new \Exception('没有权限进行此操作');
        }
        
        // 记录流转历史
        WorkflowHistory::create([
            'instance_id' => $instanceId,
            'from_state' => $instance->current_state,
            'to_state' => $toState,
            'user_id' => $userId,
            'comment' => $comment,
            'transition_time' => date('Y-m-d H:i:s')
        ]);
        
        // 更新状态
        $instance->current_state = $toState;
        $instance->save();
        
        // 执行状态逻辑
        self::executeState($instance, $toState);
        
        return true;
    }
    
    /**
     * 执行状态逻辑
     */
    private static function executeState($instance, $state)
    {
        $workflow = self::getWorkflowConfig($instance->workflow_type);
        $rules = $workflow['rules'][$state] ?? [];
        
        // 检查自动流转条件
        if (isset($rules['auto_approve']) && self::evaluateCondition($rules['auto_approve'], $instance)) {
            $nextStates = $workflow['transitions'][$state] ?? [];
            if ($nextStates) {
                self::transition($instance->id, $nextStates[0], 0, '自动流转');
            }
        } else {
            // 分配任务
            if (isset($rules['assignees'])) {
                self::assignTasks($instance, $rules['assignees']);
            }
        }
    }
    
    /**
     * 分配任务
     */
    private static function assignTasks($instance, $assignees)
    {
        foreach ($assignees as $assignee) {
            $users = self::getAssigneeUsers($assignee);
            foreach ($users as $userId) {
                WorkflowTask::create([
                    'instance_id' => $instance->id,
                    'user_id' => $userId,
                    'task_name' => $instance->workflow_type . '审核',
                    'status' => 'pending',
                    'assigned_time' => date('Y-m-d H:i:s')
                ]);
            }
        }
    }
}
```

### 4. 消息通知系统

```php
// app/common/service/NotificationService.php
<?php

namespace app\common\service;

class NotificationService
{
    /**
     * 发送通知
     */
    public static function send($type, $recipients, $template, $data = [])
    {
        $notification = Notification::create([
            'title' => $template['title'],
            'content' => self::renderTemplate($template['content'], $data),
            'type' => 'business',
            'send_type' => $type,
            'target_type' => 'custom',
            'target_users' => json_encode($recipients),
            'template_id' => $template['id'] ?? '',
            'template_params' => json_encode($data),
            'status' => 'sending'
        ]);
        
        switch ($type) {
            case 'sms':
                return self::sendSMS($notification, $recipients, $data);
            case 'email':
                return self::sendEmail($notification, $recipients, $data);
            case 'app_push':
                return self::sendAppPush($notification, $recipients, $data);
            case 'system_msg':
                return self::sendSystemMessage($notification, $recipients, $data);
        }
        
        return false;
    }
    
    /**
     * 发送短信
     */
    private static function sendSMS($notification, $recipients, $data)
    {
        $successCount = 0;
        
        foreach ($recipients as $phone) {
            try {
                // 调用短信服务商API
                $result = SMSProvider::send($phone, $notification->content);
                if ($result) {
                    $successCount++;
                }
                
                // 记录发送日志
                NotificationLog::create([
                    'notification_id' => $notification->id,
                    'recipient' => $phone,
                    'send_result' => $result ? 'success' : 'failed',
                    'response_data' => json_encode($result)
                ]);
                
            } catch (\Exception $e) {
                LogService::error('SMS发送失败', ['phone' => $phone, 'error' => $e->getMessage()]);
            }
        }
        
        $notification->update([
            'sent_count' => count($recipients),
            'success_count' => $successCount,
            'status' => $successCount > 0 ? 'sent' : 'failed',
            'send_time' => date('Y-m-d H:i:s')
        ]);
        
        return $successCount;
    }
    
    /**
     * 发送邮件
     */
    private static function sendEmail($notification, $recipients, $data)
    {
        $successCount = 0;
        
        foreach ($recipients as $email) {
            try {
                $result = EmailProvider::send($email, $notification->title, $notification->content);
                if ($result) {
                    $successCount++;
                }
                
                NotificationLog::create([
                    'notification_id' => $notification->id,
                    'recipient' => $email,
                    'send_result' => $result ? 'success' : 'failed'
                ]);
                
            } catch (\Exception $e) {
                LogService::error('邮件发送失败', ['email' => $email, 'error' => $e->getMessage()]);
            }
        }
        
        $notification->update([
            'sent_count' => count($recipients),
            'success_count' => $successCount,
            'status' => $successCount > 0 ? 'sent' : 'failed',
            'send_time' => date('Y-m-d H:i:s')
        ]);
        
        return $successCount;
    }
    
    /**
     * 消息模板
     */
    const TEMPLATES = [
        'loan_approved' => [
            'title' => '贷款申请通过通知',
            'content' => '恭喜您！您的贷款申请已通过审核，申请金额：{amount}元，请及时关注放款进度。'
        ],
        'loan_rejected' => [
            'title' => '贷款申请拒绝通知',
            'content' => '很抱歉，您的贷款申请未通过审核，拒绝原因：{reason}。如有疑问请联系客服。'
        ],
        'repayment_reminder' => [
            'title' => '还款提醒',
            'content' => '您有一笔贷款即将到期，应还金额：{amount}元，到期时间：{due_date}，请及时还款。'
        ],
        'overdue_notice' => [
            'title' => '逾期通知',
            'content' => '您的贷款已逾期，逾期金额：{amount}元，逾期天数：{days}天，请立即还款。'
        ]
    ];
}
```

## 三、性能优化方案

### 1. 数据库优化

```sql
-- 创建索引优化
-- 复合索引
CREATE INDEX idx_loan_status_time ON loan_applications(status, submit_time);
CREATE INDEX idx_customer_risk_status ON customers(risk_level, status);
CREATE INDEX idx_collection_time_result ON collection_records(collection_time, contact_result);

-- 分区表（按时间分区）
ALTER TABLE operation_logs PARTITION BY RANGE (YEAR(created_at)) (
    PARTITION p2023 VALUES LESS THAN (2024),
    PARTITION p2024 VALUES LESS THAN (2025),
    PARTITION p2025 VALUES LESS THAN (2026),
    PARTITION p_future VALUES LESS THAN MAXVALUE
);

-- 数据库连接池配置
[database]
type = mysql
hostname = 127.0.0.1
database = daihou
username = root
password = 
hostport = 3306
charset = utf8mb4
debug = false
pool_size = 20
max_connections = 100
```

### 2. 缓存策略

```php
// app/common/service/CacheService.php
<?php

namespace app\common\service;

use think\facade\Cache;

class CacheService
{
    // 缓存键前缀
    const PREFIX = [
        'user' => 'user:',
        'customer' => 'customer:',
        'config' => 'config:',
        'permission' => 'permission:',
        'statistics' => 'statistics:'
    ];
    
    // 缓存时间
    const TTL = [
        'short' => 300,    // 5分钟
        'medium' => 1800,  // 30分钟
        'long' => 3600,    // 1小时
        'day' => 86400     // 1天
    ];
    
    /**
     * 获取客户信息（带缓存）
     */
    public static function getCustomer($customerId)
    {
        $key = self::PREFIX['customer'] . $customerId;
        
        return Cache::remember($key, function() use ($customerId) {
            return Customer::find($customerId);
        }, self::TTL['medium']);
    }
    
    /**
     * 获取统计数据（带缓存）
     */
    public static function getStatistics($type, $date = null)
    {
        $date = $date ?: date('Y-m-d');
        $key = self::PREFIX['statistics'] . $type . ':' . $date;
        
        return Cache::remember($key, function() use ($type, $date) {
            return StatisticsService::calculate($type, $date);
        }, self::TTL['long']);
    }
    
    /**
     * 清除相关缓存
     */
    public static function clearRelated($type, $id)
    {
        $patterns = [
            'customer' => [
                self::PREFIX['customer'] . $id,
                self::PREFIX['statistics'] . 'customer:*'
            ],
            'loan' => [
                self::PREFIX['statistics'] . 'loan:*',
                self::PREFIX['statistics'] . 'daily:*'
            ]
        ];
        
        if (isset($patterns[$type])) {
            foreach ($patterns[$type] as $pattern) {
                if (strpos($pattern, '*') !== false) {
                    Cache::clear(str_replace('*', '', $pattern));
                } else {
                    Cache::delete($pattern);
                }
            }
        }
    }
}
```

### 3. API性能优化

```php
// app/common/middleware/ApiOptimize.php
<?php

namespace app\common\middleware;

class ApiOptimize
{
    public function handle($request, \Closure $next)
    {
        $startTime = microtime(true);
        $startMemory = memory_get_usage();
        
        $response = $next($request);
        
        // 记录性能指标
        $executionTime = round((microtime(true) - $startTime) * 1000, 2);
        $memoryUsage = memory_get_usage() - $startMemory;
        
        // 添加性能头
        $response->header([
            'X-Execution-Time' => $executionTime . 'ms',
            'X-Memory-Usage' => $this->formatBytes($memoryUsage),
            'X-Response-Time' => date('Y-m-d H:i:s')
        ]);
        
        // 记录慢查询
        if ($executionTime > 1000) {
            LogService::warning('慢接口', [
                'url' => $request->url(),
                'method' => $request->method(),
                'execution_time' => $executionTime,
                'memory_usage' => $memoryUsage
            ]);
        }
        
        return $response;
    }
    
    private function formatBytes($bytes)
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);
        
        $bytes /= pow(1024, $pow);
        
        return round($bytes, 2) . ' ' . $units[$pow];
    }
}
```

## 四、安全性增强

### 1. 数据加密

```php
// app/common/service/EncryptService.php
<?php

namespace app\common\service;

class EncryptService
{
    private static $key = 'your-encryption-key-here';
    
    /**
     * 加密敏感数据
     */
    public static function encrypt($data)
    {
        $iv = openssl_random_pseudo_bytes(16);
        $encrypted = openssl_encrypt($data, 'AES-256-CBC', self::$key, 0, $iv);
        return base64_encode($iv . $encrypted);
    }
    
    /**
     * 解密敏感数据
     */
    public static function decrypt($encryptedData)
    {
        $data = base64_decode($encryptedData);
        $iv = substr($data, 0, 16);
        $encrypted = substr($data, 16);
        return openssl_decrypt($encrypted, 'AES-256-CBC', self::$key, 0, $iv);
    }
    
    /**
     * 加密身份证号
     */
    public static function encryptIdCard($idCard)
    {
        return self::encrypt($idCard);
    }
    
    /**
     * 解密身份证号
     */
    public static function decryptIdCard($encryptedIdCard)
    {
        return self::decrypt($encryptedIdCard);
    }
    
    /**
     * 掩码显示身份证号
     */
    public static function maskIdCard($idCard)
    {
        if (strlen($idCard) == 18) {
            return substr($idCard, 0, 6) . '********' . substr($idCard, -4);
        }
        return $idCard;
    }
    
    /**
     * 掩码显示手机号
     */
    public static function maskPhone($phone)
    {
        if (strlen($phone) == 11) {
            return substr($phone, 0, 3) . '****' . substr($phone, -4);
        }
        return $phone;
    }
}
```

### 2. 访问控制

```php
// app/common/middleware/SecurityControl.php
<?php

namespace app\common\middleware;

class SecurityControl
{
    public function handle($request, \Closure $next)
    {
        // IP白名单检查
        if (!$this->checkIpWhitelist($request->ip())) {
            return json(['code' => 403, 'message' => '访问被拒绝']);
        }
        
        // 请求频率限制
        if (!$this->checkRateLimit($request)) {
            return json(['code' => 429, 'message' => '请求过于频繁']);
        }
        
        // SQL注入检查
        if ($this->detectSqlInjection($request)) {
            LogService::warning('SQL注入尝试', [
                'ip' => $request->ip(),
                'url' => $request->url(),
                'params' => $request->param()
            ]);
            return json(['code' => 400, 'message' => '非法请求']);
        }
        
        return $next($request);
    }
    
    /**
     * 检查IP白名单
     */
    private function checkIpWhitelist($ip)
    {
        $whitelist = config('security.ip_whitelist', []);
        if (empty($whitelist)) {
            return true;
        }
        
        foreach ($whitelist as $allowedIp) {
            if ($this->ipInRange($ip, $allowedIp)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 检查请求频率
     */
    private function checkRateLimit($request)
    {
        $key = 'rate_limit:' . $request->ip();
        $current = Cache::get($key, 0);
        
        if ($current >= 100) { // 每分钟最多100次请求
            return false;
        }
        
        Cache::set($key, $current + 1, 60);
        return true;
    }
    
    /**
     * 检测SQL注入
     */
    private function detectSqlInjection($request)
    {
        $patterns = [
            '/union.*select/i',
            '/select.*from/i',
            '/insert.*into/i',
            '/delete.*from/i',
            '/update.*set/i',
            '/drop.*table/i',
            '/exec.*\(/i',
            '/script.*>/i'
        ];
        
        $params = json_encode($request->param());
        
        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $params)) {
                return true;
            }
        }
        
        return false;
    }
}
```
