/**
 * 前端应用统一配置管理
 * 用于所有前端应用的配置统一管理
 */

// 环境配置
const ENV_CONFIG = {
  development: {
    API_BASE_URL: 'http://localhost:8000',
    WS_BASE_URL: 'ws://localhost:8001',
    DEBUG: true,
    LOG_LEVEL: 'debug'
  },
  production: {
    API_BASE_URL: 'https://appai.gqc.pub',
    WS_BASE_URL: 'wss://appai.gqc.pub',
    DEBUG: false,
    LOG_LEVEL: 'error'
  },
  test: {
    API_BASE_URL: 'https://test-api.gqc.pub',
    WS_BASE_URL: 'wss://test-api.gqc.pub',
    DEBUG: true,
    LOG_LEVEL: 'info'
  }
}

// 应用配置
const APP_CONFIG = {
  // 应用信息
  APP_NAME: '民间空放贷后管理系统',
  APP_VERSION: '2.0.0',
  APP_DESCRIPTION: '专业的民间借贷管理平台',
  
  // 公司信息
  COMPANY_NAME: '金融科技有限公司',
  COMPANY_LOGO: '/static/images/logo.png',
  COMPANY_FAVICON: '/static/images/favicon.ico',
  
  // 功能开关
  FEATURES: {
    AI_RISK_ASSESSMENT: true,
    REAL_TIME_MONITORING: true,
    MULTI_LANGUAGE: false,
    DARK_MODE: true,
    MOBILE_RESPONSIVE: true,
    PWA_SUPPORT: true
  },
  
  // 页面配置
  PAGE_SIZE: 20,
  MAX_PAGE_SIZE: 100,
  
  // 文件上传配置
  UPLOAD: {
    MAX_FILE_SIZE: 10 * 1024 * 1024, // 10MB
    ALLOWED_TYPES: ['image/jpeg', 'image/png', 'image/gif', 'application/pdf'],
    CHUNK_SIZE: 1024 * 1024 // 1MB
  },
  
  // 缓存配置
  CACHE: {
    TOKEN_KEY: 'auth_token',
    USER_INFO_KEY: 'user_info',
    SETTINGS_KEY: 'app_settings',
    EXPIRE_TIME: 7 * 24 * 60 * 60 * 1000 // 7天
  },
  
  // 主题配置
  THEME: {
    PRIMARY_COLOR: '#1890ff',
    SUCCESS_COLOR: '#52c41a',
    WARNING_COLOR: '#faad14',
    ERROR_COLOR: '#ff4d4f',
    INFO_COLOR: '#1890ff'
  }
}

// API端点配置
const API_ENDPOINTS = {
  // 认证相关
  AUTH: {
    LOGIN: '/auth/login',
    LOGOUT: '/auth/logout',
    REFRESH: '/auth/refresh',
    PROFILE: '/auth/profile',
    CHANGE_PASSWORD: '/auth/change-password'
  },
  
  // 客户管理
  CUSTOMER: {
    LIST: '/customer/list',
    DETAIL: '/customer/detail',
    CREATE: '/customer/create',
    UPDATE: '/customer/update',
    DELETE: '/customer/delete',
    EXPORT: '/customer/export'
  },
  
  // 借款管理
  LOAN: {
    LIST: '/loan/list',
    DETAIL: '/loan/detail',
    CREATE: '/loan/create',
    UPDATE: '/loan/update',
    APPROVE: '/loan/approve',
    REJECT: '/loan/reject',
    DISBURSE: '/loan/disburse'
  },
  
  // 还款管理
  REPAYMENT: {
    LIST: '/repayment/list',
    DETAIL: '/repayment/detail',
    CREATE: '/repayment/create',
    CONFIRM: '/repayment/confirm',
    SCHEDULE: '/repayment/schedule'
  },
  
  // 风控管理
  RISK: {
    ASSESSMENT: '/risk/assessment',
    RULES: '/risk/rules',
    BLACKLIST: '/risk/blacklist',
    AI_EVALUATE: '/risk/ai-evaluate',
    REPORT: '/risk/report'
  },
  
  // 用户管理
  USER: {
    LIST: '/user/list',
    DETAIL: '/user/detail',
    CREATE: '/user/create',
    UPDATE: '/user/update',
    DELETE: '/user/delete',
    ROLES: '/user/roles',
    PERMISSIONS: '/user/permissions'
  },
  
  // 报表统计
  REPORT: {
    DASHBOARD: '/report/dashboard',
    FINANCIAL: '/report/financial',
    RISK: '/report/risk',
    PERFORMANCE: '/report/performance',
    EXPORT: '/report/export'
  },
  
  // 系统配置
  SYSTEM: {
    CONFIG: '/system/config',
    LOGS: '/system/logs',
    BACKUP: '/system/backup',
    MONITOR: '/system/monitor'
  }
}

// 路由配置
const ROUTE_CONFIG = {
  // 管理后台路由
  ADMIN: {
    LOGIN: '/login',
    DASHBOARD: '/dashboard',
    CUSTOMERS: '/customers',
    LOANS: '/loans',
    REPAYMENTS: '/repayments',
    RISK: '/risk',
    USERS: '/users',
    REPORTS: '/reports',
    SETTINGS: '/settings'
  },
  
  // 客户端路由
  CUSTOMER: {
    HOME: '/pages/index/index',
    LOGIN: '/pages/login/login',
    APPLY: '/pages/apply/apply',
    LOANS: '/pages/loan/list',
    REPAYMENT: '/pages/repayment/list',
    PROFILE: '/pages/profile/profile'
  },
  
  // 代理端路由
  AGENT: {
    HOME: '/pages/index/index',
    LOGIN: '/pages/login/login',
    CUSTOMERS: '/pages/customer/list',
    PERFORMANCE: '/pages/performance/index',
    COMMISSION: '/pages/commission/index'
  },
  
  // 投资人端路由
  INVESTOR: {
    HOME: '/pages/index/index',
    LOGIN: '/pages/login/login',
    INVESTMENTS: '/pages/investment/list',
    RETURNS: '/pages/return/list',
    REPORTS: '/pages/report/index'
  },
  
  // 催收端路由
  COLLECTION: {
    HOME: '/pages/index/index',
    LOGIN: '/pages/login/login',
    CASES: '/pages/case/list',
    RECORDS: '/pages/record/list',
    PERFORMANCE: '/pages/performance/index'
  }
}

// 状态码配置
const STATUS_CODES = {
  SUCCESS: 200,
  CREATED: 201,
  NO_CONTENT: 204,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  INTERNAL_ERROR: 500,
  SERVICE_UNAVAILABLE: 503
}

// 业务状态配置
const BUSINESS_STATUS = {
  // 借款状态
  LOAN_STATUS: {
    PENDING: { value: 'pending', label: '待审核', color: 'warning' },
    APPROVED: { value: 'approved', label: '已批准', color: 'success' },
    REJECTED: { value: 'rejected', label: '已拒绝', color: 'error' },
    DISBURSED: { value: 'disbursed', label: '已放款', color: 'info' },
    COMPLETED: { value: 'completed', label: '已完成', color: 'success' },
    OVERDUE: { value: 'overdue', label: '已逾期', color: 'error' }
  },
  
  // 还款状态
  REPAYMENT_STATUS: {
    PENDING: { value: 'pending', label: '待还款', color: 'warning' },
    PAID: { value: 'paid', label: '已还款', color: 'success' },
    OVERDUE: { value: 'overdue', label: '已逾期', color: 'error' },
    PARTIAL: { value: 'partial', label: '部分还款', color: 'info' }
  },
  
  // 风险等级
  RISK_LEVEL: {
    LOW: { value: 'low', label: '低风险', color: 'success' },
    MEDIUM: { value: 'medium', label: '中风险', color: 'warning' },
    HIGH: { value: 'high', label: '高风险', color: 'error' },
    VERY_HIGH: { value: 'very_high', label: '极高风险', color: 'error' }
  },
  
  // 用户状态
  USER_STATUS: {
    ACTIVE: { value: 'active', label: '正常', color: 'success' },
    INACTIVE: { value: 'inactive', label: '禁用', color: 'error' },
    PENDING: { value: 'pending', label: '待激活', color: 'warning' }
  }
}

// 工具函数
const UTILS = {
  // 获取当前环境配置
  getEnvConfig() {
    const env = process.env.NODE_ENV || 'development'
    return ENV_CONFIG[env] || ENV_CONFIG.development
  },
  
  // 格式化金额
  formatMoney(amount, currency = '¥') {
    if (typeof amount !== 'number') return currency + '0.00'
    return currency + amount.toLocaleString('zh-CN', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    })
  },
  
  // 格式化日期
  formatDate(date, format = 'YYYY-MM-DD HH:mm:ss') {
    if (!date) return '-'
    const d = new Date(date)
    const year = d.getFullYear()
    const month = String(d.getMonth() + 1).padStart(2, '0')
    const day = String(d.getDate()).padStart(2, '0')
    const hour = String(d.getHours()).padStart(2, '0')
    const minute = String(d.getMinutes()).padStart(2, '0')
    const second = String(d.getSeconds()).padStart(2, '0')
    
    return format
      .replace('YYYY', year)
      .replace('MM', month)
      .replace('DD', day)
      .replace('HH', hour)
      .replace('mm', minute)
      .replace('ss', second)
  },
  
  // 获取状态配置
  getStatusConfig(type, value) {
    const statusMap = BUSINESS_STATUS[type]
    if (!statusMap) return { label: value, color: 'default' }
    
    for (const key in statusMap) {
      if (statusMap[key].value === value) {
        return statusMap[key]
      }
    }
    
    return { label: value, color: 'default' }
  },
  
  // 存储管理
  storage: {
    set(key, value, expire) {
      const data = {
        value,
        expire: expire ? Date.now() + expire : null
      }
      localStorage.setItem(key, JSON.stringify(data))
    },
    
    get(key) {
      try {
        const item = localStorage.getItem(key)
        if (!item) return null
        
        const data = JSON.parse(item)
        if (data.expire && Date.now() > data.expire) {
          localStorage.removeItem(key)
          return null
        }
        
        return data.value
      } catch (e) {
        return null
      }
    },
    
    remove(key) {
      localStorage.removeItem(key)
    },
    
    clear() {
      localStorage.clear()
    }
  }
}

// 导出配置
export {
  ENV_CONFIG,
  APP_CONFIG,
  API_ENDPOINTS,
  ROUTE_CONFIG,
  STATUS_CODES,
  BUSINESS_STATUS,
  UTILS
}

// 默认导出
export default {
  ENV_CONFIG,
  APP_CONFIG,
  API_ENDPOINTS,
  ROUTE_CONFIG,
  STATUS_CODES,
  BUSINESS_STATUS,
  UTILS
}
