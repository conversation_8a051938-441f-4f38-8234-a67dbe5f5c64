<?php
declare (strict_types = 1);

namespace app\common\model;

use think\Model;

class MiniprogramPage extends Model
{
    protected $name = 'miniprogram_pages';

    protected $schema = [
        'id'           => 'int',
        'page_name'    => 'string',
        'page_path'    => 'string',
        'page_config'  => 'json',
        'components'   => 'json',
        'is_published' => 'int',
        'created_by'   => 'int',
        'created_at'   => 'datetime',
        'updated_at'   => 'datetime',
    ];

    protected $json = ['page_config', 'components'];
}


