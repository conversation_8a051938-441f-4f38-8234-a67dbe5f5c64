<?php
declare (strict_types = 1);

namespace app\common\model;

use think\Model;

/**
 * 放款登记记录模型
 */
class LoanDisbursementRecord extends Model
{
    protected $name = 'loan_disbursement_records';
    
    // 设置字段信息
    protected $schema = [
        'id'                        => 'int',
        'business_flow_no'          => 'string',
        'loan_date'                 => 'date',
        'customer_name'             => 'string',
        'customer_id'               => 'int',
        'risk_controller_name'      => 'string',
        'risk_controller_id'        => 'int',
        'loan_amount'               => 'float',
        'repayment_type'            => 'string',
        'repayment_cycle'           => 'int',
        'repayment_frequency'       => 'string',
        'agent_id'                  => 'int',
        'agent_name'                => 'string',
        'agent_commission_rate'     => 'float',
        'agent_commission_amount'   => 'float',
        'commission_type'           => 'string',
        'front_commission_amount'   => 'float',
        'back_commission_amount'    => 'float',
        'back_commission_paid'      => 'int',
        'platform_fee'              => 'float',
        'overdue_fee'               => 'float',
        'total_repaid_amount'       => 'float',
        'remaining_amount'          => 'float',
        'customer_profit'           => 'float',
        'status'                    => 'string',
        'created_by'                => 'int',
        'created_at'                => 'datetime',
        'updated_at'                => 'datetime',
    ];

    // 只读字段
    protected $readonly = ['business_flow_no', 'created_at'];

    /**
     * 关联客户
     */
    public function customer()
    {
        return $this->belongsTo(Customer::class, 'customer_id');
    }

    /**
     * 关联还款计划
     */
    public function repaymentSchedule()
    {
        return $this->hasMany(RepaymentScheduleDetail::class, 'disbursement_record_id');
    }

    /**
     * 关联还款记录
     */
    public function repaymentRecords()
    {
        return $this->hasMany(RepaymentRecord::class, 'disbursement_record_id');
    }

    /**
     * 关联创建人
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * 获取还款进度百分比
     */
    public function getRepaymentProgressAttr(): float
    {
        if ($this->loan_amount <= 0) {
            return 0;
        }
        return round(($this->total_repaid_amount / $this->loan_amount) * 100, 2);
    }

    /**
     * 获取剩余天数
     */
    public function getRemainingDaysAttr(): int
    {
        $lastSchedule = $this->repaymentSchedule()
            ->where('status', 'pending')
            ->order('due_date', 'desc')
            ->find();
        
        if (!$lastSchedule) {
            return 0;
        }
        
        $today = strtotime(date('Y-m-d'));
        $dueDate = strtotime($lastSchedule->due_date);
        
        return max(0, ceil(($dueDate - $today) / 86400));
    }

    /**
     * 检查是否逾期
     */
    public function isOverdue(): bool
    {
        $overdueSchedule = $this->repaymentSchedule()
            ->where('status', 'pending')
            ->where('due_date', '<', date('Y-m-d'))
            ->find();
        
        return !empty($overdueSchedule);
    }

    /**
     * 获取逾期天数
     */
    public function getOverdueDays(): int
    {
        $overdueSchedule = $this->repaymentSchedule()
            ->where('status', 'pending')
            ->where('due_date', '<', date('Y-m-d'))
            ->order('due_date', 'asc')
            ->find();
        
        if (!$overdueSchedule) {
            return 0;
        }
        
        $today = strtotime(date('Y-m-d'));
        $dueDate = strtotime($overdueSchedule->due_date);
        
        return max(0, ceil(($today - $dueDate) / 86400));
    }

    /**
     * 计算当前利润率
     */
    public function getProfitRateAttr(): float
    {
        if ($this->loan_amount <= 0) {
            return 0;
        }
        return round(($this->customer_profit / $this->loan_amount) * 100, 2);
    }
}
