<?php
declare(strict_types=1);

namespace app\common\service;

use think\facade\Cache;
use think\facade\Log;

/**
 * 统一缓存管理服务
 */
class CacheService
{
    /**
     * 缓存前缀配置
     */
    const PREFIXES = [
        'user' => 'user:',
        'config' => 'config:',
        'permission' => 'perm:',
        'token' => 'token:',
        'api' => 'api:',
        'data' => 'data:',
        'temp' => 'temp:'
    ];

    /**
     * 默认缓存时间（秒）
     */
    const DEFAULT_EXPIRE = 3600;

    /**
     * 获取缓存
     * @param string $key 缓存键
     * @param string $prefix 前缀类型
     * @return mixed
     */
    public static function get(string $key, string $prefix = 'data')
    {
        try {
            $cacheKey = self::buildKey($key, $prefix);
            return Cache::get($cacheKey);
        } catch (\Exception $e) {
            Log::error('获取缓存失败', ['key' => $key, 'error' => $e->getMessage()]);
            return null;
        }
    }

    /**
     * 设置缓存
     * @param string $key 缓存键
     * @param mixed $value 缓存值
     * @param int $expire 过期时间（秒）
     * @param string $prefix 前缀类型
     * @return bool
     */
    public static function set(string $key, $value, int $expire = self::DEFAULT_EXPIRE, string $prefix = 'data'): bool
    {
        try {
            $cacheKey = self::buildKey($key, $prefix);
            return Cache::set($cacheKey, $value, $expire);
        } catch (\Exception $e) {
            Log::error('设置缓存失败', ['key' => $key, 'error' => $e->getMessage()]);
            return false;
        }
    }

    /**
     * 删除缓存
     * @param string $key 缓存键
     * @param string $prefix 前缀类型
     * @return bool
     */
    public static function delete(string $key, string $prefix = 'data'): bool
    {
        try {
            $cacheKey = self::buildKey($key, $prefix);
            return Cache::delete($cacheKey);
        } catch (\Exception $e) {
            Log::error('删除缓存失败', ['key' => $key, 'error' => $e->getMessage()]);
            return false;
        }
    }

    /**
     * 检查缓存是否存在
     * @param string $key 缓存键
     * @param string $prefix 前缀类型
     * @return bool
     */
    public static function has(string $key, string $prefix = 'data'): bool
    {
        try {
            $cacheKey = self::buildKey($key, $prefix);
            return Cache::has($cacheKey);
        } catch (\Exception $e) {
            Log::error('检查缓存失败', ['key' => $key, 'error' => $e->getMessage()]);
            return false;
        }
    }

    /**
     * 获取或设置缓存（如果不存在则通过回调获取并缓存）
     * @param string $key 缓存键
     * @param callable $callback 回调函数
     * @param int $expire 过期时间（秒）
     * @param string $prefix 前缀类型
     * @return mixed
     */
    public static function remember(string $key, callable $callback, int $expire = self::DEFAULT_EXPIRE, string $prefix = 'data')
    {
        $value = self::get($key, $prefix);
        
        if ($value === null) {
            $value = $callback();
            if ($value !== null) {
                self::set($key, $value, $expire, $prefix);
            }
        }
        
        return $value;
    }

    /**
     * 批量获取缓存
     * @param array $keys 缓存键数组
     * @param string $prefix 前缀类型
     * @return array
     */
    public static function getMultiple(array $keys, string $prefix = 'data'): array
    {
        $result = [];
        foreach ($keys as $key) {
            $result[$key] = self::get($key, $prefix);
        }
        return $result;
    }

    /**
     * 批量设置缓存
     * @param array $values 键值对数组
     * @param int $expire 过期时间（秒）
     * @param string $prefix 前缀类型
     * @return bool
     */
    public static function setMultiple(array $values, int $expire = self::DEFAULT_EXPIRE, string $prefix = 'data'): bool
    {
        $success = true;
        foreach ($values as $key => $value) {
            if (!self::set($key, $value, $expire, $prefix)) {
                $success = false;
            }
        }
        return $success;
    }

    /**
     * 批量删除缓存
     * @param array $keys 缓存键数组
     * @param string $prefix 前缀类型
     * @return bool
     */
    public static function deleteMultiple(array $keys, string $prefix = 'data'): bool
    {
        $success = true;
        foreach ($keys as $key) {
            if (!self::delete($key, $prefix)) {
                $success = false;
            }
        }
        return $success;
    }

    /**
     * 清除指定前缀的所有缓存
     * @param string $prefix 前缀类型
     * @return bool
     */
    public static function clearByPrefix(string $prefix = 'data'): bool
    {
        try {
            $pattern = self::PREFIXES[$prefix] ?? $prefix . ':';
            return Cache::clear($pattern . '*');
        } catch (\Exception $e) {
            Log::error('清除缓存失败', ['prefix' => $prefix, 'error' => $e->getMessage()]);
            return false;
        }
    }

    /**
     * 增加缓存值（仅适用于数值）
     * @param string $key 缓存键
     * @param int $step 增加步长
     * @param string $prefix 前缀类型
     * @return int|false
     */
    public static function increment(string $key, int $step = 1, string $prefix = 'data')
    {
        try {
            $cacheKey = self::buildKey($key, $prefix);
            return Cache::inc($cacheKey, $step);
        } catch (\Exception $e) {
            Log::error('缓存自增失败', ['key' => $key, 'error' => $e->getMessage()]);
            return false;
        }
    }

    /**
     * 减少缓存值（仅适用于数值）
     * @param string $key 缓存键
     * @param int $step 减少步长
     * @param string $prefix 前缀类型
     * @return int|false
     */
    public static function decrement(string $key, int $step = 1, string $prefix = 'data')
    {
        try {
            $cacheKey = self::buildKey($key, $prefix);
            return Cache::dec($cacheKey, $step);
        } catch (\Exception $e) {
            Log::error('缓存自减失败', ['key' => $key, 'error' => $e->getMessage()]);
            return false;
        }
    }

    /**
     * 获取缓存统计信息
     * @return array
     */
    public static function getStats(): array
    {
        try {
            return [
                'driver' => config('cache.default'),
                'prefixes' => array_keys(self::PREFIXES),
                'default_expire' => self::DEFAULT_EXPIRE
            ];
        } catch (\Exception $e) {
            Log::error('获取缓存统计失败', ['error' => $e->getMessage()]);
            return [];
        }
    }

    /**
     * 构建缓存键
     * @param string $key 原始键
     * @param string $prefix 前缀类型
     * @return string
     */
    private static function buildKey(string $key, string $prefix): string
    {
        $prefixStr = self::PREFIXES[$prefix] ?? $prefix . ':';
        return $prefixStr . $key;
    }

    /**
     * 用户相关缓存操作
     */
    public static function user(): UserCache
    {
        return new UserCache();
    }

    /**
     * Token相关缓存操作
     */
    public static function token(): TokenCache
    {
        return new TokenCache();
    }

    /**
     * API相关缓存操作
     */
    public static function api(): ApiCache
    {
        return new ApiCache();
    }
}

/**
 * 用户缓存操作类
 */
class UserCache
{
    public function get(int $userId)
    {
        return CacheService::get((string)$userId, 'user');
    }

    public function set(int $userId, array $userData, int $expire = 3600): bool
    {
        return CacheService::set((string)$userId, $userData, $expire, 'user');
    }

    public function delete(int $userId): bool
    {
        return CacheService::delete((string)$userId, 'user');
    }

    public function clear(): bool
    {
        return CacheService::clearByPrefix('user');
    }
}

/**
 * Token缓存操作类
 */
class TokenCache
{
    public function get(string $token)
    {
        return CacheService::get($token, 'token');
    }

    public function set(string $token, array $tokenData, int $expire = 7200): bool
    {
        return CacheService::set($token, $tokenData, $expire, 'token');
    }

    public function delete(string $token): bool
    {
        return CacheService::delete($token, 'token');
    }

    public function clear(): bool
    {
        return CacheService::clearByPrefix('token');
    }
}

/**
 * API缓存操作类
 */
class ApiCache
{
    public function get(string $key)
    {
        return CacheService::get($key, 'api');
    }

    public function set(string $key, $data, int $expire = 300): bool
    {
        return CacheService::set($key, $data, $expire, 'api');
    }

    public function delete(string $key): bool
    {
        return CacheService::delete($key, 'api');
    }

    public function clear(): bool
    {
        return CacheService::clearByPrefix('api');
    }
}
