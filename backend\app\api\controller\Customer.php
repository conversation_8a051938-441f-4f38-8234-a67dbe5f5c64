<?php
declare (strict_types = 1);

namespace app\api\controller;

use app\common\model\Customer as CustomerModel;
use think\Request;
use think\Response;

class Customer
{
    /**
     * 获取客户列表
     *
     * @param Request $request
     * @return Response
     */
    public function index(Request $request): Response
    {
        try {
            $page = (int) $request->param('page', 1);
            $limit = (int) $request->param('limit', 20);
            $keyword = $request->param('keyword', '');
            $status = $request->param('status', '');

            $query = CustomerModel::order('created_at', 'desc');

            // 关键词搜索
            if ($keyword) {
                $query->where('name|phone|id_card', 'like', "%{$keyword}%");
            }

            // 状态筛选
            if ($status) {
                $query->where('status', $status);
            }

            $result = $query->paginate([
                'list_rows' => $limit,
                'page' => $page
            ]);

            return json([
                'code' => 200,
                'message' => '获取客户列表成功',
                'data' => [
                    'items' => $result->items(),
                    'pagination' => [
                        'page' => $page,
                        'limit' => $limit,
                        'total' => $result->total(),
                        'pages' => $result->lastPage()
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '获取客户列表失败：' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    /**
     * 搜索客户（自动补全）
     *
     * @param Request $request
     * @return Response
     */
    public function search(Request $request): Response
    {
        try {
            $keyword = $request->param('keyword', '');
            $limit = (int) $request->param('limit', 10);

            if (strlen($keyword) < 2) {
                return json([
                    'code' => 200,
                    'message' => '搜索成功',
                    'data' => []
                ]);
            }

            $customers = CustomerModel::where('name|phone|id_card', 'like', "%{$keyword}%")
                ->where('status', 'active')
                ->limit($limit)
                ->field('id,name,phone,id_card')
                ->select();

            // 掩码身份证号
            foreach ($customers as $customer) {
                $customer->id_card = substr($customer->id_card, 0, 6) . '****' . substr($customer->id_card, -4);
            }

            return json([
                'code' => 200,
                'message' => '搜索成功',
                'data' => $customers
            ]);

        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '搜索失败：' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    /**
     * 获取客户详情
     *
     * @param Request $request
     * @param int $id
     * @return Response
     */
    public function read(Request $request, int $id): Response
    {
        try {
            $customer = CustomerModel::find($id);

            if (!$customer) {
                return json([
                    'code' => 404,
                    'message' => '客户不存在',
                    'data' => null
                ], 404);
            }

            return json([
                'code' => 200,
                'message' => '获取客户详情成功',
                'data' => $customer
            ]);

        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '获取客户详情失败：' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    /**
     * 创建客户
     *
     * @param Request $request
     * @return Response
     */
    public function save(Request $request): Response
    {
        try {
            $data = $request->param();

            // 基础验证
            if (empty($data['name']) || empty($data['phone']) || empty($data['id_card'])) {
                return json([
                    'code' => 400,
                    'message' => '姓名、手机号和身份证号不能为空',
                    'data' => null
                ], 400);
            }

            // 检查手机号是否已存在
            if (CustomerModel::where('phone', $data['phone'])->find()) {
                return json([
                    'code' => 400,
                    'message' => '手机号已存在',
                    'data' => null
                ], 400);
            }

            // 检查身份证号是否已存在
            if (CustomerModel::where('id_card', $data['id_card'])->find()) {
                return json([
                    'code' => 400,
                    'message' => '身份证号已存在',
                    'data' => null
                ], 400);
            }

            $customer = CustomerModel::create($data);

            return json([
                'code' => 200,
                'message' => '创建客户成功',
                'data' => $customer
            ]);

        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '创建客户失败：' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    /**
     * 更新客户
     *
     * @param Request $request
     * @param int $id
     * @return Response
     */
    public function update(Request $request, int $id): Response
    {
        try {
            $customer = CustomerModel::find($id);

            if (!$customer) {
                return json([
                    'code' => 404,
                    'message' => '客户不存在',
                    'data' => null
                ], 404);
            }

            $data = $request->param();
            
            // 如果更新手机号，检查是否已存在
            if (isset($data['phone']) && $data['phone'] != $customer->phone) {
                if (CustomerModel::where('phone', $data['phone'])->find()) {
                    return json([
                        'code' => 400,
                        'message' => '手机号已存在',
                        'data' => null
                    ], 400);
                }
            }

            // 如果更新身份证号，检查是否已存在
            if (isset($data['id_card']) && $data['id_card'] != $customer->id_card) {
                if (CustomerModel::where('id_card', $data['id_card'])->find()) {
                    return json([
                        'code' => 400,
                        'message' => '身份证号已存在',
                        'data' => null
                    ], 400);
                }
            }

            $customer->save($data);

            return json([
                'code' => 200,
                'message' => '更新客户成功',
                'data' => $customer
            ]);

        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '更新客户失败：' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    /**
     * 删除客户
     *
     * @param Request $request
     * @param int $id
     * @return Response
     */
    public function delete(Request $request, int $id): Response
    {
        try {
            $customer = CustomerModel::find($id);

            if (!$customer) {
                return json([
                    'code' => 404,
                    'message' => '客户不存在',
                    'data' => null
                ], 404);
            }

            // 软删除：更改状态为suspended
            $customer->status = 'suspended';
            $customer->save();

            return json([
                'code' => 200,
                'message' => '删除客户成功',
                'data' => null
            ]);

        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '删除客户失败：' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }
}
