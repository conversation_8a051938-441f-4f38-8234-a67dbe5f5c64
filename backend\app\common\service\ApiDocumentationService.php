<?php
declare(strict_types=1);

namespace app\common\service;

use think\facade\Route;
use think\facade\Config;
use ReflectionClass;
use ReflectionMethod;

/**
 * API文档生成服务
 * 自动生成API接口文档
 */
class ApiDocumentationService
{
    /**
     * 生成完整的API文档
     * @return array
     */
    public static function generateFullDocumentation(): array
    {
        return [
            'info' => self::getApiInfo(),
            'servers' => self::getServers(),
            'paths' => self::generatePaths(),
            'components' => self::getComponents(),
            'tags' => self::getTags()
        ];
    }

    /**
     * 获取API基本信息
     * @return array
     */
    private static function getApiInfo(): array
    {
        return [
            'title' => '民间空放贷后管理系统 API',
            'description' => '提供完整的API接口文档，包含认证、客户管理、借款管理、还款管理、催收管理等模块',
            'version' => '2.0.0',
            'contact' => [
                'name' => 'API Support',
                'email' => '<EMAIL>',
                'url' => 'https://example.com/support'
            ],
            'license' => [
                'name' => 'MIT',
                'url' => 'https://opensource.org/licenses/MIT'
            ]
        ];
    }

    /**
     * 获取服务器信息
     * @return array
     */
    private static function getServers(): array
    {
        return [
            [
                'url' => 'http://localhost:8000',
                'description' => '开发环境'
            ],
            [
                'url' => 'https://test-api.example.com',
                'description' => '测试环境'
            ],
            [
                'url' => 'https://api.example.com',
                'description' => '生产环境'
            ]
        ];
    }

    /**
     * 生成API路径文档
     * @return array
     */
    private static function generatePaths(): array
    {
        $paths = [];
        
        // 定义API端点配置
        $apiEndpoints = [
            // 认证模块
            'auth' => [
                'POST /api/auth/login' => [
                    'summary' => '用户登录',
                    'description' => '用户登录获取访问令牌',
                    'tags' => ['认证管理'],
                    'requestBody' => [
                        'required' => true,
                        'content' => [
                            'application/json' => [
                                'schema' => [
                                    'type' => 'object',
                                    'required' => ['username', 'password'],
                                    'properties' => [
                                        'username' => ['type' => 'string', 'description' => '用户名'],
                                        'password' => ['type' => 'string', 'description' => '密码'],
                                        'remember' => ['type' => 'boolean', 'description' => '记住登录']
                                    ]
                                ]
                            ]
                        ]
                    ],
                    'responses' => [
                        '200' => [
                            'description' => '登录成功',
                            'content' => [
                                'application/json' => [
                                    'schema' => [
                                        '$ref' => '#/components/schemas/LoginResponse'
                                    ]
                                ]
                            ]
                        ],
                        '401' => ['$ref' => '#/components/responses/Unauthorized']
                    ]
                ],
                'POST /api/auth/logout' => [
                    'summary' => '用户登出',
                    'description' => '用户登出并使令牌失效',
                    'tags' => ['认证管理'],
                    'security' => [['bearerAuth' => []]],
                    'responses' => [
                        '200' => ['$ref' => '#/components/responses/Success']
                    ]
                ],
                'POST /api/auth/refresh' => [
                    'summary' => '刷新令牌',
                    'description' => '刷新访问令牌',
                    'tags' => ['认证管理'],
                    'security' => [['bearerAuth' => []]],
                    'responses' => [
                        '200' => [
                            'description' => '刷新成功',
                            'content' => [
                                'application/json' => [
                                    'schema' => [
                                        '$ref' => '#/components/schemas/TokenResponse'
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ],
            
            // 客户管理模块
            'customers' => [
                'GET /api/customers' => [
                    'summary' => '获取客户列表',
                    'description' => '分页获取客户列表',
                    'tags' => ['客户管理'],
                    'security' => [['bearerAuth' => []]],
                    'parameters' => [
                        [
                            'name' => 'page',
                            'in' => 'query',
                            'description' => '页码',
                            'schema' => ['type' => 'integer', 'default' => 1]
                        ],
                        [
                            'name' => 'limit',
                            'in' => 'query',
                            'description' => '每页数量',
                            'schema' => ['type' => 'integer', 'default' => 20]
                        ],
                        [
                            'name' => 'status',
                            'in' => 'query',
                            'description' => '客户状态',
                            'schema' => ['type' => 'string', 'enum' => ['active', 'inactive', 'pending']]
                        ],
                        [
                            'name' => 'risk_level',
                            'in' => 'query',
                            'description' => '风险等级',
                            'schema' => ['type' => 'string', 'enum' => ['low', 'medium', 'high', 'very_high']]
                        ]
                    ],
                    'responses' => [
                        '200' => [
                            'description' => '获取成功',
                            'content' => [
                                'application/json' => [
                                    'schema' => [
                                        '$ref' => '#/components/schemas/CustomerListResponse'
                                    ]
                                ]
                            ]
                        ]
                    ]
                ],
                'POST /api/customers' => [
                    'summary' => '创建客户',
                    'description' => '创建新客户',
                    'tags' => ['客户管理'],
                    'security' => [['bearerAuth' => []]],
                    'requestBody' => [
                        'required' => true,
                        'content' => [
                            'application/json' => [
                                'schema' => [
                                    '$ref' => '#/components/schemas/CustomerCreateRequest'
                                ]
                            ]
                        ]
                    ],
                    'responses' => [
                        '201' => [
                            'description' => '创建成功',
                            'content' => [
                                'application/json' => [
                                    'schema' => [
                                        '$ref' => '#/components/schemas/CustomerResponse'
                                    ]
                                ]
                            ]
                        ],
                        '422' => ['$ref' => '#/components/responses/ValidationError']
                    ]
                ],
                'GET /api/customers/{id}' => [
                    'summary' => '获取客户详情',
                    'description' => '根据ID获取客户详细信息',
                    'tags' => ['客户管理'],
                    'security' => [['bearerAuth' => []]],
                    'parameters' => [
                        [
                            'name' => 'id',
                            'in' => 'path',
                            'required' => true,
                            'description' => '客户ID',
                            'schema' => ['type' => 'integer']
                        ]
                    ],
                    'responses' => [
                        '200' => [
                            'description' => '获取成功',
                            'content' => [
                                'application/json' => [
                                    'schema' => [
                                        '$ref' => '#/components/schemas/CustomerResponse'
                                    ]
                                ]
                            ]
                        ],
                        '404' => ['$ref' => '#/components/responses/NotFound']
                    ]
                ]
            ],
            
            // 借款管理模块
            'loans' => [
                'GET /api/loans' => [
                    'summary' => '获取借款列表',
                    'description' => '分页获取借款申请列表',
                    'tags' => ['借款管理'],
                    'security' => [['bearerAuth' => []]],
                    'parameters' => [
                        [
                            'name' => 'status',
                            'in' => 'query',
                            'description' => '借款状态',
                            'schema' => ['type' => 'string', 'enum' => ['pending', 'approved', 'rejected', 'disbursed']]
                        ]
                    ],
                    'responses' => [
                        '200' => [
                            'description' => '获取成功',
                            'content' => [
                                'application/json' => [
                                    'schema' => [
                                        '$ref' => '#/components/schemas/LoanListResponse'
                                    ]
                                ]
                            ]
                        ]
                    ]
                ],
                'POST /api/loans' => [
                    'summary' => '创建借款申请',
                    'description' => '创建新的借款申请',
                    'tags' => ['借款管理'],
                    'security' => [['bearerAuth' => []]],
                    'requestBody' => [
                        'required' => true,
                        'content' => [
                            'application/json' => [
                                'schema' => [
                                    '$ref' => '#/components/schemas/LoanCreateRequest'
                                ]
                            ]
                        ]
                    ],
                    'responses' => [
                        '201' => [
                            'description' => '创建成功',
                            'content' => [
                                'application/json' => [
                                    'schema' => [
                                        '$ref' => '#/components/schemas/LoanResponse'
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ],
            
            // 风控管理模块
            'risk' => [
                'POST /api/risk/assessment' => [
                    'summary' => 'AI风险评估',
                    'description' => '对客户进行AI风险评估',
                    'tags' => ['风控管理'],
                    'security' => [['bearerAuth' => []]],
                    'requestBody' => [
                        'required' => true,
                        'content' => [
                            'application/json' => [
                                'schema' => [
                                    '$ref' => '#/components/schemas/RiskAssessmentRequest'
                                ]
                            ]
                        ]
                    ],
                    'responses' => [
                        '200' => [
                            'description' => '评估成功',
                            'content' => [
                                'application/json' => [
                                    'schema' => [
                                        '$ref' => '#/components/schemas/RiskAssessmentResponse'
                                    ]
                                ]
                            ]
                        ]
                    ]
                ]
            ]
        ];

        // 转换为OpenAPI格式
        foreach ($apiEndpoints as $module => $endpoints) {
            foreach ($endpoints as $pathMethod => $config) {
                list($method, $path) = explode(' ', $pathMethod, 2);
                $method = strtolower($method);
                
                if (!isset($paths[$path])) {
                    $paths[$path] = [];
                }
                
                $paths[$path][$method] = $config;
            }
        }

        return $paths;
    }

    /**
     * 获取组件定义
     * @return array
     */
    private static function getComponents(): array
    {
        return [
            'securitySchemes' => [
                'bearerAuth' => [
                    'type' => 'http',
                    'scheme' => 'bearer',
                    'bearerFormat' => 'JWT'
                ]
            ],
            'schemas' => self::getSchemas(),
            'responses' => self::getCommonResponses()
        ];
    }

    /**
     * 获取数据模型定义
     * @return array
     */
    private static function getSchemas(): array
    {
        return [
            'LoginResponse' => [
                'type' => 'object',
                'properties' => [
                    'success' => ['type' => 'boolean'],
                    'code' => ['type' => 'integer'],
                    'message' => ['type' => 'string'],
                    'data' => [
                        'type' => 'object',
                        'properties' => [
                            'token' => ['type' => 'string', 'description' => '访问令牌'],
                            'expires_in' => ['type' => 'integer', 'description' => '过期时间(秒)'],
                            'user' => ['$ref' => '#/components/schemas/User']
                        ]
                    ]
                ]
            ],
            'TokenResponse' => [
                'type' => 'object',
                'properties' => [
                    'success' => ['type' => 'boolean'],
                    'code' => ['type' => 'integer'],
                    'message' => ['type' => 'string'],
                    'data' => [
                        'type' => 'object',
                        'properties' => [
                            'token' => ['type' => 'string'],
                            'expires_in' => ['type' => 'integer']
                        ]
                    ]
                ]
            ],
            'User' => [
                'type' => 'object',
                'properties' => [
                    'id' => ['type' => 'integer'],
                    'username' => ['type' => 'string'],
                    'name' => ['type' => 'string'],
                    'email' => ['type' => 'string'],
                    'role' => ['type' => 'string'],
                    'status' => ['type' => 'string']
                ]
            ],
            'Customer' => [
                'type' => 'object',
                'properties' => [
                    'id' => ['type' => 'integer'],
                    'name' => ['type' => 'string'],
                    'phone' => ['type' => 'string'],
                    'id_card' => ['type' => 'string'],
                    'status' => ['type' => 'string', 'enum' => ['active', 'inactive', 'pending']],
                    'risk_level' => ['type' => 'string', 'enum' => ['low', 'medium', 'high', 'very_high']],
                    'risk_score' => ['type' => 'integer'],
                    'created_at' => ['type' => 'string', 'format' => 'date-time'],
                    'updated_at' => ['type' => 'string', 'format' => 'date-time']
                ]
            ],
            'CustomerCreateRequest' => [
                'type' => 'object',
                'required' => ['name', 'phone', 'id_card'],
                'properties' => [
                    'name' => ['type' => 'string', 'description' => '客户姓名'],
                    'phone' => ['type' => 'string', 'description' => '手机号'],
                    'id_card' => ['type' => 'string', 'description' => '身份证号'],
                    'gender' => ['type' => 'string', 'enum' => ['male', 'female']],
                    'birth_date' => ['type' => 'string', 'format' => 'date'],
                    'education' => ['type' => 'string'],
                    'marital_status' => ['type' => 'string'],
                    'industry' => ['type' => 'string'],
                    'occupation' => ['type' => 'string'],
                    'monthly_income' => ['type' => 'number'],
                    'residence_address' => ['type' => 'string'],
                    'work_address' => ['type' => 'string']
                ]
            ],
            'Error' => [
                'type' => 'object',
                'properties' => [
                    'success' => ['type' => 'boolean', 'example' => false],
                    'code' => ['type' => 'integer'],
                    'message' => ['type' => 'string'],
                    'data' => ['type' => 'object', 'nullable' => true],
                    'errors' => ['type' => 'array', 'items' => ['type' => 'string']],
                    'timestamp' => ['type' => 'integer'],
                    'request_id' => ['type' => 'string']
                ]
            ]
        ];
    }

    /**
     * 获取通用响应定义
     * @return array
     */
    private static function getCommonResponses(): array
    {
        return [
            'Success' => [
                'description' => '操作成功',
                'content' => [
                    'application/json' => [
                        'schema' => [
                            'type' => 'object',
                            'properties' => [
                                'success' => ['type' => 'boolean', 'example' => true],
                                'code' => ['type' => 'integer', 'example' => 200],
                                'message' => ['type' => 'string', 'example' => '操作成功'],
                                'data' => ['type' => 'object', 'nullable' => true],
                                'timestamp' => ['type' => 'integer'],
                                'request_id' => ['type' => 'string']
                            ]
                        ]
                    ]
                ]
            ],
            'Unauthorized' => [
                'description' => '未授权访问',
                'content' => [
                    'application/json' => [
                        'schema' => ['$ref' => '#/components/schemas/Error']
                    ]
                ]
            ],
            'Forbidden' => [
                'description' => '禁止访问',
                'content' => [
                    'application/json' => [
                        'schema' => ['$ref' => '#/components/schemas/Error']
                    ]
                ]
            ],
            'NotFound' => [
                'description' => '资源不存在',
                'content' => [
                    'application/json' => [
                        'schema' => ['$ref' => '#/components/schemas/Error']
                    ]
                ]
            ],
            'ValidationError' => [
                'description' => '数据验证失败',
                'content' => [
                    'application/json' => [
                        'schema' => ['$ref' => '#/components/schemas/Error']
                    ]
                ]
            ]
        ];
    }

    /**
     * 获取标签定义
     * @return array
     */
    private static function getTags(): array
    {
        return [
            ['name' => '认证管理', 'description' => '用户认证相关接口'],
            ['name' => '客户管理', 'description' => '客户信息管理接口'],
            ['name' => '借款管理', 'description' => '借款申请和管理接口'],
            ['name' => '还款管理', 'description' => '还款计划和记录接口'],
            ['name' => '风控管理', 'description' => '风险评估和控制接口'],
            ['name' => '代理管理', 'description' => '代理商管理接口'],
            ['name' => '投资管理', 'description' => '投资人管理接口'],
            ['name' => '催收管理', 'description' => '催收任务管理接口'],
            ['name' => '报表统计', 'description' => '数据统计和报表接口'],
            ['name' => '系统管理', 'description' => '系统配置和管理接口']
        ];
    }

    /**
     * 生成Swagger UI HTML
     * @return string
     */
    public static function generateSwaggerUI(): string
    {
        $apiDoc = json_encode(self::generateFullDocumentation(), JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
        
        return <<<HTML
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API文档 - 民间空放贷后管理系统</title>
    <link rel="stylesheet" type="text/css" href="https://unpkg.com/swagger-ui-dist@4.15.5/swagger-ui.css" />
    <style>
        html { box-sizing: border-box; overflow: -moz-scrollbars-vertical; overflow-y: scroll; }
        *, *:before, *:after { box-sizing: inherit; }
        body { margin:0; background: #fafafa; }
    </style>
</head>
<body>
    <div id="swagger-ui"></div>
    <script src="https://unpkg.com/swagger-ui-dist@4.15.5/swagger-ui-bundle.js"></script>
    <script src="https://unpkg.com/swagger-ui-dist@4.15.5/swagger-ui-standalone-preset.js"></script>
    <script>
        window.onload = function() {
            const ui = SwaggerUIBundle({
                spec: $apiDoc,
                dom_id: '#swagger-ui',
                deepLinking: true,
                presets: [
                    SwaggerUIBundle.presets.apis,
                    SwaggerUIStandalonePreset
                ],
                plugins: [
                    SwaggerUIBundle.plugins.DownloadUrl
                ],
                layout: "StandaloneLayout",
                defaultModelsExpandDepth: 1,
                defaultModelExpandDepth: 1,
                docExpansion: "list",
                filter: true,
                showRequestHeaders: true,
                showCommonExtensions: true,
                tryItOutEnabled: true
            });
        };
    </script>
</body>
</html>
HTML;
    }
}
