<?php
declare (strict_types = 1);

namespace app\api\controller;

use app\common\model\User;
use app\common\service\JwtService;
use think\Request;
use think\Response;
use think\exception\ValidateException;

class Auth
{
    /**
     * 用户登录
     *
     * @param Request $request
     * @return Response
     */
    public function login(Request $request): Response
    {
        try {
            // 验证请求参数
            $data = $request->param();
            
            if (empty($data['username']) || empty($data['password'])) {
                return json([
                    'code' => 400,
                    'message' => '用户名和密码不能为空',
                    'data' => null
                ], 400);
            }

            // 查找用户
            $user = User::findByUsername($data['username']);
            if (!$user) {
                return json([
                    'code' => 401,
                    'message' => '用户名或密码错误',
                    'data' => null
                ], 401);
            }

            // 验证密码
            if (!$user->verifyPassword($data['password'])) {
                return json([
                    'code' => 401,
                    'message' => '用户名或密码错误',
                    'data' => null
                ], 401);
            }

            // 检查用户状态
            if ($user->status !== 'active') {
                return json([
                    'code' => 403,
                    'message' => '账户已被禁用',
                    'data' => null
                ], 403);
            }

            // 生成Token
            $tokenData = JwtService::generateToken($user->toArray());
            
            // 更新最后登录时间
            $user->updateLastLoginTime();

            return json([
                'code' => 200,
                'message' => '登录成功',
                'data' => [
                    'token' => $tokenData['token'],
                    'refresh_token' => $tokenData['refresh_token'],
                    'expires_at' => $tokenData['expires_at'],
                    'user' => [
                        'id' => $user->id,
                        'username' => $user->username,
                        'real_name' => $user->real_name,
                        'role' => $user->role,
                        'department' => $user->department,
                        'permissions' => $user->permissions
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '登录失败：' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    /**
     * 刷新Token
     *
     * @param Request $request
     * @return Response
     */
    public function refresh(Request $request): Response
    {
        try {
            $refreshToken = $request->param('refresh_token');
            
            if (!$refreshToken) {
                return json([
                    'code' => 400,
                    'message' => 'refresh_token不能为空',
                    'data' => null
                ], 400);
            }

            $tokenData = JwtService::refreshToken($refreshToken);
            
            if (!$tokenData) {
                return json([
                    'code' => 401,
                    'message' => 'refresh_token无效或已过期',
                    'data' => null
                ], 401);
            }

            return json([
                'code' => 200,
                'message' => 'Token刷新成功',
                'data' => $tokenData
            ]);

        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => 'Token刷新失败：' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    /**
     * 获取用户信息
     *
     * @param Request $request
     * @return Response
     */
    public function user(Request $request): Response
    {
        try {
            // 从中间件中获取用户信息
            $user = $request->user ?? null;
            
            if (!$user) {
                return json([
                    'code' => 401,
                    'message' => '未认证用户',
                    'data' => null
                ], 401);
            }

            return json([
                'code' => 200,
                'message' => '获取用户信息成功',
                'data' => $user
            ]);

        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '获取用户信息失败：' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    /**
     * 用户登出
     *
     * @param Request $request
     * @return Response
     */
    public function logout(Request $request): Response
    {
        // 这里可以将token加入黑名单
        // 为了简化，暂时只返回成功消息
        
        return json([
            'code' => 200,
            'message' => '登出成功',
            'data' => null
        ]);
    }
}
