<?php
declare (strict_types = 1);

namespace app\api\controller;

use app\common\model\AppConfig;
use app\common\model\AppConfigHelper;
use app\common\model\MiniprogramComponent;
use app\common\model\MiniprogramPage;
use think\Request;
use think\Response;

class Config
{
    /** 获取应用配置 */
    public function getAppConfig(Request $request): Response
    {
        $platform = (string) $request->param('platform','');
        $appType = (string) $request->param('app_type','');
        if (!$platform || !$appType) {
            return json(['code'=>400,'message'=>'platform/app_type 不能为空','data'=>null],400);
        }

        $configs = AppConfig::where('platform',$platform)
            ->where('app_type',$appType)
            ->where('is_active',1)
            ->select();

        $data = [];
        foreach ($configs as $cfg) {
            $data[$cfg->config_key] = $this->castValue($cfg->config_value, $cfg->config_type);
        }

        return json(['code'=>200,'data'=>$data,'message'=>'获取成功']);
    }

    /** 更新应用配置 */
    public function updateAppConfig(Request $request): Response
    {
        $platform = (string) $request->param('platform','');
        $appType = (string) $request->param('app_type','');
        $configs = $request->param('configs', []);

        if (!$platform || !$appType || !is_array($configs)) {
            return json(['code'=>400,'message'=>'参数错误','data'=>null],400);
        }

        foreach ($configs as $item) {
            if (empty($item['key'])) continue;
            AppConfigHelper::updateOrCreate([
                'platform' => $platform,
                'app_type' => $appType,
                'config_key' => $item['key'],
            ], [
                'config_value' => $item['value'] ?? '',
                'config_type' => $item['type'] ?? 'string',
                'is_active' => 1,
            ]);
        }

        return json(['code'=>200,'message'=>'更新成功','data'=>true]);
    }

    /** 组件库 */
    public function components(Request $request): Response
    {
        $type = (string) $request->param('type','');
        $query = MiniprogramComponent::where('is_active',1);
        if ($type) $query->where('component_type',$type);
        $list = $query->order('sort_order','asc')->select();
        return json(['code'=>200,'data'=>$list]);
    }

    /** 保存页面 */
    public function savePage(Request $request): Response
    {
        $data = $request->only(['page_name','page_path','page_config','components']);
        foreach (['page_name','page_path'] as $f) {
            if (empty($data[$f])) {
                return json(['code'=>400,'message'=>"字段 {$f} 不能为空",'data'=>null],400);
            }
        }

        $page = MiniprogramPage::create([
            'page_name' => $data['page_name'],
            'page_path' => $data['page_path'],
            'page_config' => $data['page_config'] ?? [],
            'components' => $data['components'] ?? [],
            'is_published' => 0,
            'created_by' => $request->user['id'] ?? 0,
        ]);

        return json(['code'=>200,'message'=>'保存成功','data'=>$page]);
    }

    private function castValue(string $value, string $type)
    {
        return match ($type) {
            'number' => (float) $value,
            'boolean' => in_array(strtolower($value), ['1','true','yes'], true),
            'json' => json_decode($value, true),
            default => $value,
        };
    }
}


