-- 民间空放贷后管理系统数据库结构

USE daihou;

-- 用户表
CREATE TABLE users (
    id INT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) NOT NULL UNIQUE COMMENT 'username',
    password VARCHAR(255) NOT NULL COMMENT 'password',
    real_name VARCHAR(50) NOT NULL COMMENT 'real name',
    phone VARCHAR(20) NOT NULL COMMENT 'phone',
    email VARCHAR(100) COMMENT 'email',
    role ENUM('admin','risk_controller','finance','collection','agent','investor') NOT NULL COMMENT 'role',
    department VARCHAR(50) COMMENT 'department',
    permissions J<PERSON><PERSON> COMMENT 'permissions',
    status ENUM('active','inactive','locked') DEFAULT 'active' COMMENT 'status',
    last_login_at TIMESTAMP NULL COMMENT 'last login time',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_username (username),
    INDEX idx_role (role),
    INDEX idx_status (status)
) COMMENT='users table';

-- 客户表
CREATE TABLE customers (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(50) NOT NULL COMMENT 'customer name',
    phone VARCHAR(20) NOT NULL UNIQUE COMMENT 'phone',
    id_card VARCHAR(18) NOT NULL UNIQUE COMMENT 'id card',
    age INT COMMENT 'age',
    gender ENUM('male','female') COMMENT 'gender',
    address TEXT COMMENT 'address',
    industry VARCHAR(50) COMMENT 'industry',
    monthly_income DECIMAL(10,2) COMMENT 'monthly income',
    emergency_contact VARCHAR(50) COMMENT 'emergency contact',
    emergency_phone VARCHAR(20) COMMENT 'emergency phone',
    risk_score INT DEFAULT 0 COMMENT 'risk score',
    total_loan_amount DECIMAL(12,2) DEFAULT 0 COMMENT 'total loan amount',
    total_repaid_amount DECIMAL(12,2) DEFAULT 0 COMMENT 'total repaid amount',
    active_loans_count INT DEFAULT 0 COMMENT 'active loans count',
    last_loan_date DATE COMMENT 'last loan date',
    last_repayment_date DATE COMMENT 'last repayment date',
    customer_profit DECIMAL(10,2) DEFAULT 0 COMMENT 'customer profit',
    risk_assessment_score INT COMMENT 'risk assessment score',
    status ENUM('active','blacklist','suspended') DEFAULT 'active' COMMENT 'status',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_phone (phone),
    INDEX idx_id_card (id_card),
    INDEX idx_status (status)
) COMMENT='customers table';

-- 放款登记记录表
CREATE TABLE loan_disbursement_records (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    business_flow_no VARCHAR(32) NOT NULL UNIQUE COMMENT 'business flow no',
    loan_date DATE NOT NULL COMMENT 'loan date',
    customer_name VARCHAR(50) NOT NULL COMMENT 'customer name',
    customer_id BIGINT UNSIGNED NOT NULL COMMENT 'customer id',
    risk_controller_name VARCHAR(50) NOT NULL COMMENT 'risk controller name',
    risk_controller_id INT NOT NULL COMMENT 'risk controller id',
    loan_amount DECIMAL(10,2) NOT NULL COMMENT 'loan amount',
    
    -- 还款周期设置
    repayment_type ENUM('daily','weekly','monthly') NOT NULL COMMENT 'repayment type',
    repayment_cycle INT NOT NULL COMMENT 'repayment cycle',
    repayment_frequency VARCHAR(20) COMMENT 'repayment frequency',
    
    -- 中介返点
    agent_id INT COMMENT 'agent id',
    agent_name VARCHAR(50) COMMENT 'agent name',
    agent_commission_rate DECIMAL(5,2) COMMENT 'agent commission rate',
    agent_commission_amount DECIMAL(10,2) COMMENT 'agent commission amount',
    commission_type ENUM('front','back','both') DEFAULT 'front' COMMENT 'commission type',
    front_commission_amount DECIMAL(10,2) DEFAULT 0 COMMENT 'front commission amount',
    back_commission_amount DECIMAL(10,2) DEFAULT 0 COMMENT 'back commission amount',
    back_commission_paid TINYINT DEFAULT 0 COMMENT 'back commission paid',
    
    -- 费用管理
    platform_fee DECIMAL(10,2) NOT NULL DEFAULT 0 COMMENT 'platform fee',
    overdue_fee DECIMAL(10,2) DEFAULT 0 COMMENT 'overdue fee',
    
    -- 财务统计
    total_repaid_amount DECIMAL(10,2) DEFAULT 0 COMMENT 'total repaid amount',
    remaining_amount DECIMAL(10,2) NOT NULL COMMENT 'remaining amount',
    customer_profit DECIMAL(10,2) DEFAULT 0 COMMENT 'customer profit',
    
    -- 状态管理
    status ENUM('active','completed','overdue','settled') DEFAULT 'active' COMMENT 'status',
    created_by INT NOT NULL COMMENT 'created by',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_customer_id (customer_id),
    INDEX idx_agent_id (agent_id),
    INDEX idx_loan_date (loan_date),
    INDEX idx_status (status),
    FOREIGN KEY (customer_id) REFERENCES customers(id),
    FOREIGN KEY (created_by) REFERENCES users(id)
) COMMENT='loan disbursement records';

-- 还款计划详细表
CREATE TABLE repayment_schedule_details (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    disbursement_record_id BIGINT UNSIGNED NOT NULL COMMENT 'disbursement record id',
    period_number INT NOT NULL COMMENT 'period number',
    due_date DATE NOT NULL COMMENT 'due date',
    due_amount DECIMAL(10,2) NOT NULL COMMENT 'due amount',
    paid_amount DECIMAL(10,2) DEFAULT 0 COMMENT 'paid amount',
    remaining_amount DECIMAL(10,2) NOT NULL COMMENT 'remaining amount',
    status ENUM('pending','paid','overdue','partial') DEFAULT 'pending' COMMENT 'status',
    overdue_days INT DEFAULT 0 COMMENT 'overdue days',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_disbursement_record_id (disbursement_record_id),
    INDEX idx_due_date (due_date),
    INDEX idx_status (status),
    FOREIGN KEY (disbursement_record_id) REFERENCES loan_disbursement_records(id)
) COMMENT='repayment schedule details';

-- 还款登记记录表
CREATE TABLE repayment_records (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    business_flow_no VARCHAR(32) NOT NULL UNIQUE COMMENT 'business flow no',
    disbursement_record_id BIGINT UNSIGNED NOT NULL COMMENT 'disbursement record id',
    customer_id BIGINT UNSIGNED NOT NULL COMMENT 'customer id',
    customer_name VARCHAR(50) NOT NULL COMMENT 'customer name',
    
    -- 还款信息
    repayment_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'repayment date',
    repayment_method ENUM('wechat','alipay','bank_card','cash','other') NOT NULL COMMENT 'repayment method',
    repayment_amount DECIMAL(10,2) NOT NULL COMMENT 'repayment amount',
    repayment_type ENUM('normal','partial','negotiated','overdue_fee','early_settlement') NOT NULL COMMENT 'repayment type',
    
    -- 附件和备注
    payment_screenshot VARCHAR(500) COMMENT 'payment screenshot',
    remark TEXT COMMENT 'remark',
    
    -- 逾期处理
    overdue_days INT DEFAULT 0 COMMENT 'overdue days',
    overdue_fee DECIMAL(10,2) DEFAULT 0 COMMENT 'overdue fee',
    
    -- 状态管理
    status ENUM('pending','confirmed','cancelled') DEFAULT 'confirmed' COMMENT 'status',
    created_by INT NOT NULL COMMENT 'created by',
    confirmed_by INT COMMENT 'confirmed by',
    confirmed_at TIMESTAMP NULL COMMENT 'confirmed at',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_disbursement_record_id (disbursement_record_id),
    INDEX idx_customer_id (customer_id),
    INDEX idx_repayment_date (repayment_date),
    INDEX idx_status (status),
    FOREIGN KEY (disbursement_record_id) REFERENCES loan_disbursement_records(id),
    FOREIGN KEY (customer_id) REFERENCES customers(id),
    FOREIGN KEY (created_by) REFERENCES users(id)
) COMMENT='repayment records';

-- 插入初始数据
INSERT INTO users (username, password, real_name, phone, role, status) VALUES 
('admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Administrator', '13900000000', 'admin', 'active'),
('finance001', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Finance Manager', '13900000001', 'finance', 'active'),
('risk001', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Risk Controller', '13900000002', 'risk_controller', 'active');
