<?php
declare(strict_types=1);

namespace tests\Unit\Service;

use tests\TestCase;
use app\common\service\ApiValidatorService;
use think\exception\ValidateException;

/**
 * API验证器服务测试
 */
class ApiValidatorServiceTest extends TestCase
{
    /**
     * 测试登录验证成功
     */
    public function testAuthLoginValidationSuccess(): void
    {
        $data = [
            'username' => 'admin',
            'password' => 'password123',
            'remember' => true
        ];

        $validated = ApiValidatorService::validate($data, 'auth.login');

        $this->assertEquals($data, $validated);
    }

    /**
     * 测试登录验证失败 - 用户名为空
     */
    public function testAuthLoginValidationFailureEmptyUsername(): void
    {
        $this->expectException(ValidateException::class);
        $this->expectExceptionMessage('用户名不能为空');

        $data = [
            'username' => '',
            'password' => 'password123'
        ];

        ApiValidatorService::validate($data, 'auth.login');
    }

    /**
     * 测试登录验证失败 - 密码太短
     */
    public function testAuthLoginValidationFailureShortPassword(): void
    {
        $this->expectException(ValidateException::class);

        $data = [
            'username' => 'admin',
            'password' => '123'
        ];

        ApiValidatorService::validate($data, 'auth.login');
    }

    /**
     * 测试客户创建验证成功
     */
    public function testCustomerCreateValidationSuccess(): void
    {
        $data = [
            'name' => '张三',
            'phone' => '13800138000',
            'id_card' => '110101199001011234',
            'gender' => 'male',
            'birth_date' => '1990-01-01',
            'education' => 'university',
            'marital_status' => 'single',
            'industry' => '互联网',
            'occupation' => '软件工程师',
            'monthly_income' => 15000.50,
            'residence_address' => '北京市朝阳区',
            'work_address' => '北京市海淀区'
        ];

        $validated = ApiValidatorService::validate($data, 'customer.create');

        $this->assertEquals($data, $validated);
    }

    /**
     * 测试客户创建验证失败 - 必填字段缺失
     */
    public function testCustomerCreateValidationFailureMissingRequired(): void
    {
        $this->expectException(ValidateException::class);
        $this->expectExceptionMessage('姓名不能为空');

        $data = [
            'phone' => '13800138000',
            'id_card' => '110101199001011234'
        ];

        ApiValidatorService::validate($data, 'customer.create');
    }

    /**
     * 测试客户创建验证失败 - 手机号格式错误
     */
    public function testCustomerCreateValidationFailureInvalidPhone(): void
    {
        $this->expectException(ValidateException::class);

        $data = [
            'name' => '张三',
            'phone' => '1234567890',
            'id_card' => '110101199001011234'
        ];

        ApiValidatorService::validate($data, 'customer.create');
    }

    /**
     * 测试借款申请验证成功
     */
    public function testLoanCreateValidationSuccess(): void
    {
        $data = [
            'customer_id' => 1,
            'amount' => 50000.00,
            'cycle' => 30,
            'interest_rate' => 0.15,
            'repayment_method' => 'equal_installment',
            'purpose' => '个人消费',
            'collateral_info' => '房产抵押'
        ];

        $validated = ApiValidatorService::validate($data, 'loan.create');

        $this->assertEquals($data, $validated);
    }

    /**
     * 测试借款申请验证失败 - 金额超出范围
     */
    public function testLoanCreateValidationFailureAmountOutOfRange(): void
    {
        $this->expectException(ValidateException::class);

        $data = [
            'customer_id' => 1,
            'amount' => 500000.00, // 超出最大限额
            'cycle' => 30,
            'purpose' => '个人消费'
        ];

        ApiValidatorService::validate($data, 'loan.create');
    }

    /**
     * 测试分页参数验证
     */
    public function testValidatePagination(): void
    {
        $params = [
            'page' => 2,
            'limit' => 10,
            'sort' => 'created_at',
            'order' => 'desc'
        ];

        $validated = ApiValidatorService::validatePagination($params);

        $this->assertEquals($params, $validated);
    }

    /**
     * 测试分页参数验证 - 使用默认值
     */
    public function testValidatePaginationWithDefaults(): void
    {
        $params = [];

        $validated = ApiValidatorService::validatePagination($params);

        $expected = [
            'page' => 1,
            'limit' => 20,
            'sort' => 'id',
            'order' => 'desc'
        ];

        $this->assertEquals($expected, $validated);
    }

    /**
     * 测试搜索参数验证
     */
    public function testValidateSearch(): void
    {
        $params = [
            'keyword' => '测试关键词',
            'status' => 'active',
            'start_date' => '2025-01-01',
            'end_date' => '2025-01-31'
        ];

        $validated = ApiValidatorService::validateSearch($params);

        $this->assertEquals($params, $validated);
    }

    /**
     * 测试搜索参数验证失败 - 结束日期早于开始日期
     */
    public function testValidateSearchFailureInvalidDateRange(): void
    {
        $this->expectException(ValidateException::class);

        $params = [
            'start_date' => '2025-01-31',
            'end_date' => '2025-01-01'
        ];

        ApiValidatorService::validateSearch($params);
    }

    /**
     * 测试ID验证成功
     */
    public function testValidateIdSuccess(): void
    {
        $id = ApiValidatorService::validateId('123');
        $this->assertEquals(123, $id);

        $id = ApiValidatorService::validateId(456);
        $this->assertEquals(456, $id);
    }

    /**
     * 测试ID验证失败 - 无效ID
     */
    public function testValidateIdFailure(): void
    {
        $this->expectException(ValidateException::class);

        ApiValidatorService::validateId('invalid');
    }

    /**
     * 测试ID验证失败 - 负数ID
     */
    public function testValidateIdFailureNegative(): void
    {
        $this->expectException(ValidateException::class);

        ApiValidatorService::validateId(-1);
    }

    /**
     * 测试批量验证成功
     */
    public function testBatchValidateSuccess(): void
    {
        $dataList = [
            [
                'username' => 'user1',
                'password' => 'password123'
            ],
            [
                'username' => 'user2',
                'password' => 'password456'
            ]
        ];

        $validated = ApiValidatorService::batchValidate($dataList, 'auth.login');

        $this->assertEquals($dataList, $validated);
    }

    /**
     * 测试批量验证失败
     */
    public function testBatchValidateFailure(): void
    {
        $this->expectException(ValidateException::class);
        $this->expectExceptionMessageMatches('/第1条数据/');

        $dataList = [
            [
                'username' => '',
                'password' => 'password123'
            ],
            [
                'username' => 'user2',
                'password' => 'password456'
            ]
        ];

        ApiValidatorService::batchValidate($dataList, 'auth.login');
    }

    /**
     * 测试自定义验证规则
     */
    public function testCustomValidationRules(): void
    {
        $customRules = [
            'custom_field' => 'require|length:5,10'
        ];

        $customMessages = [
            'custom_field.require' => '自定义字段不能为空',
            'custom_field.length' => '自定义字段长度必须在5到10之间'
        ];

        $data = [
            'username' => 'admin',
            'password' => 'password123',
            'custom_field' => 'test123'
        ];

        $validated = ApiValidatorService::validate($data, 'auth.login', $customRules, $customMessages);

        $this->assertArrayHasKey('custom_field', $validated);
        $this->assertEquals('test123', $validated['custom_field']);
    }

    /**
     * 测试获取验证规则
     */
    public function testGetValidationRules(): void
    {
        $rules = ApiValidatorService::getValidationRules('auth.login');

        $this->assertIsArray($rules);
        $this->assertArrayHasKey('username', $rules);
        $this->assertArrayHasKey('password', $rules);
    }

    /**
     * 测试获取验证场景
     */
    public function testGetValidationScenes(): void
    {
        $scenes = ApiValidatorService::getValidationScenes();

        $this->assertIsArray($scenes);
        $this->assertContains('auth.login', $scenes);
        $this->assertContains('customer.create', $scenes);
        $this->assertContains('loan.create', $scenes);
    }

    /**
     * 测试不存在的验证场景
     */
    public function testNonExistentValidationScene(): void
    {
        $data = ['test' => 'value'];
        
        $validated = ApiValidatorService::validate($data, 'non.existent.scene');
        
        // 不存在的场景应该返回原始数据
        $this->assertEquals($data, $validated);
    }
}
