<?php
declare (strict_types = 1);

namespace app\api\controller;

use app\common\model\LoanDisbursementRecord;
use app\common\model\RepaymentRecord;
use app\common\service\DisbursementService;
use app\common\service\RepaymentService;
use think\Request;
use think\Response;

class Finance
{
    /**
     * 创建放款登记
     *
     * @param Request $request
     * @return Response
     */
    public function createDisbursement(Request $request): Response
    {
        try {
            $data = $request->param();

            // 注入创建人ID（来自认证中间件）
            if (!empty($request->user) && isset($request->user['id'])) {
                $data['created_by'] = $request->user['id'];
            }

            // 基础验证
            $required = ['loan_date', 'customer_id', 'customer_name', 'loan_amount', 'repayment_type', 'repayment_cycle'];
            foreach ($required as $field) {
                if (empty($data[$field])) {
                    return json([
                        'code' => 400,
                        'message' => "字段 {$field} 不能为空",
                        'data' => null
                    ], 400);
                }
            }

            // 调用放款服务
            $result = DisbursementService::createDisbursementRecord($data);

            return json([
                'code' => 200,
                'message' => '放款登记成功',
                'data' => $result
            ]);

        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '放款登记失败：' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    /**
     * 获取放款记录列表
     *
     * @param Request $request
     * @return Response
     */
    public function getDisbursements(Request $request): Response
    {
        try {
            $page = (int) $request->param('page', 1);
            $limit = (int) $request->param('limit', 20);
            $status = $request->param('status', '');
            $dateFrom = $request->param('date_from', '');
            $dateTo = $request->param('date_to', '');
            $keyword = $request->param('keyword', '');

            $query = LoanDisbursementRecord::order('created_at', 'desc');

            // 状态筛选
            if ($status) {
                $query->where('status', $status);
            }

            // 日期筛选
            if ($dateFrom) {
                $query->where('loan_date', '>=', $dateFrom);
            }
            if ($dateTo) {
                $query->where('loan_date', '<=', $dateTo);
            }

            // 关键词搜索
            if ($keyword) {
                $query->where('business_flow_no|customer_name', 'like', "%{$keyword}%");
            }

            $result = $query->paginate([
                'list_rows' => $limit,
                'page' => $page
            ]);

            return json([
                'code' => 200,
                'message' => '获取放款记录成功',
                'data' => [
                    'items' => $result->items(),
                    'pagination' => [
                        'page' => $page,
                        'limit' => $limit,
                        'total' => $result->total(),
                        'pages' => $result->lastPage()
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '获取放款记录失败：' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    /**
     * 获取放款记录详情
     *
     * @param Request $request
     * @param int $id
     * @return Response
     */
    public function getDisbursementDetail(Request $request, int $id): Response
    {
        try {
            $record = LoanDisbursementRecord::with(['repaymentSchedule', 'repaymentRecords'])->find($id);

            if (!$record) {
                return json([
                    'code' => 404,
                    'message' => '放款记录不存在',
                    'data' => null
                ], 404);
            }

            return json([
                'code' => 200,
                'message' => '获取放款记录详情成功',
                'data' => $record
            ]);

        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '获取放款记录详情失败：' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    /**
     * 创建还款登记
     *
     * @param Request $request
     * @return Response
     */
    public function createRepayment(Request $request): Response
    {
        try {
            $data = $request->param();

            // 注入创建人ID（来自认证中间件）
            if (!empty($request->user) && isset($request->user['id'])) {
                $data['created_by'] = $request->user['id'];
            }

            // 基础验证
            $required = ['disbursement_record_id', 'repayment_method', 'repayment_amount', 'repayment_type'];
            foreach ($required as $field) {
                if (empty($data[$field])) {
                    return json([
                        'code' => 400,
                        'message' => "字段 {$field} 不能为空",
                        'data' => null
                    ], 400);
                }
            }

            // 调用还款服务
            $result = RepaymentService::createRepaymentRecord($data);

            return json([
                'code' => 200,
                'message' => '还款登记成功',
                'data' => $result
            ]);

        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '还款登记失败：' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    /**
     * 获取还款记录列表
     *
     * @param Request $request
     * @return Response
     */
    public function getRepayments(Request $request): Response
    {
        try {
            $page = (int) $request->param('page', 1);
            $limit = (int) $request->param('limit', 20);
            $disbursementRecordId = $request->param('disbursement_record_id', '');
            $customerId = $request->param('customer_id', '');
            $repaymentMethod = $request->param('repayment_method', '');

            $query = RepaymentRecord::order('created_at', 'desc');

            // 按放款记录筛选
            if ($disbursementRecordId) {
                $query->where('disbursement_record_id', $disbursementRecordId);
            }

            // 按客户筛选
            if ($customerId) {
                $query->where('customer_id', $customerId);
            }

            // 按还款方式筛选
            if ($repaymentMethod) {
                $query->where('repayment_method', $repaymentMethod);
            }

            $result = $query->paginate([
                'list_rows' => $limit,
                'page' => $page
            ]);

            return json([
                'code' => 200,
                'message' => '获取还款记录成功',
                'data' => [
                    'items' => $result->items(),
                    'pagination' => [
                        'page' => $page,
                        'limit' => $limit,
                        'total' => $result->total(),
                        'pages' => $result->lastPage()
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '获取还款记录失败：' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    /**
     * 上传还款截图
     *
     * @param Request $request
     * @return Response
     */
    public function uploadScreenshot(Request $request): Response
    {
        try {
            $file = $request->file('file');
            
            if (!$file) {
                return json([
                    'code' => 400,
                    'message' => '请选择要上传的文件',
                    'data' => null
                ], 400);
            }

            // 验证文件类型
            $allowedTypes = ['image/jpeg', 'image/png', 'image/gif'];
            if (!in_array($file->getMime(), $allowedTypes)) {
                return json([
                    'code' => 400,
                    'message' => '文件类型不支持，只支持jpg、png、gif格式',
                    'data' => null
                ], 400);
            }

            // 验证文件大小（5MB）
            if ($file->getSize() > 5 * 1024 * 1024) {
                return json([
                    'code' => 400,
                    'message' => '文件大小不能超过5MB',
                    'data' => null
                ], 400);
            }

            // 保存文件
            $savePath = 'uploads/screenshots/' . date('Y/m/d/');
            // 确保目录存在
            $fullDir = public_path() . $savePath;
            if (!is_dir($fullDir)) {
                @mkdir($fullDir, 0777, true);
            }
            $fileName = date('YmdHis') . '_' . uniqid() . '.' . $file->extension();
            $result = $file->move($savePath, $fileName);

            if (!$result) {
                return json([
                    'code' => 500,
                    'message' => '文件上传失败',
                    'data' => null
                ], 500);
            }

            $relativePath = '/' . $savePath . $fileName;
            $fileUrl = $request->domain() . $relativePath;

            return json([
                'code' => 200,
                'message' => '文件上传成功',
                'data' => [
                    'url' => $fileUrl,
                    'path' => $relativePath,
                    'filename' => $fileName,
                    'size' => $file->getSize()
                ]
            ]);

        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '文件上传失败：' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    /**
     * 获取财务统计数据
     *
     * @param Request $request
     * @return Response
     */
    public function getStatistics(Request $request): Response
    {
        try {
            $dateFrom = $request->param('date_from', date('Y-m-d'));
            $dateTo = $request->param('date_to', date('Y-m-d'));

            // 今日放款统计
            $todayDisbursement = LoanDisbursementRecord::whereDay('loan_date', 'today')
                ->field('COUNT(*) as count, SUM(loan_amount) as amount')
                ->find();

            // 今日回款统计
            $todayRepayment = RepaymentRecord::whereDay('repayment_date', 'today')
                ->field('COUNT(*) as count, SUM(repayment_amount) as amount')
                ->find();

            // 今日利润
            $todayProfit = LoanDisbursementRecord::where('loan_date', date('Y-m-d'))
                ->sum('customer_profit');

            // 逾期统计
            $overdueStats = LoanDisbursementRecord::where('status', 'overdue')
                ->field('COUNT(*) as count, SUM(remaining_amount) as amount')
                ->find();

            return json([
                'code' => 200,
                'message' => '获取统计数据成功',
                'data' => [
                    'today_disbursement' => [
                        'count' => $todayDisbursement['count'] ?? 0,
                        'amount' => $todayDisbursement['amount'] ?? 0
                    ],
                    'today_repayment' => [
                        'count' => $todayRepayment['count'] ?? 0,
                        'amount' => $todayRepayment['amount'] ?? 0
                    ],
                    'today_profit' => $todayProfit ?? 0,
                    'overdue' => [
                        'count' => $overdueStats['count'] ?? 0,
                        'amount' => $overdueStats['amount'] ?? 0
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '获取统计数据失败：' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }
}
