<?php
declare(strict_types=1);

namespace app\common\controller;

use app\common\service\ResponseService;
use app\common\service\AuditLogService;
use think\App;
use think\Request;
use think\Response;
use think\exception\ValidateException;
use think\facade\Log;

/**
 * 统一基础控制器
 * 提供所有应用通用的基础功能
 */
abstract class BaseController
{
    /**
     * Request实例
     * @var Request
     */
    protected $request;

    /**
     * 应用实例
     * @var App
     */
    protected $app;

    /**
     * 当前用户信息
     * @var array|null
     */
    protected $user;

    /**
     * 当前应用名称
     * @var string
     */
    protected $appName;

    /**
     * 构造方法
     * @param App $app
     */
    public function __construct(App $app)
    {
        $this->app = $app;
        $this->request = $this->app->request;
        $this->appName = $this->app->http->getName();
        
        // 初始化用户信息
        $this->initUser();
        
        // 记录请求日志
        $this->logRequest();
    }

    /**
     * 初始化用户信息
     */
    protected function initUser(): void
    {
        $this->user = $this->request->userData ?? null;
    }

    /**
     * 记录请求日志
     */
    protected function logRequest(): void
    {
        try {
            $logData = [
                'app' => $this->appName,
                'method' => $this->request->method(),
                'url' => $this->request->url(),
                'ip' => $this->request->ip(),
                'user_agent' => $this->request->header('user-agent', ''),
                'params' => $this->request->param(),
                'user_id' => $this->user['id'] ?? 0
            ];
            
            Log::info('API请求', $logData);
        } catch (\Exception $e) {
            // 日志记录失败不影响主流程
        }
    }

    /**
     * 成功响应
     * @param mixed $data 响应数据
     * @param string $message 响应消息
     * @param int $code 响应码
     * @return Response
     */
    protected function success($data = [], string $message = '操作成功', int $code = 200): Response
    {
        return ResponseService::success($data, $message, $code);
    }

    /**
     * 错误响应
     * @param string $message 错误消息
     * @param int $code 错误码
     * @param mixed $data 错误数据
     * @return Response
     */
    protected function error(string $message = '操作失败', int $code = 400, $data = []): Response
    {
        // 记录错误日志
        Log::error('API错误响应', [
            'message' => $message,
            'code' => $code,
            'data' => $data,
            'url' => $this->request->url(),
            'user_id' => $this->user['id'] ?? 0
        ]);
        
        return ResponseService::error($message, $code, $data);
    }

    /**
     * 分页响应
     * @param mixed $list 分页数据
     * @param string $message 响应消息
     * @return Response
     */
    protected function paginate($list, string $message = '获取成功'): Response
    {
        return ResponseService::paginate($list, $message);
    }

    /**
     * 未授权响应
     * @param string $message 错误消息
     * @return Response
     */
    protected function unauthorized(string $message = '未授权访问'): Response
    {
        return ResponseService::unauthorized($message);
    }

    /**
     * 禁止访问响应
     * @param string $message 错误消息
     * @return Response
     */
    protected function forbidden(string $message = '禁止访问'): Response
    {
        return ResponseService::forbidden($message);
    }

    /**
     * 资源不存在响应
     * @param string $message 错误消息
     * @return Response
     */
    protected function notFound(string $message = '资源不存在'): Response
    {
        return ResponseService::notFound($message);
    }

    /**
     * 服务器错误响应
     * @param string $message 错误消息
     * @return Response
     */
    protected function serverError(string $message = '服务器内部错误'): Response
    {
        return ResponseService::serverError($message);
    }

    /**
     * 验证请求参数
     * @param array $rules 验证规则
     * @param array $messages 错误消息
     * @param array $data 验证数据
     * @return array 验证后的数据
     * @throws ValidateException
     */
    protected function validate(array $rules, array $messages = [], array $data = []): array
    {
        if (empty($data)) {
            $data = $this->request->param();
        }
        
        try {
            validate($rules, $messages)->check($data);
            return $data;
        } catch (ValidateException $e) {
            throw new ValidateException($e->getError());
        }
    }

    /**
     * 获取当前用户ID
     * @return int
     */
    protected function getUserId(): int
    {
        return $this->user['id'] ?? 0;
    }

    /**
     * 获取当前用户信息
     * @return array|null
     */
    protected function getUser(): ?array
    {
        return $this->user;
    }

    /**
     * 检查用户权限
     * @param string $permission 权限标识
     * @return bool
     */
    protected function checkPermission(string $permission): bool
    {
        if (!$this->user) {
            return false;
        }
        
        // TODO: 实现权限检查逻辑
        return true;
    }

    /**
     * 记录操作审计日志
     * @param string $action 操作动作
     * @param array $data 操作数据
     * @param string $module 模块名称
     */
    protected function auditLog(string $action, array $data = [], string $module = ''): void
    {
        try {
            AuditLogService::log(
                $action,
                $data,
                $module ?: $this->appName,
                $this->getUserId()
            );
        } catch (\Exception $e) {
            Log::error('审计日志记录失败', ['error' => $e->getMessage()]);
        }
    }
}
