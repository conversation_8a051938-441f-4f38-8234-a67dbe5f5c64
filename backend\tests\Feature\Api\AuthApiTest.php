<?php
declare(strict_types=1);

namespace tests\Feature\Api;

use tests\TestCase;
use think\facade\Db;

/**
 * 认证API集成测试
 */
class AuthApiTest extends TestCase
{
    /**
     * 测试用户登录成功
     */
    public function testLoginSuccess(): void
    {
        // 创建测试用户
        $user = $this->createTestUser([
            'username' => 'testuser',
            'password' => password_hash('password123', PASSWORD_DEFAULT),
            'status' => 1
        ]);

        // 模拟登录请求
        $response = $this->makeRequest('POST', '/api/auth/login', [
            'username' => 'testuser',
            'password' => 'password123'
        ]);

        // 断言响应成功
        $this->assertResponseSuccess($response);
        $this->assertArrayHasKey('data', $response);
        $this->assertArrayHasKey('token', $response['data']);
        $this->assertArrayHasKey('user', $response['data']);
        $this->assertEquals('testuser', $response['data']['user']['username']);
    }

    /**
     * 测试用户登录失败 - 用户名错误
     */
    public function testLoginFailureInvalidUsername(): void
    {
        $response = $this->makeRequest('POST', '/api/auth/login', [
            'username' => 'nonexistent',
            'password' => 'password123'
        ]);

        $this->assertResponseError($response, 401);
        $this->assertStringContainsString('用户名或密码错误', $response['message']);
    }

    /**
     * 测试用户登录失败 - 密码错误
     */
    public function testLoginFailureInvalidPassword(): void
    {
        $user = $this->createTestUser([
            'username' => 'testuser',
            'password' => password_hash('password123', PASSWORD_DEFAULT),
            'status' => 1
        ]);

        $response = $this->makeRequest('POST', '/api/auth/login', [
            'username' => 'testuser',
            'password' => 'wrongpassword'
        ]);

        $this->assertResponseError($response, 401);
        $this->assertStringContainsString('用户名或密码错误', $response['message']);
    }

    /**
     * 测试用户登录失败 - 用户被禁用
     */
    public function testLoginFailureUserDisabled(): void
    {
        $user = $this->createTestUser([
            'username' => 'testuser',
            'password' => password_hash('password123', PASSWORD_DEFAULT),
            'status' => 0 // 禁用状态
        ]);

        $response = $this->makeRequest('POST', '/api/auth/login', [
            'username' => 'testuser',
            'password' => 'password123'
        ]);

        $this->assertResponseError($response, 403);
        $this->assertStringContainsString('用户已被禁用', $response['message']);
    }

    /**
     * 测试用户登录失败 - 参数验证错误
     */
    public function testLoginFailureValidationError(): void
    {
        $response = $this->makeRequest('POST', '/api/auth/login', [
            'username' => '',
            'password' => '123'
        ]);

        $this->assertResponseError($response, 422);
        $this->assertArrayHasKey('errors', $response);
    }

    /**
     * 测试用户登出成功
     */
    public function testLogoutSuccess(): void
    {
        // 创建测试用户并登录
        $user = $this->createTestUser([
            'username' => 'testuser',
            'password' => password_hash('password123', PASSWORD_DEFAULT),
            'status' => 1
        ]);

        // 模拟获取token
        $token = 'test_token_' . uniqid();

        $response = $this->makeRequest('POST', '/api/auth/logout', [], [
            'Authorization: Bearer ' . $token
        ]);

        $this->assertResponseSuccess($response);
        $this->assertEquals('登出成功', $response['message']);
    }

    /**
     * 测试用户登出失败 - 未提供token
     */
    public function testLogoutFailureNoToken(): void
    {
        $response = $this->makeRequest('POST', '/api/auth/logout');

        $this->assertResponseError($response, 401);
        $this->assertStringContainsString('未授权访问', $response['message']);
    }

    /**
     * 测试刷新token成功
     */
    public function testRefreshTokenSuccess(): void
    {
        // 创建测试用户
        $user = $this->createTestUser([
            'username' => 'testuser',
            'password' => password_hash('password123', PASSWORD_DEFAULT),
            'status' => 1
        ]);

        // 模拟有效token
        $token = 'test_token_' . uniqid();

        $response = $this->makeRequest('POST', '/api/auth/refresh', [], [
            'Authorization: Bearer ' . $token
        ]);

        $this->assertResponseSuccess($response);
        $this->assertArrayHasKey('data', $response);
        $this->assertArrayHasKey('token', $response['data']);
        $this->assertArrayHasKey('expires_in', $response['data']);
    }

    /**
     * 测试刷新token失败 - 无效token
     */
    public function testRefreshTokenFailureInvalidToken(): void
    {
        $response = $this->makeRequest('POST', '/api/auth/refresh', [], [
            'Authorization: Bearer invalid_token'
        ]);

        $this->assertResponseError($response, 401);
        $this->assertStringContainsString('token无效', $response['message']);
    }

    /**
     * 测试获取用户信息成功
     */
    public function testGetUserProfileSuccess(): void
    {
        // 创建测试用户
        $user = $this->createTestUser([
            'username' => 'testuser',
            'name' => '测试用户',
            'email' => '<EMAIL>',
            'phone' => '13800138000',
            'status' => 1
        ]);

        // 模拟有效token
        $token = 'test_token_' . uniqid();

        $response = $this->makeRequest('GET', '/api/auth/profile', [], [
            'Authorization: Bearer ' . $token
        ]);

        $this->assertResponseSuccess($response);
        $this->assertArrayHasKey('data', $response);
        
        $userData = $response['data'];
        $this->assertEquals('testuser', $userData['username']);
        $this->assertEquals('测试用户', $userData['name']);
        $this->assertEquals('<EMAIL>', $userData['email']);
        $this->assertEquals('13800138000', $userData['phone']);
    }

    /**
     * 测试更新用户信息成功
     */
    public function testUpdateUserProfileSuccess(): void
    {
        // 创建测试用户
        $user = $this->createTestUser([
            'username' => 'testuser',
            'name' => '测试用户',
            'email' => '<EMAIL>',
            'status' => 1
        ]);

        // 模拟有效token
        $token = 'test_token_' . uniqid();

        $updateData = [
            'name' => '更新后的用户名',
            'email' => '<EMAIL>',
            'phone' => '13900139000'
        ];

        $response = $this->makeRequest('PUT', '/api/auth/profile', $updateData, [
            'Authorization: Bearer ' . $token
        ]);

        $this->assertResponseSuccess($response);
        $this->assertEquals('更新成功', $response['message']);

        // 验证数据库中的数据已更新
        $this->assertDatabaseHas('admin_users', [
            'id' => $user['id'],
            'name' => '更新后的用户名',
            'email' => '<EMAIL>',
            'phone' => '13900139000'
        ]);
    }

    /**
     * 测试更新用户信息失败 - 验证错误
     */
    public function testUpdateUserProfileFailureValidationError(): void
    {
        // 创建测试用户
        $user = $this->createTestUser([
            'username' => 'testuser',
            'status' => 1
        ]);

        // 模拟有效token
        $token = 'test_token_' . uniqid();

        $updateData = [
            'email' => 'invalid-email', // 无效邮箱格式
            'phone' => '123' // 无效手机号格式
        ];

        $response = $this->makeRequest('PUT', '/api/auth/profile', $updateData, [
            'Authorization: Bearer ' . $token
        ]);

        $this->assertResponseError($response, 422);
        $this->assertArrayHasKey('errors', $response);
    }

    /**
     * 测试修改密码成功
     */
    public function testChangePasswordSuccess(): void
    {
        // 创建测试用户
        $user = $this->createTestUser([
            'username' => 'testuser',
            'password' => password_hash('oldpassword', PASSWORD_DEFAULT),
            'status' => 1
        ]);

        // 模拟有效token
        $token = 'test_token_' . uniqid();

        $passwordData = [
            'old_password' => 'oldpassword',
            'new_password' => 'newpassword123',
            'confirm_password' => 'newpassword123'
        ];

        $response = $this->makeRequest('PUT', '/api/auth/password', $passwordData, [
            'Authorization: Bearer ' . $token
        ]);

        $this->assertResponseSuccess($response);
        $this->assertEquals('密码修改成功', $response['message']);

        // 验证新密码可以登录
        $loginResponse = $this->makeRequest('POST', '/api/auth/login', [
            'username' => 'testuser',
            'password' => 'newpassword123'
        ]);

        $this->assertResponseSuccess($loginResponse);
    }

    /**
     * 测试修改密码失败 - 旧密码错误
     */
    public function testChangePasswordFailureWrongOldPassword(): void
    {
        // 创建测试用户
        $user = $this->createTestUser([
            'username' => 'testuser',
            'password' => password_hash('oldpassword', PASSWORD_DEFAULT),
            'status' => 1
        ]);

        // 模拟有效token
        $token = 'test_token_' . uniqid();

        $passwordData = [
            'old_password' => 'wrongpassword',
            'new_password' => 'newpassword123',
            'confirm_password' => 'newpassword123'
        ];

        $response = $this->makeRequest('PUT', '/api/auth/password', $passwordData, [
            'Authorization: Bearer ' . $token
        ]);

        $this->assertResponseError($response, 400);
        $this->assertStringContainsString('原密码错误', $response['message']);
    }

    /**
     * 测试修改密码失败 - 确认密码不匹配
     */
    public function testChangePasswordFailurePasswordMismatch(): void
    {
        // 创建测试用户
        $user = $this->createTestUser([
            'username' => 'testuser',
            'password' => password_hash('oldpassword', PASSWORD_DEFAULT),
            'status' => 1
        ]);

        // 模拟有效token
        $token = 'test_token_' . uniqid();

        $passwordData = [
            'old_password' => 'oldpassword',
            'new_password' => 'newpassword123',
            'confirm_password' => 'differentpassword'
        ];

        $response = $this->makeRequest('PUT', '/api/auth/password', $passwordData, [
            'Authorization: Bearer ' . $token
        ]);

        $this->assertResponseError($response, 422);
        $this->assertStringContainsString('确认密码不匹配', $response['message']);
    }
}
