# 系统改进方案与新增功能设计

## 一、系统架构升级方案

### 1. 微服务架构改造

#### 1.1 服务拆分策略

```yaml
# 微服务拆分方案
services:
  # 用户服务
  user-service:
    description: 用户管理、权限管理、认证授权
    database: user_db
    ports: 8001
    
  # 客户服务  
  customer-service:
    description: 客户管理、客户档案、风险评估
    database: customer_db
    ports: 8002
    
  # 贷款服务
  loan-service:
    description: 贷款申请、审核流程、放款管理
    database: loan_db
    ports: 8003
    
  # 风控服务
  risk-service:
    description: AI风控、评分计算、决策引擎
    database: risk_db
    ports: 8004
    
  # 财务服务
  finance-service:
    description: 财务管理、还款记录、资金流水
    database: finance_db
    ports: 8005
    
  # 催收服务
  collection-service:
    description: 催收管理、下户记录、催收策略
    database: collection_db
    ports: 8006
    
  # 消息服务
  notification-service:
    description: 消息通知、短信邮件、推送管理
    database: notification_db
    ports: 8007
    
  # 配置服务
  config-service:
    description: 配置管理、参数设置、规则引擎
    database: config_db
    ports: 8008

# 服务注册中心
registry:
  type: consul
  host: localhost
  port: 8500

# API网关
gateway:
  type: kong
  host: localhost
  port: 8000
```

#### 1.2 微服务通信设计

```php
// app/common/service/MicroService.php
<?php

namespace app\common\service;

use GuzzleHttp\Client;
use think\facade\Config;

class MicroService
{
    private static $client;
    private static $services = [
        'user' => 'http://user-service:8001',
        'customer' => 'http://customer-service:8002',
        'loan' => 'http://loan-service:8003',
        'risk' => 'http://risk-service:8004',
        'finance' => 'http://finance-service:8005',
        'collection' => 'http://collection-service:8006',
        'notification' => 'http://notification-service:8007',
        'config' => 'http://config-service:8008'
    ];
    
    /**
     * 初始化HTTP客户端
     */
    private static function getClient()
    {
        if (!self::$client) {
            self::$client = new Client([
                'timeout' => 30,
                'headers' => [
                    'Content-Type' => 'application/json',
                    'User-Agent' => 'DaiHou-Gateway/1.0'
                ]
            ]);
        }
        return self::$client;
    }
    
    /**
     * 调用微服务
     */
    public static function call($service, $endpoint, $data = [], $method = 'POST')
    {
        if (!isset(self::$services[$service])) {
            throw new \Exception("未知的服务: {$service}");
        }
        
        $url = self::$services[$service] . $endpoint;
        $client = self::getClient();
        
        try {
            $response = $client->request($method, $url, [
                'json' => $data,
                'headers' => [
                    'Authorization' => 'Bearer ' . self::getServiceToken(),
                    'X-Request-ID' => uniqid()
                ]
            ]);
            
            return json_decode($response->getBody()->getContents(), true);
            
        } catch (\Exception $e) {
            LogService::error("微服务调用失败", [
                'service' => $service,
                'endpoint' => $endpoint,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }
    
    /**
     * 获取服务令牌
     */
    private static function getServiceToken()
    {
        // 从配置或缓存中获取服务间认证令牌
        return Config::get('microservice.token', '');
    }
    
    /**
     * 服务发现
     */
    public static function discover($serviceName)
    {
        // 从服务注册中心获取服务实例
        $consul = new ConsulClient(Config::get('consul.host'), Config::get('consul.port'));
        $services = $consul->health()->service($serviceName)->getStatusCode() == 200 
                   ? $consul->health()->service($serviceName)->json() 
                   : [];
        
        if (empty($services)) {
            throw new \Exception("服务 {$serviceName} 不可用");
        }
        
        // 负载均衡选择服务实例
        $instance = $services[array_rand($services)];
        return "http://{$instance['Service']['Address']}:{$instance['Service']['Port']}";
    }
}
```

### 2. 分布式事务处理

```php
// app/common/service/DistributedTransaction.php
<?php

namespace app\common\service;

class DistributedTransaction
{
    private $transactionId;
    private $participants = [];
    private $status = 'init';
    
    /**
     * 开始分布式事务
     */
    public function begin()
    {
        $this->transactionId = uniqid('tx_', true);
        $this->status = 'active';
        
        LogService::info('分布式事务开始', ['transaction_id' => $this->transactionId]);
        
        return $this->transactionId;
    }
    
    /**
     * 添加事务参与者
     */
    public function addParticipant($service, $method, $data, $compensate = null)
    {
        $this->participants[] = [
            'service' => $service,
            'method' => $method,
            'data' => $data,
            'compensate' => $compensate,
            'status' => 'pending'
        ];
    }
    
    /**
     * 执行分布式事务 (TCC模式)
     */
    public function commit()
    {
        // Try阶段
        foreach ($this->participants as &$participant) {
            try {
                $result = MicroService::call(
                    $participant['service'], 
                    '/try/' . $participant['method'], 
                    array_merge($participant['data'], ['transaction_id' => $this->transactionId])
                );
                
                if ($result['code'] != 200) {
                    throw new \Exception($result['message']);
                }
                
                $participant['status'] = 'prepared';
                $participant['try_result'] = $result;
                
            } catch (\Exception $e) {
                LogService::error('Try阶段失败', [
                    'transaction_id' => $this->transactionId,
                    'service' => $participant['service'],
                    'error' => $e->getMessage()
                ]);
                
                // Try失败，执行回滚
                $this->rollback();
                throw $e;
            }
        }
        
        // Confirm阶段
        foreach ($this->participants as &$participant) {
            try {
                $result = MicroService::call(
                    $participant['service'], 
                    '/confirm/' . $participant['method'], 
                    ['transaction_id' => $this->transactionId]
                );
                
                $participant['status'] = 'confirmed';
                
            } catch (\Exception $e) {
                LogService::error('Confirm阶段失败', [
                    'transaction_id' => $this->transactionId,
                    'service' => $participant['service'],
                    'error' => $e->getMessage()
                ]);
                
                // Confirm失败，需要补偿
                $this->compensate();
                throw $e;
            }
        }
        
        $this->status = 'committed';
        LogService::info('分布式事务提交成功', ['transaction_id' => $this->transactionId]);
        
        return true;
    }
    
    /**
     * 回滚事务
     */
    public function rollback()
    {
        foreach ($this->participants as &$participant) {
            if ($participant['status'] == 'prepared') {
                try {
                    MicroService::call(
                        $participant['service'], 
                        '/cancel/' . $participant['method'], 
                        ['transaction_id' => $this->transactionId]
                    );
                    
                    $participant['status'] = 'cancelled';
                    
                } catch (\Exception $e) {
                    LogService::error('Cancel阶段失败', [
                        'transaction_id' => $this->transactionId,
                        'service' => $participant['service'],
                        'error' => $e->getMessage()
                    ]);
                }
            }
        }
        
        $this->status = 'rollbacked';
        LogService::info('分布式事务回滚完成', ['transaction_id' => $this->transactionId]);
    }
    
    /**
     * 补偿操作
     */
    private function compensate()
    {
        foreach ($this->participants as &$participant) {
            if ($participant['status'] == 'confirmed' && $participant['compensate']) {
                try {
                    MicroService::call(
                        $participant['service'], 
                        '/compensate/' . $participant['compensate'], 
                        ['transaction_id' => $this->transactionId]
                    );
                    
                    $participant['status'] = 'compensated';
                    
                } catch (\Exception $e) {
                    LogService::error('补偿操作失败', [
                        'transaction_id' => $this->transactionId,
                        'service' => $participant['service'],
                        'error' => $e->getMessage()
                    ]);
                }
            }
        }
    }
}

// 使用示例：贷款申请分布式事务
class LoanApplicationTransaction
{
    public function processLoanApplication($customerId, $applyData)
    {
        $tx = new DistributedTransaction();
        $tx->begin();
        
        try {
            // 客户服务：验证客户信息
            $tx->addParticipant('customer', 'validateCustomer', [
                'customer_id' => $customerId
            ], 'releaseCustomerLock');
            
            // 风控服务：进行风控评估
            $tx->addParticipant('risk', 'assessRisk', [
                'customer_id' => $customerId,
                'apply_data' => $applyData
            ], 'clearRiskCache');
            
            // 贷款服务：创建贷款申请
            $tx->addParticipant('loan', 'createApplication', [
                'customer_id' => $customerId,
                'apply_data' => $applyData
            ], 'deleteApplication');
            
            // 通知服务：发送申请确认通知
            $tx->addParticipant('notification', 'sendNotification', [
                'customer_id' => $customerId,
                'template' => 'application_received'
            ]);
            
            $tx->commit();
            
            return ['code' => 200, 'message' => '申请提交成功'];
            
        } catch (\Exception $e) {
            return ['code' => 500, 'message' => '申请提交失败：' . $e->getMessage()];
        }
    }
}
```

## 二、高级业务功能

### 1. 智能报表系统

```php
// app/common/service/ReportService.php
<?php

namespace app\common\service;

class ReportService
{
    /**
     * 生成动态报表
     */
    public static function generateReport($reportConfig)
    {
        $report = new DynamicReport($reportConfig);
        
        // 构建查询
        $query = $report->buildQuery();
        
        // 执行查询
        $data = $report->executeQuery($query);
        
        // 数据处理
        $processedData = $report->processData($data);
        
        // 生成图表
        $charts = $report->generateCharts($processedData);
        
        return [
            'data' => $processedData,
            'charts' => $charts,
            'summary' => $report->generateSummary($processedData)
        ];
    }
    
    /**
     * 预定义报表配置
     */
    const REPORT_CONFIGS = [
        'daily_business' => [
            'name' => '日常业务报表',
            'tables' => ['loan_applications', 'customers', 'repayment_records'],
            'fields' => [
                'date' => 'DATE(loan_applications.submit_time)',
                'apply_count' => 'COUNT(loan_applications.id)',
                'apply_amount' => 'SUM(loan_applications.apply_amount)',
                'approved_count' => 'COUNT(CASE WHEN loan_applications.status = "approved" THEN 1 END)',
                'approved_amount' => 'SUM(CASE WHEN loan_applications.status = "approved" THEN loan_applications.apply_amount END)',
                'approval_rate' => 'ROUND(COUNT(CASE WHEN loan_applications.status = "approved" THEN 1 END) / COUNT(loan_applications.id) * 100, 2)'
            ],
            'conditions' => [
                'loan_applications.submit_time >= :start_date',
                'loan_applications.submit_time <= :end_date'
            ],
            'group_by' => ['DATE(loan_applications.submit_time)'],
            'order_by' => ['date DESC'],
            'charts' => [
                ['type' => 'line', 'x' => 'date', 'y' => 'apply_count', 'title' => '申请数量趋势'],
                ['type' => 'bar', 'x' => 'date', 'y' => 'apply_amount', 'title' => '申请金额'],
                ['type' => 'line', 'x' => 'date', 'y' => 'approval_rate', 'title' => '审批通过率']
            ]
        ],
        
        'risk_analysis' => [
            'name' => '风险分析报表',
            'tables' => ['customers', 'loan_applications'],
            'fields' => [
                'risk_level' => 'customers.risk_level',
                'customer_count' => 'COUNT(DISTINCT customers.id)',
                'apply_count' => 'COUNT(loan_applications.id)',
                'avg_amount' => 'AVG(loan_applications.apply_amount)',
                'approval_rate' => 'ROUND(COUNT(CASE WHEN loan_applications.status = "approved" THEN 1 END) / COUNT(loan_applications.id) * 100, 2)',
                'overdue_rate' => 'ROUND(COUNT(CASE WHEN loan_applications.status = "overdue" THEN 1 END) / COUNT(CASE WHEN loan_applications.status IN ("disbursed", "completed", "overdue") THEN 1 END) * 100, 2)'
            ],
            'joins' => [
                'LEFT JOIN loan_applications ON customers.id = loan_applications.customer_id'
            ],
            'group_by' => ['customers.risk_level'],
            'charts' => [
                ['type' => 'pie', 'label' => 'risk_level', 'value' => 'customer_count', 'title' => '客户风险等级分布'],
                ['type' => 'bar', 'x' => 'risk_level', 'y' => 'approval_rate', 'title' => '不同风险等级审批率'],
                ['type' => 'bar', 'x' => 'risk_level', 'y' => 'overdue_rate', 'title' => '不同风险等级逾期率']
            ]
        ],
        
        'collection_performance' => [
            'name' => '催收效果报表',
            'tables' => ['collection_records', 'users', 'loan_applications'],
            'fields' => [
                'collector_name' => 'users.real_name',
                'collection_count' => 'COUNT(collection_records.id)',
                'contact_success_rate' => 'ROUND(COUNT(CASE WHEN collection_records.contact_result = "connected" THEN 1 END) / COUNT(collection_records.id) * 100, 2)',
                'payment_promise_rate' => 'ROUND(COUNT(CASE WHEN collection_records.payment_promise != "refuse" THEN 1 END) / COUNT(CASE WHEN collection_records.contact_result = "connected" THEN 1 END) * 100, 2)',
                'avg_promised_amount' => 'AVG(collection_records.promised_amount)'
            ],
            'joins' => [
                'LEFT JOIN users ON collection_records.collector_id = users.id',
                'LEFT JOIN loan_applications ON collection_records.application_id = loan_applications.id'
            ],
            'conditions' => [
                'collection_records.collection_time >= :start_date',
                'collection_records.collection_time <= :end_date'
            ],
            'group_by' => ['collection_records.collector_id', 'users.real_name'],
            'charts' => [
                ['type' => 'bar', 'x' => 'collector_name', 'y' => 'collection_count', 'title' => '催收次数'],
                ['type' => 'bar', 'x' => 'collector_name', 'y' => 'contact_success_rate', 'title' => '联系成功率'],
                ['type' => 'bar', 'x' => 'collector_name', 'y' => 'payment_promise_rate', 'title' => '承诺还款率']
            ]
        ]
    ];
}

// 动态报表构建器
class DynamicReport
{
    private $config;
    
    public function __construct($config)
    {
        $this->config = $config;
    }
    
    /**
     * 构建SQL查询
     */
    public function buildQuery()
    {
        $select = [];
        foreach ($this->config['fields'] as $alias => $field) {
            $select[] = "{$field} AS {$alias}";
        }
        
        $sql = "SELECT " . implode(', ', $select);
        $sql .= " FROM " . $this->config['tables'][0];
        
        // 添加JOIN
        if (isset($this->config['joins'])) {
            foreach ($this->config['joins'] as $join) {
                $sql .= " " . $join;
            }
        }
        
        // 添加WHERE条件
        if (isset($this->config['conditions']) && !empty($this->config['conditions'])) {
            $sql .= " WHERE " . implode(' AND ', $this->config['conditions']);
        }
        
        // 添加GROUP BY
        if (isset($this->config['group_by']) && !empty($this->config['group_by'])) {
            $sql .= " GROUP BY " . implode(', ', $this->config['group_by']);
        }
        
        // 添加ORDER BY
        if (isset($this->config['order_by']) && !empty($this->config['order_by'])) {
            $sql .= " ORDER BY " . implode(', ', $this->config['order_by']);
        }
        
        return $sql;
    }
    
    /**
     * 执行查询
     */
    public function executeQuery($sql, $params = [])
    {
        return Db::query($sql, $params);
    }
    
    /**
     * 生成图表数据
     */
    public function generateCharts($data)
    {
        $charts = [];
        
        if (isset($this->config['charts'])) {
            foreach ($this->config['charts'] as $chartConfig) {
                $chartData = $this->processChartData($data, $chartConfig);
                $charts[] = [
                    'type' => $chartConfig['type'],
                    'title' => $chartConfig['title'],
                    'data' => $chartData
                ];
            }
        }
        
        return $charts;
    }
    
    /**
     * 处理图表数据
     */
    private function processChartData($data, $chartConfig)
    {
        $chartData = [];
        
        switch ($chartConfig['type']) {
            case 'line':
            case 'bar':
                foreach ($data as $row) {
                    $chartData[] = [
                        'x' => $row[$chartConfig['x']],
                        'y' => floatval($row[$chartConfig['y']])
                    ];
                }
                break;
                
            case 'pie':
                foreach ($data as $row) {
                    $chartData[] = [
                        'label' => $row[$chartConfig['label']],
                        'value' => floatval($row[$chartConfig['value']])
                    ];
                }
                break;
        }
        
        return $chartData;
    }
}
```

### 2. 数据导入导出系统

```php
// app/common/service/ImportExportService.php
<?php

namespace app\common\service;

use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\IOFactory;

class ImportExportService
{
    /**
     * 导出数据到Excel
     */
    public static function exportToExcel($data, $headers, $filename)
    {
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();
        
        // 设置表头
        $column = 'A';
        foreach ($headers as $header) {
            $sheet->setCellValue($column . '1', $header);
            $sheet->getStyle($column . '1')->getFont()->setBold(true);
            $column++;
        }
        
        // 填充数据
        $row = 2;
        foreach ($data as $rowData) {
            $column = 'A';
            foreach ($rowData as $value) {
                $sheet->setCellValue($column . $row, $value);
                $column++;
            }
            $row++;
        }
        
        // 自动调整列宽
        foreach (range('A', $sheet->getHighestColumn()) as $col) {
            $sheet->getColumnDimension($col)->setAutoSize(true);
        }
        
        // 保存文件
        $writer = new Xlsx($spreadsheet);
        $filepath = 'uploads/exports/' . $filename . '.xlsx';
        $writer->save($filepath);
        
        return $filepath;
    }
    
    /**
     * 从Excel导入数据
     */
    public static function importFromExcel($filepath, $mappings)
    {
        $spreadsheet = IOFactory::load($filepath);
        $sheet = $spreadsheet->getActiveSheet();
        $data = $sheet->toArray();
        
        // 获取表头
        $headers = array_shift($data);
        
        // 映射字段
        $fieldMappings = [];
        foreach ($headers as $index => $header) {
            if (isset($mappings[$header])) {
                $fieldMappings[$index] = $mappings[$header];
            }
        }
        
        // 处理数据
        $importData = [];
        $errors = [];
        
        foreach ($data as $rowIndex => $row) {
            $rowData = [];
            $hasError = false;
            
            foreach ($fieldMappings as $colIndex => $field) {
                $value = $row[$colIndex] ?? '';
                
                // 数据验证
                $validation = self::validateImportData($field, $value);
                if (!$validation['valid']) {
                    $errors[] = [
                        'row' => $rowIndex + 2,
                        'field' => $field,
                        'value' => $value,
                        'error' => $validation['message']
                    ];
                    $hasError = true;
                }
                
                $rowData[$field] = $validation['value'];
            }
            
            if (!$hasError) {
                $importData[] = $rowData;
            }
        }
        
        return [
            'data' => $importData,
            'errors' => $errors,
            'total' => count($data),
            'success' => count($importData),
            'failed' => count($errors)
        ];
    }
    
    /**
     * 批量导入客户数据
     */
    public static function importCustomers($filepath)
    {
        $mappings = [
            '客户姓名' => 'name',
            '身份证号' => 'id_card',
            '手机号' => 'phone',
            '性别' => 'gender',
            '月收入' => 'monthly_income',
            '行业' => 'industry',
            '职业' => 'occupation',
            '居住地址' => 'residence_address',
            '公司名称' => 'company_name'
        ];
        
        $result = self::importFromExcel($filepath, $mappings);
        
        if (!empty($result['data'])) {
            foreach ($result['data'] as $customerData) {
                try {
                    // 检查是否已存在
                    $exists = Customer::where('id_card', $customerData['id_card'])
                                    ->orWhere('phone', $customerData['phone'])
                                    ->find();
                    
                    if ($exists) {
                        $result['errors'][] = [
                            'data' => $customerData,
                            'error' => '客户已存在'
                        ];
                        continue;
                    }
                    
                    // 生成客户编号
                    $customerData['customer_no'] = CustomerService::generateCustomerNo();
                    $customerData['created_by'] = request()->user_id ?? 0;
                    
                    Customer::create($customerData);
                    
                } catch (\Exception $e) {
                    $result['errors'][] = [
                        'data' => $customerData,
                        'error' => $e->getMessage()
                    ];
                }
            }
        }
        
        return $result;
    }
    
    /**
     * 导出客户数据
     */
    public static function exportCustomers($conditions = [])
    {
        $query = Customer::where($conditions);
        $customers = $query->select();
        
        $headers = [
            '客户编号', '客户姓名', '身份证号', '手机号', '性别', 
            '月收入', '行业', '职业', '居住地址', '风险等级', '创建时间'
        ];
        
        $data = [];
        foreach ($customers as $customer) {
            $data[] = [
                $customer->customer_no,
                $customer->name,
                EncryptService::maskIdCard($customer->id_card),
                EncryptService::maskPhone($customer->phone),
                $customer->gender == 'male' ? '男' : '女',
                $customer->monthly_income,
                $customer->industry,
                $customer->occupation,
                $customer->residence_address,
                $customer->risk_level,
                $customer->created_at
            ];
        }
        
        $filename = 'customers_' . date('YmdHis');
        return self::exportToExcel($data, $headers, $filename);
    }
    
    /**
     * 数据验证
     */
    private static function validateImportData($field, $value)
    {
        $result = ['valid' => true, 'value' => $value, 'message' => ''];
        
        switch ($field) {
            case 'id_card':
                if (!preg_match('/^\d{15}$|^\d{17}[\dX]$/', $value)) {
                    $result['valid'] = false;
                    $result['message'] = '身份证号格式不正确';
                }
                break;
                
            case 'phone':
                if (!preg_match('/^1[3-9]\d{9}$/', $value)) {
                    $result['valid'] = false;
                    $result['message'] = '手机号格式不正确';
                }
                break;
                
            case 'monthly_income':
                if (!is_numeric($value) || $value < 0) {
                    $result['valid'] = false;
                    $result['message'] = '月收入必须为正数';
                } else {
                    $result['value'] = floatval($value);
                }
                break;
                
            case 'gender':
                $genderMap = ['男' => 'male', '女' => 'female'];
                if (isset($genderMap[$value])) {
                    $result['value'] = $genderMap[$value];
                } else {
                    $result['valid'] = false;
                    $result['message'] = '性别只能是男或女';
                }
                break;
        }
        
        return $result;
    }
}
```

### 3. 定时任务系统

```php
// app/common/service/CronService.php
<?php

namespace app\common\service;

class CronService
{
    /**
     * 定时任务配置
     */
    const CRON_JOBS = [
        'daily_statistics' => [
            'description' => '每日统计数据计算',
            'schedule' => '0 1 * * *', // 每天凌晨1点
            'class' => 'DailyStatisticsJob',
            'enabled' => true
        ],
        'overdue_check' => [
            'description' => '逾期检查',
            'schedule' => '0 */6 * * *', // 每6小时
            'class' => 'OverdueCheckJob',
            'enabled' => true
        ],
        'payment_reminder' => [
            'description' => '还款提醒',
            'schedule' => '0 9 * * *', // 每天上午9点
            'class' => 'PaymentReminderJob',
            'enabled' => true
        ],
        'data_backup' => [
            'description' => '数据备份',
            'schedule' => '0 2 * * 0', // 每周日凌晨2点
            'class' => 'DataBackupJob',
            'enabled' => true
        ],
        'log_cleanup' => [
            'description' => '日志清理',
            'schedule' => '0 3 1 * *', // 每月1号凌晨3点
            'class' => 'LogCleanupJob',
            'enabled' => true
        ]
    ];
    
    /**
     * 执行定时任务
     */
    public static function runJobs()
    {
        foreach (self::CRON_JOBS as $jobName => $config) {
            if (!$config['enabled']) {
                continue;
            }
            
            if (self::shouldRun($config['schedule'])) {
                self::runJob($jobName, $config);
            }
        }
    }
    
    /**
     * 检查是否应该运行
     */
    private static function shouldRun($schedule)
    {
        $cron = new CronExpression($schedule);
        return $cron->isDue();
    }
    
    /**
     * 运行单个任务
     */
    private static function runJob($jobName, $config)
    {
        try {
            $jobClass = "app\\common\\job\\{$config['class']}";
            
            if (!class_exists($jobClass)) {
                throw new \Exception("任务类 {$jobClass} 不存在");
            }
            
            $job = new $jobClass();
            $startTime = microtime(true);
            
            LogService::info("定时任务开始", ['job' => $jobName]);
            
            $result = $job->handle();
            
            $executionTime = round((microtime(true) - $startTime) * 1000, 2);
            
            LogService::info("定时任务完成", [
                'job' => $jobName,
                'execution_time' => $executionTime,
                'result' => $result
            ]);
            
            // 记录任务执行历史
            CronJobHistory::create([
                'job_name' => $jobName,
                'status' => 'success',
                'execution_time' => $executionTime,
                'result' => json_encode($result),
                'executed_at' => date('Y-m-d H:i:s')
            ]);
            
        } catch (\Exception $e) {
            LogService::error("定时任务失败", [
                'job' => $jobName,
                'error' => $e->getMessage()
            ]);
            
            CronJobHistory::create([
                'job_name' => $jobName,
                'status' => 'failed',
                'error_message' => $e->getMessage(),
                'executed_at' => date('Y-m-d H:i:s')
            ]);
        }
    }
}

// 逾期检查任务
class OverdueCheckJob
{
    public function handle()
    {
        $overdueLoans = LoanApplication::where('status', 'disbursed')
                                     ->where('due_time', '<', date('Y-m-d H:i:s'))
                                     ->select();
        
        $processedCount = 0;
        
        foreach ($overdueLoans as $loan) {
            // 更新状态为逾期
            $loan->status = 'overdue';
            $loan->save();
            
            // 发送逾期通知
            NotificationService::send('sms', [$loan->customer->phone], 
                NotificationService::TEMPLATES['overdue_notice'], [
                    'amount' => $loan->total_amount,
                    'days' => $this->calculateOverdueDays($loan->due_time)
                ]
            );
            
            // 创建催收任务
            CollectionTask::create([
                'application_id' => $loan->id,
                'customer_id' => $loan->customer_id,
                'task_type' => 'overdue',
                'priority' => $this->calculatePriority($loan),
                'status' => 'pending',
                'created_at' => date('Y-m-d H:i:s')
            ]);
            
            $processedCount++;
        }
        
        return ['processed_count' => $processedCount];
    }
    
    private function calculateOverdueDays($dueTime)
    {
        return floor((time() - strtotime($dueTime)) / 86400);
    }
    
    private function calculatePriority($loan)
    {
        $days = $this->calculateOverdueDays($loan->due_time);
        
        if ($days <= 3) return 'low';
        if ($days <= 7) return 'medium';
        if ($days <= 15) return 'high';
        return 'urgent';
    }
}

// 每日统计任务
class DailyStatisticsJob
{
    public function handle()
    {
        $date = date('Y-m-d', strtotime('-1 day'));
        
        $statistics = [
            'date' => $date,
            'new_customers' => $this->getNewCustomersCount($date),
            'new_applications' => $this->getNewApplicationsCount($date),
            'approved_applications' => $this->getApprovedApplicationsCount($date),
            'disbursed_amount' => $this->getDisbursedAmount($date),
            'repayment_amount' => $this->getRepaymentAmount($date),
            'overdue_count' => $this->getOverdueCount($date),
            'collection_count' => $this->getCollectionCount($date)
        ];
        
        // 保存统计数据
        DailyStatistics::create($statistics);
        
        // 清除相关缓存
        CacheService::clearRelated('statistics', $date);
        
        return $statistics;
    }
    
    private function getNewCustomersCount($date)
    {
        return Customer::whereDate('created_at', $date)->count();
    }
    
    private function getNewApplicationsCount($date)
    {
        return LoanApplication::whereDate('submit_time', $date)->count();
    }
    
    private function getApprovedApplicationsCount($date)
    {
        return LoanApplication::whereDate('approval_time', $date)
                             ->where('status', 'approved')
                             ->count();
    }
    
    private function getDisbursedAmount($date)
    {
        return LoanApplication::whereDate('disburse_time', $date)
                             ->where('status', 'disbursed')
                             ->sum('actual_amount');
    }
    
    private function getRepaymentAmount($date)
    {
        return RepaymentRecord::whereDate('repay_time', $date)
                             ->where('status', 'success')
                             ->sum('repay_amount');
    }
    
    private function getOverdueCount($date)
    {
        return LoanApplication::whereDate('updated_at', $date)
                             ->where('status', 'overdue')
                             ->count();
    }
    
    private function getCollectionCount($date)
    {
        return CollectionRecord::whereDate('collection_time', $date)->count();
    }
}
```

## 三、技术架构优化

### 1. Redis使用优化

```php
// app/common/service/RedisService.php
<?php

namespace app\common\service;

use think\facade\Cache;

class RedisService
{
    // 缓存键规范
    const CACHE_KEYS = [
        'user_session' => 'session:user:{user_id}',
        'user_permissions' => 'permission:user:{user_id}',
        'customer_info' => 'customer:{customer_id}',
        'loan_application' => 'loan:application:{application_id}',
        'risk_score' => 'risk:score:{customer_id}',
        'daily_statistics' => 'statistics:daily:{date}',
        'api_rate_limit' => 'rate_limit:{ip}:{endpoint}',
        'sms_rate_limit' => 'sms:rate_limit:{phone}',
        'login_attempts' => 'login:attempts:{ip}:{username}'
    ];
    
    /**
     * 设置缓存（支持标签）
     */
    public static function setWithTags($key, $value, $ttl = 3600, $tags = [])
    {
        Cache::tag($tags)->set($key, $value, $ttl);
    }
    
    /**
     * 批量清除标签缓存
     */
    public static function clearByTags($tags)
    {
        Cache::tag($tags)->clear();
    }
    
    /**
     * 分布式锁
     */
    public static function lock($key, $ttl = 10)
    {
        $lockKey = "lock:{$key}";
        $identifier = uniqid();
        
        if (Cache::store('redis')->handler()->set($lockKey, $identifier, 'EX', $ttl, 'NX')) {
            return $identifier;
        }
        
        return false;
    }
    
    /**
     * 释放锁
     */
    public static function unlock($key, $identifier)
    {
        $lockKey = "lock:{$key}";
        $lua = "
            if redis.call('get', KEYS[1]) == ARGV[1] then
                return redis.call('del', KEYS[1])
            else
                return 0
            end
        ";
        
        return Cache::store('redis')->handler()->eval($lua, [$lockKey, $identifier], 1);
    }
    
    /**
     * 计数器（原子操作）
     */
    public static function increment($key, $step = 1, $ttl = null)
    {
        $redis = Cache::store('redis')->handler();
        $current = $redis->incrBy($key, $step);
        
        if ($ttl && $current == $step) {
            $redis->expire($key, $ttl);
        }
        
        return $current;
    }
    
    /**
     * 限流器
     */
    public static function rateLimit($key, $maxRequests, $window)
    {
        $redis = Cache::store('redis')->handler();
        $current = time();
        $pipeline = $redis->pipeline();
        
        // 滑动窗口限流
        $pipeline->zRemRangeByScore($key, 0, $current - $window);
        $pipeline->zCard($key);
        $pipeline->zAdd($key, $current, $current . ':' . uniqid());
        $pipeline->expire($key, $window);
        
        $results = $pipeline->exec();
        $currentCount = $results[1];
        
        return $currentCount < $maxRequests;
    }
    
    /**
     * 延迟队列
     */
    public static function delayedQueue($queue, $message, $delay)
    {
        $executeTime = time() + $delay;
        $redis = Cache::store('redis')->handler();
        
        return $redis->zAdd("delayed_queue:{$queue}", $executeTime, json_encode([
            'message' => $message,
            'execute_time' => $executeTime,
            'created_at' => time()
        ]));
    }
    
    /**
     * 处理延迟队列
     */
    public static function processDelayedQueue($queue)
    {
        $redis = Cache::store('redis')->handler();
        $now = time();
        
        $messages = $redis->zRangeByScore("delayed_queue:{$queue}", 0, $now, ['limit' => [0, 10]]);
        
        foreach ($messages as $message) {
            $data = json_decode($message, true);
            
            // 处理消息
            QueueService::push($queue, $data['message']);
            
            // 从延迟队列中移除
            $redis->zRem("delayed_queue:{$queue}", $message);
        }
        
        return count($messages);
    }
}
```

### 2. 数据库读写分离

```php
// config/database.php
return [
    // 主库配置
    'master' => [
        'type' => 'mysql',
        'hostname' => '*************',
        'database' => 'daihou',
        'username' => 'root',
        'password' => 'password',
        'hostport' => 3306,
        'charset' => 'utf8mb4',
        'deploy' => 0,
        'rw_separate' => false
    ],
    
    // 从库配置
    'slave' => [
        'type' => 'mysql',
        'hostname' => '*************,*************',
        'database' => 'daihou',
        'username' => 'slave_user',
        'password' => 'slave_password',
        'hostport' => 3306,
        'charset' => 'utf8mb4',
        'deploy' => 1,
        'rw_separate' => true
    ]
];

// app/common/service/DatabaseService.php
class DatabaseService
{
    /**
     * 强制读主库
     */
    public static function readMaster($callback)
    {
        return Db::connect('master')->transaction($callback);
    }
    
    /**
     * 读从库
     */
    public static function readSlave($callback)
    {
        return Db::connect('slave')->transaction($callback);
    }
    
    /**
     * 智能路由
     */
    public static function smartQuery($sql, $params = [])
    {
        // 写操作使用主库
        if (preg_match('/^\s*(INSERT|UPDATE|DELETE|CREATE|ALTER|DROP)\s+/i', $sql)) {
            return Db::connect('master')->query($sql, $params);
        }
        
        // 读操作使用从库
        return Db::connect('slave')->query($sql, $params);
    }
}
```

## 四、运维监控优化

### 1. 系统监控

```php
// app/common/service/MonitorService.php
<?php

namespace app\common\service;

class MonitorService
{
    /**
     * 系统健康检查
     */
    public static function healthCheck()
    {
        $health = [
            'status' => 'healthy',
            'checks' => [],
            'timestamp' => date('Y-m-d H:i:s')
        ];
        
        // 数据库连接检查
        $health['checks']['database'] = self::checkDatabase();
        
        // Redis连接检查
        $health['checks']['redis'] = self::checkRedis();
        
        // 磁盘空间检查
        $health['checks']['disk'] = self::checkDiskSpace();
        
        // 内存使用检查
        $health['checks']['memory'] = self::checkMemoryUsage();
        
        // 队列状态检查
        $health['checks']['queue'] = self::checkQueueStatus();
        
        // 外部服务检查
        $health['checks']['external_services'] = self::checkExternalServices();
        
        // 总体状态判断
        foreach ($health['checks'] as $check) {
            if ($check['status'] !== 'healthy') {
                $health['status'] = 'unhealthy';
                break;
            }
        }
        
        return $health;
    }
    
    /**
     * 检查数据库连接
     */
    private static function checkDatabase()
    {
        try {
            $startTime = microtime(true);
            Db::query('SELECT 1');
            $responseTime = round((microtime(true) - $startTime) * 1000, 2);
            
            return [
                'status' => 'healthy',
                'response_time' => $responseTime . 'ms',
                'message' => '数据库连接正常'
            ];
            
        } catch (\Exception $e) {
            return [
                'status' => 'unhealthy',
                'error' => $e->getMessage(),
                'message' => '数据库连接失败'
            ];
        }
    }
    
    /**
     * 检查Redis连接
     */
    private static function checkRedis()
    {
        try {
            $startTime = microtime(true);
            Cache::set('health_check', time(), 60);
            $responseTime = round((microtime(true) - $startTime) * 1000, 2);
            
            return [
                'status' => 'healthy',
                'response_time' => $responseTime . 'ms',
                'message' => 'Redis连接正常'
            ];
            
        } catch (\Exception $e) {
            return [
                'status' => 'unhealthy',
                'error' => $e->getMessage(),
                'message' => 'Redis连接失败'
            ];
        }
    }
    
    /**
     * 检查磁盘空间
     */
    private static function checkDiskSpace()
    {
        $totalSpace = disk_total_space('/');
        $freeSpace = disk_free_space('/');
        $usedSpace = $totalSpace - $freeSpace;
        $usagePercent = round(($usedSpace / $totalSpace) * 100, 2);
        
        $status = $usagePercent > 90 ? 'unhealthy' : 'healthy';
        
        return [
            'status' => $status,
            'usage_percent' => $usagePercent,
            'free_space' => self::formatBytes($freeSpace),
            'total_space' => self::formatBytes($totalSpace),
            'message' => "磁盘使用率: {$usagePercent}%"
        ];
    }
    
    /**
     * 检查内存使用
     */
    private static function checkMemoryUsage()
    {
        $memoryUsage = memory_get_usage(true);
        $memoryLimit = ini_get('memory_limit');
        $memoryLimitBytes = self::parseBytes($memoryLimit);
        $usagePercent = round(($memoryUsage / $memoryLimitBytes) * 100, 2);
        
        $status = $usagePercent > 80 ? 'unhealthy' : 'healthy';
        
        return [
            'status' => $status,
            'usage_percent' => $usagePercent,
            'memory_usage' => self::formatBytes($memoryUsage),
            'memory_limit' => $memoryLimit,
            'message' => "内存使用率: {$usagePercent}%"
        ];
    }
    
    /**
     * 格式化字节
     */
    private static function formatBytes($bytes)
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);
        
        $bytes /= pow(1024, $pow);
        
        return round($bytes, 2) . ' ' . $units[$pow];
    }
    
    /**
     * 解析字节
     */
    private static function parseBytes($val)
    {
        $val = trim($val);
        $last = strtolower($val[strlen($val)-1]);
        $val = intval($val);
        
        switch($last) {
            case 'g': $val *= 1024;
            case 'm': $val *= 1024;
            case 'k': $val *= 1024;
        }
        
        return $val;
    }
}
```

## 五、总结

<function_calls>
<invoke name="todo_write">
<parameter name="merge">true
