<?php
declare (strict_types = 1);

namespace app\common\model;

use think\Model;

class MiniprogramComponent extends Model
{
    protected $name = 'miniprogram_components';

    protected $schema = [
        'id'               => 'int',
        'component_name'   => 'string',
        'component_type'   => 'string',
        'component_config' => 'json',
        'preview_image'    => 'string',
        'is_active'        => 'int',
        'sort_order'       => 'int',
        'created_at'       => 'datetime',
        'updated_at'       => 'datetime',
    ];

    protected $json = ['component_config'];
}


