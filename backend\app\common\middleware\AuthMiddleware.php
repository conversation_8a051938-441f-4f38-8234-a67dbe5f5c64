<?php
declare (strict_types = 1);

namespace app\common\middleware;

use app\common\service\JwtService;
use think\Response;

class AuthMiddleware
{
    /**
     * 处理请求
     *
     * @param \think\Request $request
     * @param \Closure       $next
     * @return Response
     */
    public function handle($request, \Closure $next)
    {
        $token = JwtService::getTokenFromRequest($request);
        
        if (!$token) {
            return json([
                'code' => 401,
                'message' => 'Token缺失',
                'data' => null
            ], 401);
        }

        $payload = JwtService::validateToken($token);
        
        if (!$payload) {
            return json([
                'code' => 401,
                'message' => 'Token无效或已过期',
                'data' => null
            ], 401);
        }

        // 将用户信息存储到请求中
        $request->user = $payload['user'];
        $request->user_id = $payload['user']['id'];

        return $next($request);
    }
}
