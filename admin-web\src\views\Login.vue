<template>
  <div class="login-container">
    <!-- 背景装饰 -->
    <div class="bg-decoration">
      <div class="bg-circle bg-circle-1"></div>
      <div class="bg-circle bg-circle-2"></div>
      <div class="bg-circle bg-circle-3"></div>
    </div>

    <div class="login-box">
      <!-- 系统状态指示器 -->
      <div class="system-status" :class="{ 'online': systemOnline }">
        <el-icon size="12">
          <CircleCheckFilled v-if="systemOnline" />
          <CircleCloseFilled v-else />
        </el-icon>
        <span>{{ systemOnline ? '系统正常' : '系统异常' }}</span>
      </div>

      <div class="login-header">
        <div class="logo">
          <el-icon size="48" color="#409EFF">
            <Monitor />
          </el-icon>
        </div>
        <h1 class="title">民间空放贷后管理系统</h1>
        <p class="subtitle">管理端控制台 v2.0</p>
      </div>

      <el-form
        ref="loginFormRef"
        :model="loginForm"
        :rules="loginRules"
        class="login-form"
        size="large"
        @submit.prevent="handleLogin"
      >
        <el-form-item prop="username">
          <el-input
            v-model="loginForm.username"
            placeholder="请输入用户名"
            prefix-icon="User"
            clearable
            :disabled="loading"
            autocomplete="username"
          />
        </el-form-item>

        <el-form-item prop="password">
          <el-input
            v-model="loginForm.password"
            type="password"
            placeholder="请输入密码"
            prefix-icon="Lock"
            show-password
            :disabled="loading"
            autocomplete="current-password"
            @keyup.enter="handleLogin"
          />
        </el-form-item>

        <!-- 验证码 -->
        <el-form-item v-if="showCaptcha" prop="captcha">
          <div class="captcha-container">
            <el-input
              v-model="loginForm.captcha"
              placeholder="请输入验证码"
              prefix-icon="Key"
              :disabled="loading"
              style="flex: 1; margin-right: 10px;"
            />
            <div class="captcha-image" @click="refreshCaptcha">
              <img :src="captchaImage" alt="验证码" />
              <div class="captcha-refresh">
                <el-icon><Refresh /></el-icon>
              </div>
            </div>
          </div>
        </el-form-item>

        <!-- 记住登录 -->
        <el-form-item>
          <div class="login-options">
            <el-checkbox v-model="loginForm.remember" :disabled="loading">
              记住登录状态
            </el-checkbox>
            <el-link type="primary" :underline="false" @click="showForgotPassword">
              忘记密码？
            </el-link>
          </div>
        </el-form-item>

        <el-form-item>
          <el-button
            type="primary"
            class="login-btn"
            :loading="loading"
            :disabled="!systemOnline"
            @click="handleLogin"
          >
            <template #loading>
              <el-icon class="is-loading"><Loading /></el-icon>
            </template>
            {{ loading ? '登录中...' : '登录' }}
          </el-button>
        </el-form-item>
      </el-form>

      <!-- 快速登录选项 -->
      <div class="quick-login" v-if="!loading">
        <el-divider>
          <span class="divider-text">快速登录</span>
        </el-divider>
        <div class="quick-login-buttons">
          <el-button
            size="small"
            @click="quickLogin('admin')"
            :disabled="!systemOnline"
          >
            管理员
          </el-button>
          <el-button
            size="small"
            @click="quickLogin('demo')"
            :disabled="!systemOnline"
          >
            演示账号
          </el-button>
        </div>
      </div>

      <!-- 登录统计 -->
      <div class="login-stats">
        <div class="stat-item">
          <span class="stat-label">今日登录:</span>
          <span class="stat-value">{{ loginStats.today }}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">在线用户:</span>
          <span class="stat-value">{{ loginStats.online }}</span>
        </div>
      </div>

      <div class="login-footer">
        <p>&copy; 2025 iApp 贷后管理系统 | 版本 v2.0.0</p>
        <p class="footer-links">
          <el-link type="info" size="small" @click="showHelp">使用帮助</el-link>
          <el-divider direction="vertical" />
          <el-link type="info" size="small" @click="showPrivacy">隐私政策</el-link>
        </p>
      </div>
    </div>

    <!-- 帮助对话框 -->
    <el-dialog v-model="helpVisible" title="使用帮助" width="500px">
      <div class="help-content">
        <h4>默认账号信息：</h4>
        <ul>
          <li>管理员账号：admin / password</li>
          <li>演示账号：demo / demo123</li>
        </ul>
        <h4>功能说明：</h4>
        <ul>
          <li>支持记住登录状态</li>
          <li>登录失败3次后需要验证码</li>
          <li>系统会自动检测在线状态</li>
        </ul>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox, type FormInstance } from 'element-plus'
import {
  Monitor,
  CircleCheckFilled,
  CircleCloseFilled,
  Refresh,
  Loading
} from '@element-plus/icons-vue'
import { useAuthStore } from '../stores/auth'

const router = useRouter()
const authStore = useAuthStore()

const loginFormRef = ref<FormInstance>()
const loading = ref(false)
const systemOnline = ref(true)
const showCaptcha = ref(false)
const captchaImage = ref('')
const helpVisible = ref(false)
const loginAttempts = ref(0)

const loginForm = reactive({
  username: '',
  password: '',
  captcha: '',
  remember: false
})

const loginStats = reactive({
  today: 0,
  online: 0
})

const loginRules = computed(() => ({
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 2, max: 20, message: '用户名长度在2到20个字符', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度在6到20个字符', trigger: 'blur' }
  ],
  captcha: showCaptcha.value ? [
    { required: true, message: '请输入验证码', trigger: 'blur' },
    { len: 4, message: '验证码长度为4位', trigger: 'blur' }
  ] : []
}))

// 初始化
onMounted(() => {
  checkSystemStatus()
  loadLoginStats()
  loadRememberedLogin()
  generateCaptcha()
})

// 检查系统状态
const checkSystemStatus = async () => {
  try {
    // 这里可以调用健康检查接口
    systemOnline.value = true
  } catch (error) {
    systemOnline.value = false
    ElMessage.warning('系统连接异常，请稍后重试')
  }
}

// 加载登录统计
const loadLoginStats = async () => {
  try {
    // 模拟数据，实际应该从API获取
    loginStats.today = 23
    loginStats.online = 5
  } catch (error) {
    console.error('Failed to load login stats:', error)
  }
}

// 加载记住的登录信息
const loadRememberedLogin = () => {
  const remembered = localStorage.getItem('remembered_login')
  if (remembered) {
    try {
      const data = JSON.parse(remembered)
      loginForm.username = data.username || ''
      loginForm.remember = true
    } catch (error) {
      console.error('Failed to load remembered login:', error)
    }
  }
}

// 生成验证码
const generateCaptcha = () => {
  // 生成简单的数学验证码
  const num1 = Math.floor(Math.random() * 10)
  const num2 = Math.floor(Math.random() * 10)
  const operators = ['+', '-', '×']
  const operator = operators[Math.floor(Math.random() * operators.length)]

  let result
  switch (operator) {
    case '+':
      result = num1 + num2
      break
    case '-':
      result = Math.abs(num1 - num2)
      break
    case '×':
      result = num1 * num2
      break
  }

  // 这里应该生成真实的验证码图片
  captchaImage.value = `data:image/svg+xml;base64,${btoa(`
    <svg width="100" height="40" xmlns="http://www.w3.org/2000/svg">
      <rect width="100" height="40" fill="#f0f0f0"/>
      <text x="50%" y="50%" text-anchor="middle" dy=".3em" font-family="Arial" font-size="16" fill="#333">
        ${num1} ${operator} ${num2} = ?
      </text>
    </svg>
  `)}`

  // 存储正确答案用于验证
  sessionStorage.setItem('captcha_answer', result.toString())
}

// 刷新验证码
const refreshCaptcha = () => {
  generateCaptcha()
  loginForm.captcha = ''
}

// 处理登录
const handleLogin = async () => {
  if (!loginFormRef.value) return

  try {
    await loginFormRef.value.validate()

    // 验证码检查
    if (showCaptcha.value) {
      const correctAnswer = sessionStorage.getItem('captcha_answer')
      if (loginForm.captcha !== correctAnswer) {
        ElMessage.error('验证码错误')
        refreshCaptcha()
        return
      }
    }

    loading.value = true

    await authStore.login({
      username: loginForm.username,
      password: loginForm.password,
      remember: loginForm.remember
    })

    // 保存记住的登录信息
    if (loginForm.remember) {
      localStorage.setItem('remembered_login', JSON.stringify({
        username: loginForm.username
      }))
    } else {
      localStorage.removeItem('remembered_login')
    }

    // 重置登录尝试次数
    loginAttempts.value = 0
    localStorage.removeItem('login_attempts')

    ElMessage.success('登录成功')
    router.push('/dashboard')

  } catch (error) {
    console.error('Login failed:', error)

    // 增加登录尝试次数
    loginAttempts.value++
    localStorage.setItem('login_attempts', loginAttempts.value.toString())

    // 3次失败后显示验证码
    if (loginAttempts.value >= 3) {
      showCaptcha.value = true
      generateCaptcha()
    }

    ElMessage.error(error.message || '登录失败，请检查用户名和密码')
  } finally {
    loading.value = false
  }
}

// 快速登录
const quickLogin = (type: string) => {
  if (type === 'admin') {
    loginForm.username = 'admin'
    loginForm.password = 'password'
  } else if (type === 'demo') {
    loginForm.username = 'demo'
    loginForm.password = 'demo123'
  }

  handleLogin()
}

// 显示忘记密码
const showForgotPassword = () => {
  ElMessageBox.alert(
    '请联系系统管理员重置密码，或使用演示账号进行体验。',
    '忘记密码',
    {
      confirmButtonText: '确定',
      type: 'info'
    }
  )
}

// 显示帮助
const showHelp = () => {
  helpVisible.value = true
}

// 显示隐私政策
const showPrivacy = () => {
  ElMessageBox.alert(
    '本系统严格保护用户隐私，所有数据均经过加密处理，仅用于业务管理目的。',
    '隐私政策',
    {
      confirmButtonText: '确定',
      type: 'info'
    }
  )
}
</script>

<style scoped>
.login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  position: relative;
  overflow: hidden;
}

/* 背景装饰 */
.bg-decoration {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 0;
}

.bg-circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: float 6s ease-in-out infinite;
}

.bg-circle-1 {
  width: 200px;
  height: 200px;
  top: 10%;
  left: 10%;
  animation-delay: 0s;
}

.bg-circle-2 {
  width: 150px;
  height: 150px;
  top: 60%;
  right: 10%;
  animation-delay: 2s;
}

.bg-circle-3 {
  width: 100px;
  height: 100px;
  bottom: 20%;
  left: 20%;
  animation-delay: 4s;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(180deg); }
}

.login-box {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 24px;
  padding: 40px;
  width: 100%;
  max-width: 420px;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
  position: relative;
  z-index: 1;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* 系统状态指示器 */
.system-status {
  position: absolute;
  top: 15px;
  right: 20px;
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 12px;
  color: #ff4d4f;
  transition: color 0.3s ease;
}

.system-status.online {
  color: #52c41a;
}

.login-header {
  text-align: center;
  margin-bottom: 40px;
}

.logo {
  margin-bottom: 20px;
  animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

.title {
  font-size: 26px;
  font-weight: bold;
  color: #333;
  margin: 0 0 10px 0;
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.subtitle {
  color: #666;
  margin: 0;
  font-size: 14px;
  font-weight: 500;
}

.login-form {
  margin-bottom: 30px;
}

/* 验证码容器 */
.captcha-container {
  display: flex;
  align-items: center;
}

.captcha-image {
  position: relative;
  cursor: pointer;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  overflow: hidden;
  transition: border-color 0.3s ease;
}

.captcha-image:hover {
  border-color: #409eff;
}

.captcha-image img {
  display: block;
  width: 100px;
  height: 40px;
}

.captcha-refresh {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
  color: white;
}

.captcha-image:hover .captcha-refresh {
  opacity: 1;
}

/* 登录选项 */
.login-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.login-btn {
  width: 100%;
  height: 48px;
  font-size: 16px;
  font-weight: 600;
  border-radius: 12px;
  transition: all 0.3s ease;
}

.login-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(64, 158, 255, 0.3);
}

/* 快速登录 */
.quick-login {
  margin-bottom: 30px;
}

.divider-text {
  color: #999;
  font-size: 12px;
  padding: 0 15px;
  background: rgba(255, 255, 255, 0.95);
}

.quick-login-buttons {
  display: flex;
  gap: 10px;
  justify-content: center;
}

/* 登录统计 */
.login-stats {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
  padding: 15px;
  background: rgba(64, 158, 255, 0.05);
  border-radius: 8px;
  border: 1px solid rgba(64, 158, 255, 0.1);
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5px;
}

.stat-label {
  font-size: 12px;
  color: #666;
}

.stat-value {
  font-size: 18px;
  font-weight: bold;
  color: #409eff;
}

.login-footer {
  text-align: center;
  color: #999;
  font-size: 12px;
}

.footer-links {
  margin-top: 10px;
}

/* 帮助内容 */
.help-content h4 {
  color: #333;
  margin: 15px 0 10px 0;
}

.help-content ul {
  margin: 0 0 15px 0;
  padding-left: 20px;
}

.help-content li {
  margin-bottom: 5px;
  color: #666;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .login-container {
    padding: 10px;
  }

  .login-box {
    padding: 30px 20px;
    max-width: 100%;
  }

  .title {
    font-size: 22px;
  }

  .login-stats {
    flex-direction: column;
    gap: 15px;
  }

  .stat-item {
    flex-direction: row;
    justify-content: space-between;
  }
}

/* 暗色模式支持 */
@media (prefers-color-scheme: dark) {
  .login-box {
    background: rgba(30, 30, 30, 0.95);
    color: #fff;
  }

  .title {
    color: #fff;
  }

  .subtitle {
    color: #ccc;
  }

  .login-stats {
    background: rgba(64, 158, 255, 0.1);
  }

  .stat-label {
    color: #ccc;
  }
}
</style>
