<?php
// 简单的API测试脚本

function testAPI($url, $method = 'GET', $data = null, $headers = []) {
    $curl = curl_init();
    
    curl_setopt_array($curl, [
        CURLOPT_URL => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_CUSTOMREQUEST => $method,
        CURLOPT_HTTPHEADER => $headers,
        CURLOPT_TIMEOUT => 10
    ]);
    
    if ($data && in_array($method, ['POST', 'PUT'])) {
        curl_setopt($curl, CURLOPT_POSTFIELDS, json_encode($data));
    }
    
    $response = curl_exec($curl);
    $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
    $error = curl_error($curl);
    curl_close($curl);
    
    return [
        'http_code' => $httpCode,
        'response' => $response,
        'error' => $error
    ];
}

echo "=== 民间空放贷后管理系统 API 测试 ===\n\n";

// 1. 测试健康检查
echo "1. 测试健康检查...\n";
$result = testAPI('http://localhost:8000/health');
echo "HTTP Code: " . $result['http_code'] . "\n";
echo "Response: " . $result['response'] . "\n\n";

// 2. 测试登录
echo "2. 测试用户登录...\n";
$loginData = [
    'username' => 'admin',
    'password' => 'password'
];
$result = testAPI('http://localhost:8000/api/v1/auth/login', 'POST', $loginData, [
    'Content-Type: application/json'
]);
echo "HTTP Code: " . $result['http_code'] . "\n";
echo "Response: " . $result['response'] . "\n\n";

// 解析登录响应获取token
$loginResponse = json_decode($result['response'], true);
$token = '';
if (isset($loginResponse['data']['token'])) {
    $token = $loginResponse['data']['token'];
    echo "Login Token: " . substr($token, 0, 50) . "...\n\n";
    
    // 3. 测试获取用户信息
    echo "3. 测试获取用户信息...\n";
    $result = testAPI('http://localhost:8000/api/v1/auth/user', 'GET', null, [
        'Content-Type: application/json',
        'Authorization: Bearer ' . $token
    ]);
    echo "HTTP Code: " . $result['http_code'] . "\n";
    echo "Response: " . $result['response'] . "\n\n";
    
    // 4. 测试获取客户列表
    echo "4. 测试获取客户列表...\n";
    $result = testAPI('http://localhost:8000/api/v1/customers', 'GET', null, [
        'Content-Type: application/json',
        'Authorization: Bearer ' . $token
    ]);
    echo "HTTP Code: " . $result['http_code'] . "\n";
    echo "Response: " . $result['response'] . "\n\n";
    
    // 5. 测试财务统计
    echo "5. 测试财务统计...\n";
    $result = testAPI('http://localhost:8000/api/v1/finance/statistics', 'GET', null, [
        'Content-Type: application/json',
        'Authorization: Bearer ' . $token
    ]);
    echo "HTTP Code: " . $result['http_code'] . "\n";
    echo "Response: " . $result['response'] . "\n\n";
} else {
    echo "登录失败，无法继续测试\n";
}

echo "=== 测试完成 ===\n";
?>
