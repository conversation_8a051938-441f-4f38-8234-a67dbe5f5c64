<?php

return [
    // 短信配置
    'sms' => [
        // 默认提供商 (aliyun|tencent|huawei)
        'provider' => env('SMS_PROVIDER', 'aliyun'),
        
        // 阿里云短信配置
        'aliyun' => [
            'access_key' => env('SMS_ALIYUN_ACCESS_KEY', ''),
            'access_secret' => env('SMS_ALIYUN_ACCESS_SECRET', ''),
            'sign_name' => env('SMS_ALIYUN_SIGN_NAME', ''),
            'region' => env('SMS_ALIYUN_REGION', 'cn-hangzhou'),
            // 模板配置
            'templates' => [
                'repayment_reminder' => 'SMS_123456789', // 还款提醒模板
                'overdue_notice' => 'SMS_123456790',     // 逾期通知模板
                'loan_approved' => 'SMS_123456791',      // 放款通过模板
                'verify_code' => 'SMS_123456792',        // 验证码模板
            ]
        ],
        
        // 腾讯云短信配置
        'tencent' => [
            'secret_id' => env('SMS_TENCENT_SECRET_ID', ''),
            'secret_key' => env('SMS_TENCENT_SECRET_KEY', ''),
            'sdk_app_id' => env('SMS_TENCENT_SDK_APP_ID', ''),
            'sign_name' => env('SMS_TENCENT_SIGN_NAME', ''),
            'region' => env('SMS_TENCENT_REGION', 'ap-beijing'),
            'templates' => [
                'repayment_reminder' => '123456',
                'overdue_notice' => '123457',
                'loan_approved' => '123458',
                'verify_code' => '123459',
            ]
        ],
        
        // 华为云短信配置
        'huawei' => [
            'app_key' => env('SMS_HUAWEI_APP_KEY', ''),
            'app_secret' => env('SMS_HUAWEI_APP_SECRET', ''),
            'sender' => env('SMS_HUAWEI_SENDER', ''),
            'signature' => env('SMS_HUAWEI_SIGNATURE', ''),
            'url' => env('SMS_HUAWEI_URL', 'https://smsapi.cn-north-4.myhuaweicloud.com:443'),
            'templates' => [
                'repayment_reminder' => 'template_id_1',
                'overdue_notice' => 'template_id_2',
                'loan_approved' => 'template_id_3',
                'verify_code' => 'template_id_4',
            ]
        ],
        
        // 全局配置
        'access_key' => env('SMS_ACCESS_KEY', ''),
        'access_secret' => env('SMS_ACCESS_SECRET', ''),
        'sign_name' => env('SMS_SIGN_NAME', ''),
        'region' => env('SMS_REGION', 'cn-hangzhou'),
        
        // 发送限制
        'rate_limit' => [
            'enabled' => true,
            'max_per_minute' => 10,  // 每分钟最大发送数
            'max_per_hour' => 100,   // 每小时最大发送数
            'max_per_day' => 1000,   // 每天最大发送数
        ],
        
        // 黑名单手机号（测试用）
        'blacklist' => [
            // '13800000000',
        ],
    ],
    
    // 邮件配置
    'email' => [
        // 邮件发送方式 (smtp|sendmail)
        'driver' => env('MAIL_DRIVER', 'smtp'),
        
        // SMTP 配置
        'host' => env('MAIL_HOST', 'smtp.qq.com'),
        'port' => env('MAIL_PORT', 587),
        'username' => env('MAIL_USERNAME', ''),
        'password' => env('MAIL_PASSWORD', ''),
        'encryption' => env('MAIL_ENCRYPTION', 'tls'), // tls|ssl
        
        // 发送方信息
        'from_email' => env('MAIL_FROM_ADDRESS', ''),
        'from_name' => env('MAIL_FROM_NAME', '贷后管理系统'),
        
        // 邮件模板配置
        'templates' => [
            'repayment_reminder' => [
                'subject' => '还款提醒通知',
                'template' => 'email/repayment_reminder'
            ],
            'overdue_notice' => [
                'subject' => '逾期还款通知',
                'template' => 'email/overdue_notice'
            ],
            'loan_approved' => [
                'subject' => '贷款审批通过通知',
                'template' => 'email/loan_approved'
            ],
            'password_reset' => [
                'subject' => '密码重置',
                'template' => 'email/password_reset'
            ],
        ],
        
        // 发送限制
        'rate_limit' => [
            'enabled' => true,
            'max_per_minute' => 5,
            'max_per_hour' => 50,
            'max_per_day' => 500,
        ]
    ],
    
    // 微信通知配置
    'wechat' => [
        'enabled' => false,
        
        // 微信公众号配置
        'official_account' => [
            'app_id' => env('WECHAT_OFFICIAL_ACCOUNT_APPID', ''),
            'secret' => env('WECHAT_OFFICIAL_ACCOUNT_SECRET', ''),
            'token' => env('WECHAT_OFFICIAL_ACCOUNT_TOKEN', ''),
            'aes_key' => env('WECHAT_OFFICIAL_ACCOUNT_AES_KEY', ''),
        ],
        
        // 企业微信配置
        'work' => [
            'corp_id' => env('WECHAT_WORK_CORP_ID', ''),
            'secret' => env('WECHAT_WORK_SECRET', ''),
            'agent_id' => env('WECHAT_WORK_AGENT_ID', ''),
        ],
        
        // 模板消息配置
        'templates' => [
            'repayment_reminder' => 'template_id_1',
            'overdue_notice' => 'template_id_2',
            'loan_approved' => 'template_id_3',
        ]
    ],
    
    // 系统通知配置
    'system' => [
        'enabled' => true,
        
        // 默认通知级别
        'default_level' => 'info', // info|warning|error|success
        
        // 通知保留时间（天）
        'retention_days' => 30,
        
        // 自动清理已读通知
        'auto_cleanup_read' => true,
        
        // 实时推送配置（WebSocket）
        'realtime_push' => [
            'enabled' => false,
            'server_host' => env('WEBSOCKET_HOST', '127.0.0.1'),
            'server_port' => env('WEBSOCKET_PORT', 2346),
        ]
    ],
    
    // 通用配置
    'common' => [
        // 异步发送
        'async_send' => env('NOTIFICATION_ASYNC', true),
        
        // 重试配置
        'retry' => [
            'enabled' => true,
            'max_attempts' => 3,
            'delay_seconds' => 60, // 重试间隔（秒）
        ],
        
        // 日志配置
        'log' => [
            'enabled' => true,
            'level' => 'info', // debug|info|warning|error
            'channel' => 'notification',
        ],
        
        // 统计分析
        'analytics' => [
            'enabled' => true,
            'retention_days' => 90, // 统计数据保留天数
        ]
    ],
    
    // 模板变量配置
    'template_vars' => [
        // 客户相关
        'customer_name' => '客户姓名',
        'customer_phone' => '客户手机号',
        
        // 金额相关  
        'amount' => '金额',
        'loan_amount' => '放款金额',
        'repayment_amount' => '还款金额',
        'overdue_amount' => '逾期金额',
        
        // 日期相关
        'due_date' => '到期日期',
        'loan_date' => '放款日期',
        'repayment_date' => '还款日期',
        
        // 其他
        'overdue_days' => '逾期天数',
        'business_flow_no' => '业务流水号',
        'verify_code' => '验证码',
        'company_name' => '公司名称',
        'contact_phone' => '联系电话',
    ]
];
