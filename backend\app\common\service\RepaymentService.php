<?php
declare (strict_types = 1);

namespace app\common\service;

use app\common\model\LoanDisbursementRecord;
use app\common\model\RepaymentRecord;
use app\common\model\RepaymentScheduleDetail;
use think\facade\Db;

class RepaymentService
{
    /**
     * 创建还款登记
     */
    public static function createRepaymentRecord($data)
    {
        Db::startTrans();
        try {
            // 1. 生成业务流水号
            $businessFlowNo = self::generateRepaymentFlowNo();
            
            // 2. 获取放款记录
            $disbursementRecord = LoanDisbursementRecord::find($data['disbursement_record_id']);
            if (!$disbursementRecord) {
                throw new \Exception('放款记录不存在');
            }
            
            // 3. 创建还款记录
            $repaymentRecord = RepaymentRecord::create([
                'business_flow_no' => $businessFlowNo,
                'disbursement_record_id' => $data['disbursement_record_id'],
                'customer_id' => $disbursementRecord->customer_id,
                'customer_name' => $disbursementRecord->customer_name,
                'repayment_method' => $data['repayment_method'],
                'repayment_amount' => $data['repayment_amount'],
                'repayment_type' => $data['repayment_type'],
                'payment_screenshot' => $data['payment_screenshot'] ?? null,
                'remark' => $data['remark'] ?? null,
                'overdue_days' => $data['overdue_days'] ?? 0,
                'overdue_fee' => $data['overdue_fee'] ?? 0,
                'created_by' => $data['created_by'] ?? 0
            ]);
            
            // 4. 更新放款记录的统计数据
            self::updateDisbursementStatistics($disbursementRecord, $data['repayment_amount']);
            
            // 5. 更新还款计划
            self::updateRepaymentSchedule($disbursementRecord, $data['repayment_amount'], $data['repayment_type']);
            
            // 6. 检查是否结清，处理后返佣金
            if ($disbursementRecord->fresh()->remaining_amount <= 0) {
                self::processBackCommission($disbursementRecord);
            }
            
            Db::commit();
            return $repaymentRecord;
            
        } catch (\Exception $e) {
            Db::rollback();
            throw $e;
        }
    }
    
    /**
     * 生成还款流水号
     */
    private static function generateRepaymentFlowNo(): string
    {
        return 'REP' . date('YmdHis') . sprintf('%03d', rand(1, 999));
    }
    
    /**
     * 更新放款记录统计
     */
    private static function updateDisbursementStatistics($disbursementRecord, $repaymentAmount)
    {
        // 更新回款总额
        $newTotalRepaid = $disbursementRecord->total_repaid_amount + $repaymentAmount;
        
        // 更新未回款总额
        $newRemainingAmount = $disbursementRecord->remaining_amount - $repaymentAmount;
        
        // 计算客户利润 = 回款总额 - 放款金额 - 中介返点
        $customerProfit = $newTotalRepaid - $disbursementRecord->loan_amount - $disbursementRecord->agent_commission_amount;
        
        // 更新状态
        $status = $disbursementRecord->status;
        if ($newRemainingAmount <= 0) {
            $status = 'completed';
        }
        
        $disbursementRecord->save([
            'total_repaid_amount' => $newTotalRepaid,
            'remaining_amount' => max(0, $newRemainingAmount),
            'customer_profit' => $customerProfit,
            'status' => $status
        ]);
    }
    
    /**
     * 更新还款计划
     */
    private static function updateRepaymentSchedule($disbursementRecord, $repaymentAmount, $repaymentType)
    {
        // 获取未还清的计划，按期数排序
        $pendingSchedules = RepaymentScheduleDetail::where('disbursement_record_id', $disbursementRecord->id)
            ->whereIn('status', ['pending', 'partial'])
            ->order('period_number')
            ->select();
        
        $remainingAmount = $repaymentAmount;
        
        foreach ($pendingSchedules as $schedule) {
            if ($remainingAmount <= 0) break;
            
            $scheduleRemaining = $schedule->remaining_amount;
            
            if ($remainingAmount >= $scheduleRemaining) {
                // 当期完全结清
                $schedule->save([
                    'paid_amount' => $schedule->due_amount,
                    'remaining_amount' => 0,
                    'status' => 'paid'
                ]);
                $remainingAmount -= $scheduleRemaining;
            } else {
                // 当期部分还款
                $newPaidAmount = $schedule->paid_amount + $remainingAmount;
                $newRemainingAmount = $schedule->due_amount - $newPaidAmount;
                
                $schedule->save([
                    'paid_amount' => $newPaidAmount,
                    'remaining_amount' => $newRemainingAmount,
                    'status' => 'partial'
                ]);
                $remainingAmount = 0;
            }
        }
        
        // 如果是提前结清，将所有剩余计划标记为已付
        if ($repaymentType === 'early_settlement' && $remainingAmount > 0) {
            RepaymentScheduleDetail::where('disbursement_record_id', $disbursementRecord->id)
                ->whereIn('status', ['pending', 'partial'])
                ->update([
                    'paid_amount' => Db::raw('due_amount'),
                    'remaining_amount' => 0,
                    'status' => 'paid'
                ]);
        }
    }
    
    /**
     * 处理后返佣金
     */
    private static function processBackCommission($disbursementRecord)
    {
        if ($disbursementRecord->back_commission_amount > 0 && 
            !$disbursementRecord->back_commission_paid && 
            $disbursementRecord->customer_profit > 0) {
            
            // 只有在有利润的情况下才支付后返
            // 这里可以调用 AgentCommissionService::payBackCommission
            // 为了演示，直接更新状态
            $disbursementRecord->save(['back_commission_paid' => 1]);
        }
    }
}
