<?php
declare(strict_types=1);

namespace app\common\service;

use think\Response;
use think\facade\Log;

/**
 * 标准化API响应服务
 * 提供统一的API响应格式和状态码管理
 */
class StandardResponseService
{
    /**
     * HTTP状态码常量
     */
    const HTTP_OK = 200;
    const HTTP_CREATED = 201;
    const HTTP_ACCEPTED = 202;
    const HTTP_NO_CONTENT = 204;
    const HTTP_BAD_REQUEST = 400;
    const HTTP_UNAUTHORIZED = 401;
    const HTTP_FORBIDDEN = 403;
    const HTTP_NOT_FOUND = 404;
    const HTTP_METHOD_NOT_ALLOWED = 405;
    const HTTP_CONFLICT = 409;
    const HTTP_UNPROCESSABLE_ENTITY = 422;
    const HTTP_TOO_MANY_REQUESTS = 429;
    const HTTP_INTERNAL_SERVER_ERROR = 500;
    const HTTP_SERVICE_UNAVAILABLE = 503;

    /**
     * 业务状态码常量
     */
    const SUCCESS = 200;
    const CREATED = 201;
    const ACCEPTED = 202;
    const NO_CONTENT = 204;
    const BAD_REQUEST = 400;
    const UNAUTHORIZED = 401;
    const FORBIDDEN = 403;
    const NOT_FOUND = 404;
    const VALIDATION_ERROR = 422;
    const INTERNAL_ERROR = 500;

    /**
     * 响应消息映射
     */
    const MESSAGES = [
        self::SUCCESS => '操作成功',
        self::CREATED => '创建成功',
        self::ACCEPTED => '请求已接受',
        self::NO_CONTENT => '无内容',
        self::BAD_REQUEST => '请求参数错误',
        self::UNAUTHORIZED => '未授权访问',
        self::FORBIDDEN => '禁止访问',
        self::NOT_FOUND => '资源不存在',
        self::VALIDATION_ERROR => '数据验证失败',
        self::INTERNAL_ERROR => '服务器内部错误'
    ];

    /**
     * 成功响应
     * @param mixed $data 响应数据
     * @param string $message 响应消息
     * @param int $code 业务状态码
     * @param int $httpCode HTTP状态码
     * @param array $meta 元数据
     * @return Response
     */
    public static function success(
        $data = null, 
        string $message = '', 
        int $code = self::SUCCESS, 
        int $httpCode = self::HTTP_OK,
        array $meta = []
    ): Response {
        if (empty($message)) {
            $message = self::MESSAGES[$code] ?? '操作成功';
        }

        $response = [
            'success' => true,
            'code' => $code,
            'message' => $message,
            'data' => $data,
            'timestamp' => time(),
            'request_id' => self::generateRequestId()
        ];

        if (!empty($meta)) {
            $response['meta'] = $meta;
        }

        return json($response, $httpCode);
    }

    /**
     * 错误响应
     * @param string $message 错误消息
     * @param int $code 业务状态码
     * @param mixed $data 错误数据
     * @param int $httpCode HTTP状态码
     * @param array $errors 详细错误信息
     * @return Response
     */
    public static function error(
        string $message = '', 
        int $code = self::BAD_REQUEST, 
        $data = null, 
        int $httpCode = 0,
        array $errors = []
    ): Response {
        if (empty($message)) {
            $message = self::MESSAGES[$code] ?? '操作失败';
        }

        if ($httpCode === 0) {
            $httpCode = self::getHttpCodeFromBusinessCode($code);
        }

        $response = [
            'success' => false,
            'code' => $code,
            'message' => $message,
            'data' => $data,
            'timestamp' => time(),
            'request_id' => self::generateRequestId()
        ];

        if (!empty($errors)) {
            $response['errors'] = $errors;
        }

        // 记录错误日志
        self::logError($message, $code, $data, $errors);

        return json($response, $httpCode);
    }

    /**
     * 分页响应
     * @param mixed $list 分页数据
     * @param string $message 响应消息
     * @param array $pagination 分页信息
     * @return Response
     */
    public static function paginate($list, string $message = '获取成功', array $pagination = []): Response
    {
        $data = null;
        $meta = [];

        if (is_object($list) && method_exists($list, 'toArray')) {
            // ThinkPHP分页对象
            $data = $list->toArray();
            $meta = [
                'pagination' => [
                    'total' => $data['total'] ?? 0,
                    'per_page' => $data['per_page'] ?? 15,
                    'current_page' => $data['current_page'] ?? 1,
                    'last_page' => $data['last_page'] ?? 1,
                    'from' => $data['from'] ?? 0,
                    'to' => $data['to'] ?? 0
                ]
            ];
            $data = $data['data'] ?? [];
        } else {
            $data = $list;
            if (!empty($pagination)) {
                $meta['pagination'] = $pagination;
            }
        }

        return self::success($data, $message, self::SUCCESS, self::HTTP_OK, $meta);
    }

    /**
     * 创建成功响应
     * @param mixed $data 创建的数据
     * @param string $message 响应消息
     * @return Response
     */
    public static function created($data = null, string $message = '创建成功'): Response
    {
        return self::success($data, $message, self::CREATED, self::HTTP_CREATED);
    }

    /**
     * 接受响应
     * @param mixed $data 响应数据
     * @param string $message 响应消息
     * @return Response
     */
    public static function accepted($data = null, string $message = '请求已接受'): Response
    {
        return self::success($data, $message, self::ACCEPTED, self::HTTP_ACCEPTED);
    }

    /**
     * 无内容响应
     * @param string $message 响应消息
     * @return Response
     */
    public static function noContent(string $message = '操作成功'): Response
    {
        return self::success(null, $message, self::NO_CONTENT, self::HTTP_NO_CONTENT);
    }

    /**
     * 未授权响应
     * @param string $message 错误消息
     * @return Response
     */
    public static function unauthorized(string $message = '未授权访问'): Response
    {
        return self::error($message, self::UNAUTHORIZED, null, self::HTTP_UNAUTHORIZED);
    }

    /**
     * 禁止访问响应
     * @param string $message 错误消息
     * @return Response
     */
    public static function forbidden(string $message = '禁止访问'): Response
    {
        return self::error($message, self::FORBIDDEN, null, self::HTTP_FORBIDDEN);
    }

    /**
     * 资源不存在响应
     * @param string $message 错误消息
     * @return Response
     */
    public static function notFound(string $message = '资源不存在'): Response
    {
        return self::error($message, self::NOT_FOUND, null, self::HTTP_NOT_FOUND);
    }

    /**
     * 验证错误响应
     * @param string $message 错误消息
     * @param array $errors 验证错误详情
     * @return Response
     */
    public static function validationError(string $message = '数据验证失败', array $errors = []): Response
    {
        return self::error($message, self::VALIDATION_ERROR, null, self::HTTP_UNPROCESSABLE_ENTITY, $errors);
    }

    /**
     * 服务器错误响应
     * @param string $message 错误消息
     * @return Response
     */
    public static function serverError(string $message = '服务器内部错误'): Response
    {
        return self::error($message, self::INTERNAL_ERROR, null, self::HTTP_INTERNAL_SERVER_ERROR);
    }

    /**
     * 生成请求ID
     * @return string
     */
    private static function generateRequestId(): string
    {
        return uniqid('req_', true);
    }

    /**
     * 根据业务状态码获取HTTP状态码
     * @param int $businessCode 业务状态码
     * @return int HTTP状态码
     */
    private static function getHttpCodeFromBusinessCode(int $businessCode): int
    {
        $mapping = [
            self::SUCCESS => self::HTTP_OK,
            self::CREATED => self::HTTP_CREATED,
            self::ACCEPTED => self::HTTP_ACCEPTED,
            self::NO_CONTENT => self::HTTP_NO_CONTENT,
            self::BAD_REQUEST => self::HTTP_BAD_REQUEST,
            self::UNAUTHORIZED => self::HTTP_UNAUTHORIZED,
            self::FORBIDDEN => self::HTTP_FORBIDDEN,
            self::NOT_FOUND => self::HTTP_NOT_FOUND,
            self::VALIDATION_ERROR => self::HTTP_UNPROCESSABLE_ENTITY,
            self::INTERNAL_ERROR => self::HTTP_INTERNAL_SERVER_ERROR
        ];

        return $mapping[$businessCode] ?? self::HTTP_BAD_REQUEST;
    }

    /**
     * 记录错误日志
     * @param string $message 错误消息
     * @param int $code 错误码
     * @param mixed $data 错误数据
     * @param array $errors 详细错误
     */
    private static function logError(string $message, int $code, $data, array $errors): void
    {
        try {
            Log::error('API错误响应', [
                'message' => $message,
                'code' => $code,
                'data' => $data,
                'errors' => $errors,
                'request_id' => self::generateRequestId(),
                'url' => request()->url(),
                'method' => request()->method(),
                'ip' => request()->ip(),
                'user_agent' => request()->header('user-agent', '')
            ]);
        } catch (\Exception $e) {
            // 日志记录失败不影响响应
        }
    }
}
