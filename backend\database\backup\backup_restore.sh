#!/bin/bash

# 数据库备份和恢复脚本
# 民间空放贷后管理系统 v2.0

# 配置参数
DB_HOST="127.0.0.1"
DB_PORT="3306"
DB_NAME="iapp"
DB_USER="root"
DB_PASS="4390024"

# 备份目录
BACKUP_DIR="/var/backups/mysql"
LOG_DIR="/var/log/mysql-backup"

# 创建必要目录
mkdir -p "$BACKUP_DIR"
mkdir -p "$LOG_DIR"

# 日志文件
LOG_FILE="$LOG_DIR/backup_$(date +%Y%m%d).log"

# 日志函数
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

# 错误处理函数
error_exit() {
    log "ERROR: $1"
    exit 1
}

# 检查MySQL连接
check_mysql_connection() {
    log "检查MySQL连接..."
    mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASS" -e "SELECT 1;" > /dev/null 2>&1
    if [ $? -ne 0 ]; then
        error_exit "无法连接到MySQL数据库"
    fi
    log "MySQL连接正常"
}

# 全量备份函数
full_backup() {
    local backup_file="$BACKUP_DIR/full_backup_$(date +%Y%m%d_%H%M%S).sql"
    local compressed_file="${backup_file}.gz"
    
    log "开始全量备份..."
    log "备份文件: $backup_file"
    
    # 执行备份
    mysqldump \
        -h"$DB_HOST" \
        -P"$DB_PORT" \
        -u"$DB_USER" \
        -p"$DB_PASS" \
        --single-transaction \
        --routines \
        --triggers \
        --events \
        --hex-blob \
        --complete-insert \
        --add-drop-database \
        --add-drop-table \
        --create-options \
        --disable-keys \
        --extended-insert \
        --quick \
        --lock-tables=false \
        --databases "$DB_NAME" > "$backup_file"
    
    if [ $? -eq 0 ]; then
        log "备份成功完成"
        
        # 压缩备份文件
        log "压缩备份文件..."
        gzip "$backup_file"
        
        if [ $? -eq 0 ]; then
            log "备份文件压缩完成: $compressed_file"
            
            # 获取文件大小
            local file_size=$(du -h "$compressed_file" | cut -f1)
            log "备份文件大小: $file_size"
            
            # 验证备份文件
            verify_backup "$compressed_file"
        else
            error_exit "备份文件压缩失败"
        fi
    else
        error_exit "数据库备份失败"
    fi
}

# 增量备份函数（基于binlog）
incremental_backup() {
    local backup_file="$BACKUP_DIR/incremental_backup_$(date +%Y%m%d_%H%M%S).sql"
    local start_time="$1"
    local end_time="$2"
    
    if [ -z "$start_time" ]; then
        start_time=$(date -d "1 hour ago" '+%Y-%m-%d %H:%M:%S')
    fi
    
    if [ -z "$end_time" ]; then
        end_time=$(date '+%Y-%m-%d %H:%M:%S')
    fi
    
    log "开始增量备份..."
    log "时间范围: $start_time 到 $end_time"
    log "备份文件: $backup_file"
    
    # 获取binlog文件列表
    local binlog_files=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASS" -e "SHOW BINARY LOGS;" | awk 'NR>1 {print $1}')
    
    if [ -z "$binlog_files" ]; then
        error_exit "未找到binlog文件，请检查binlog是否启用"
    fi
    
    # 导出binlog
    for binlog_file in $binlog_files; do
        log "处理binlog文件: $binlog_file"
        mysqlbinlog \
            --start-datetime="$start_time" \
            --stop-datetime="$end_time" \
            --database="$DB_NAME" \
            "/var/lib/mysql/$binlog_file" >> "$backup_file" 2>/dev/null
    done
    
    if [ -s "$backup_file" ]; then
        log "增量备份完成: $backup_file"
        
        # 压缩备份文件
        gzip "$backup_file"
        log "增量备份文件压缩完成: ${backup_file}.gz"
    else
        log "警告: 增量备份文件为空，可能指定时间范围内没有数据变更"
        rm -f "$backup_file"
    fi
}

# 备份验证函数
verify_backup() {
    local backup_file="$1"
    
    log "验证备份文件: $backup_file"
    
    # 检查文件是否存在
    if [ ! -f "$backup_file" ]; then
        error_exit "备份文件不存在: $backup_file"
    fi
    
    # 检查文件大小
    local file_size=$(stat -f%z "$backup_file" 2>/dev/null || stat -c%s "$backup_file" 2>/dev/null)
    if [ "$file_size" -lt 1024 ]; then
        error_exit "备份文件过小，可能备份失败"
    fi
    
    # 检查压缩文件完整性
    if [[ "$backup_file" == *.gz ]]; then
        gzip -t "$backup_file"
        if [ $? -ne 0 ]; then
            error_exit "备份文件压缩损坏"
        fi
    fi
    
    log "备份文件验证通过"
}

# 数据库恢复函数
restore_database() {
    local backup_file="$1"
    local target_db="$2"
    
    if [ -z "$backup_file" ]; then
        error_exit "请指定备份文件"
    fi
    
    if [ -z "$target_db" ]; then
        target_db="$DB_NAME"
    fi
    
    log "开始数据库恢复..."
    log "备份文件: $backup_file"
    log "目标数据库: $target_db"
    
    # 检查备份文件
    if [ ! -f "$backup_file" ]; then
        error_exit "备份文件不存在: $backup_file"
    fi
    
    # 确认恢复操作
    echo "警告: 此操作将覆盖数据库 $target_db 的所有数据！"
    read -p "确认继续吗？(yes/no): " confirm
    
    if [ "$confirm" != "yes" ]; then
        log "恢复操作已取消"
        exit 0
    fi
    
    # 创建恢复前备份
    log "创建恢复前备份..."
    local pre_restore_backup="$BACKUP_DIR/pre_restore_backup_$(date +%Y%m%d_%H%M%S).sql.gz"
    mysqldump \
        -h"$DB_HOST" \
        -P"$DB_PORT" \
        -u"$DB_USER" \
        -p"$DB_PASS" \
        --single-transaction \
        --databases "$target_db" | gzip > "$pre_restore_backup"
    
    log "恢复前备份完成: $pre_restore_backup"
    
    # 执行恢复
    log "执行数据库恢复..."
    
    if [[ "$backup_file" == *.gz ]]; then
        # 压缩文件恢复
        gunzip -c "$backup_file" | mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASS"
    else
        # 普通文件恢复
        mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASS" < "$backup_file"
    fi
    
    if [ $? -eq 0 ]; then
        log "数据库恢复成功完成"
        
        # 验证恢复结果
        verify_restore "$target_db"
    else
        error_exit "数据库恢复失败"
    fi
}

# 恢复验证函数
verify_restore() {
    local target_db="$1"
    
    log "验证数据库恢复结果..."
    
    # 检查数据库是否存在
    local db_exists=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASS" -e "SHOW DATABASES LIKE '$target_db';" | grep "$target_db")
    
    if [ -z "$db_exists" ]; then
        error_exit "目标数据库不存在"
    fi
    
    # 检查主要表是否存在
    local tables=("customers" "loan_applications" "agents" "investors" "collectors")
    for table in "${tables[@]}"; do
        local table_exists=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASS" -D"$target_db" -e "SHOW TABLES LIKE '$table';" | grep "$table")
        
        if [ -z "$table_exists" ]; then
            error_exit "表 $table 不存在"
        fi
        
        # 检查表数据
        local row_count=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASS" -D"$target_db" -e "SELECT COUNT(*) FROM $table;" | tail -n 1)
        log "表 $table 记录数: $row_count"
    done
    
    log "数据库恢复验证通过"
}

# 清理旧备份函数
cleanup_old_backups() {
    local retention_days="$1"
    
    if [ -z "$retention_days" ]; then
        retention_days=30
    fi
    
    log "清理 $retention_days 天前的备份文件..."
    
    # 查找并删除旧备份文件
    local old_files=$(find "$BACKUP_DIR" -name "*.sql.gz" -mtime +$retention_days)
    
    if [ -n "$old_files" ]; then
        echo "$old_files" | while read file; do
            log "删除旧备份文件: $file"
            rm -f "$file"
        done
    else
        log "没有找到需要清理的旧备份文件"
    fi
}

# 显示帮助信息
show_help() {
    echo "数据库备份和恢复工具"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  full-backup              执行全量备份"
    echo "  incremental-backup       执行增量备份"
    echo "  restore <backup_file>    从备份文件恢复数据库"
    echo "  cleanup [days]           清理指定天数前的备份文件（默认30天）"
    echo "  verify <backup_file>     验证备份文件"
    echo "  help                     显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 full-backup"
    echo "  $0 incremental-backup"
    echo "  $0 restore /var/backups/mysql/full_backup_20250127_120000.sql.gz"
    echo "  $0 cleanup 7"
    echo ""
}

# 主函数
main() {
    local action="$1"
    
    case "$action" in
        "full-backup")
            check_mysql_connection
            full_backup
            ;;
        "incremental-backup")
            check_mysql_connection
            incremental_backup "$2" "$3"
            ;;
        "restore")
            check_mysql_connection
            restore_database "$2" "$3"
            ;;
        "cleanup")
            cleanup_old_backups "$2"
            ;;
        "verify")
            verify_backup "$2"
            ;;
        "help"|"--help"|"-h")
            show_help
            ;;
        *)
            echo "错误: 未知操作 '$action'"
            echo "使用 '$0 help' 查看帮助信息"
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
