-- 民间空放贷后管理系统数据库结构 (简化版)

USE daihou;

-- 用户表
CREATE TABLE users (
    id INT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    real_name VARCHAR(50) NOT NULL,
    phone VARCHAR(20) NOT NULL,
    email VARCHAR(100),
    role ENUM('admin','risk_controller','finance','collection','agent','investor') NOT NULL,
    department VARCHAR(50),
    permissions JSON,
    status ENUM('active','inactive','locked') DEFAULT 'active',
    last_login_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_username (username),
    INDEX idx_role (role),
    INDEX idx_status (status)
);

-- 客户表
CREATE TABLE customers (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(50) NOT NULL,
    phone VARCHAR(20) NOT NULL UNIQUE,
    id_card VARCHAR(18) NOT NULL UNIQUE,
    age INT,
    gender ENUM('male','female'),
    address TEXT,
    industry VARCHAR(50),
    monthly_income DECIMAL(10,2),
    emergency_contact VARCHAR(50),
    emergency_phone VARCHAR(20),
    risk_score INT DEFAULT 0,
    total_loan_amount DECIMAL(12,2) DEFAULT 0,
    total_repaid_amount DECIMAL(12,2) DEFAULT 0,
    active_loans_count INT DEFAULT 0,
    last_loan_date DATE,
    last_repayment_date DATE,
    customer_profit DECIMAL(10,2) DEFAULT 0,
    risk_assessment_score INT,
    status ENUM('active','blacklist','suspended') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_phone (phone),
    INDEX idx_id_card (id_card),
    INDEX idx_status (status)
);

-- 放款登记记录表
CREATE TABLE loan_disbursement_records (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    business_flow_no VARCHAR(32) NOT NULL UNIQUE,
    loan_date DATE NOT NULL,
    customer_name VARCHAR(50) NOT NULL,
    customer_id BIGINT UNSIGNED NOT NULL,
    risk_controller_name VARCHAR(50) NOT NULL,
    risk_controller_id INT NOT NULL,
    loan_amount DECIMAL(10,2) NOT NULL,
    repayment_type ENUM('daily','weekly','monthly') NOT NULL,
    repayment_cycle INT NOT NULL,
    repayment_frequency VARCHAR(20),
    agent_id INT,
    agent_name VARCHAR(50),
    agent_commission_rate DECIMAL(5,2),
    agent_commission_amount DECIMAL(10,2),
    commission_type ENUM('front','back','both') DEFAULT 'front',
    front_commission_amount DECIMAL(10,2) DEFAULT 0,
    back_commission_amount DECIMAL(10,2) DEFAULT 0,
    back_commission_paid TINYINT DEFAULT 0,
    platform_fee DECIMAL(10,2) NOT NULL DEFAULT 0,
    overdue_fee DECIMAL(10,2) DEFAULT 0,
    total_repaid_amount DECIMAL(10,2) DEFAULT 0,
    remaining_amount DECIMAL(10,2) NOT NULL,
    customer_profit DECIMAL(10,2) DEFAULT 0,
    status ENUM('active','completed','overdue','settled') DEFAULT 'active',
    created_by INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_customer_id (customer_id),
    INDEX idx_agent_id (agent_id),
    INDEX idx_loan_date (loan_date),
    INDEX idx_status (status)
);

-- 还款计划详细表
CREATE TABLE repayment_schedule_details (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    disbursement_record_id BIGINT UNSIGNED NOT NULL,
    period_number INT NOT NULL,
    due_date DATE NOT NULL,
    due_amount DECIMAL(10,2) NOT NULL,
    paid_amount DECIMAL(10,2) DEFAULT 0,
    remaining_amount DECIMAL(10,2) NOT NULL,
    status ENUM('pending','paid','overdue','partial') DEFAULT 'pending',
    overdue_days INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_disbursement_record_id (disbursement_record_id),
    INDEX idx_due_date (due_date),
    INDEX idx_status (status)
);

-- 还款登记记录表
CREATE TABLE repayment_records (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    business_flow_no VARCHAR(32) NOT NULL UNIQUE,
    disbursement_record_id BIGINT UNSIGNED NOT NULL,
    customer_id BIGINT UNSIGNED NOT NULL,
    customer_name VARCHAR(50) NOT NULL,
    repayment_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    repayment_method ENUM('wechat','alipay','bank_card','cash','other') NOT NULL,
    repayment_amount DECIMAL(10,2) NOT NULL,
    repayment_type ENUM('normal','partial','negotiated','overdue_fee','early_settlement') NOT NULL,
    payment_screenshot VARCHAR(500),
    remark TEXT,
    overdue_days INT DEFAULT 0,
    overdue_fee DECIMAL(10,2) DEFAULT 0,
    status ENUM('pending','confirmed','cancelled') DEFAULT 'confirmed',
    created_by INT NOT NULL,
    confirmed_by INT,
    confirmed_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_disbursement_record_id (disbursement_record_id),
    INDEX idx_customer_id (customer_id),
    INDEX idx_repayment_date (repayment_date),
    INDEX idx_status (status)
);

-- 插入初始数据
INSERT INTO users (username, password, real_name, phone, role, status) VALUES 
('admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Administrator', '***********', 'admin', 'active'),
('finance001', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Finance Manager', '13900000001', 'finance', 'active'),
('risk001', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Risk Controller', '13900000002', 'risk_controller', 'active');
