<?php
declare (strict_types = 1);

namespace app\api\controller;

use app\common\model\IndustryBlacklist;
use think\Request;
use think\Response;

class Blacklist
{
    /**
     * 添加黑名单
     */
    public function create(Request $request): Response
    {
        $data = $request->only(['type', 'value', 'reason', 'risk_level', 'effective_date']);

        foreach (['type','value','effective_date'] as $f) {
            if (empty($data[$f])) {
                return json(['code'=>400,'message'=>"字段 {$f} 不能为空",'data'=>null],400);
            }
        }

        $record = IndustryBlacklist::create([
            'type' => $data['type'],
            'value' => $data['value'],
            'reason' => $data['reason'] ?? '',
            'risk_level' => $data['risk_level'] ?? 'medium',
            'effective_date' => $data['effective_date'],
            'is_active' => 1,
            'created_by' => $request->user['id'] ?? 0,
        ]);

        return json(['code'=>200,'message'=>'创建成功','data'=>$record]);
    }

    /**
     * 黑名单检查
     */
    public function check(Request $request): Response
    {
        $type = (string) $request->param('type','');
        $value = (string) $request->param('value','');

        if (!$type || !$value) {
            return json(['code'=>400,'message'=>'参数错误','data'=>null],400);
        }

        $record = IndustryBlacklist::where('type',$type)
            ->where('value',$value)
            ->where('is_active',1)
            ->find();

        return json(['code'=>200,'data'=>[
            'is_blacklist' => (bool)$record,
            'risk_level' => $record->risk_level ?? null,
            'reason' => $record->reason ?? null,
            'effective_date' => $record->effective_date ?? null,
        ]]);
    }

    /**
     * 批量检查
     */
    public function batchCheck(Request $request): Response
    {
        $checks = $request->param('checks', []);
        if (!is_array($checks) || empty($checks)) {
            return json(['code'=>400,'message'=>'参数错误','data'=>null],400);
        }

        $results = [];
        foreach ($checks as $c) {
            $type = $c['type'] ?? '';
            $value = $c['value'] ?? '';
            if (!$type || !$value) continue;
            $record = IndustryBlacklist::where('type',$type)
                ->where('value',$value)
                ->where('is_active',1)
                ->find();
            $results[] = [
                'type' => $type,
                'value' => $value,
                'is_blacklist' => (bool)$record,
                'risk_level' => $record->risk_level ?? null,
            ];
        }

        return json(['code'=>200,'data'=>$results]);
    }
}


