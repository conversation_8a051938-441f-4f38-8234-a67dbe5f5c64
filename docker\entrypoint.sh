#!/bin/sh

# 容器启动脚本
# 民间空放贷后管理系统 v2.0

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 等待数据库连接
wait_for_database() {
    log "等待数据库连接..."
    
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if php -r "
            try {
                \$pdo = new PDO('mysql:host=${DB_HOST};port=${DB_PORT}', '${DB_USERNAME}', '${DB_PASSWORD}');
                echo 'Database connection successful';
                exit(0);
            } catch (Exception \$e) {
                echo 'Database connection failed: ' . \$e->getMessage();
                exit(1);
            }
        " > /dev/null 2>&1; then
            success "数据库连接成功"
            return 0
        fi
        
        log "数据库连接失败，重试中... ($attempt/$max_attempts)"
        sleep 2
        attempt=$((attempt + 1))
    done
    
    error "数据库连接超时"
    exit 1
}

# 等待Redis连接
wait_for_redis() {
    log "等待Redis连接..."
    
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if php -r "
            try {
                \$redis = new Redis();
                \$redis->connect('${REDIS_HOST}', ${REDIS_PORT});
                \$redis->ping();
                echo 'Redis connection successful';
                exit(0);
            } catch (Exception \$e) {
                echo 'Redis connection failed: ' . \$e->getMessage();
                exit(1);
            }
        " > /dev/null 2>&1; then
            success "Redis连接成功"
            return 0
        fi
        
        log "Redis连接失败，重试中... ($attempt/$max_attempts)"
        sleep 2
        attempt=$((attempt + 1))
    done
    
    warning "Redis连接超时，继续启动"
}

# 初始化应用
initialize_app() {
    log "初始化应用..."
    
    # 设置权限
    chown -R www:www /var/www/html
    chmod -R 755 /var/www/html
    chmod -R 777 /var/www/html/runtime
    
    # 创建必要目录
    mkdir -p /var/www/html/runtime/log
    mkdir -p /var/www/html/runtime/cache
    mkdir -p /var/www/html/runtime/temp
    mkdir -p /var/www/html/uploads
    
    # 设置目录权限
    chown -R www:www /var/www/html/runtime
    chown -R www:www /var/www/html/uploads
    chmod -R 777 /var/www/html/runtime
    chmod -R 755 /var/www/html/uploads
    
    success "应用初始化完成"
}

# 运行数据库迁移
run_migrations() {
    log "运行数据库迁移..."
    
    if [ -f "/var/www/html/think" ]; then
        # 检查数据库是否存在
        php -r "
            try {
                \$pdo = new PDO('mysql:host=${DB_HOST};port=${DB_PORT}', '${DB_USERNAME}', '${DB_PASSWORD}');
                \$pdo->exec('CREATE DATABASE IF NOT EXISTS \`${DB_DATABASE}\` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci');
                echo 'Database created or exists';
            } catch (Exception \$e) {
                echo 'Database creation failed: ' . \$e->getMessage();
                exit(1);
            }
        "
        
        # 运行迁移
        if php /var/www/html/think migrate:status > /dev/null 2>&1; then
            php /var/www/html/think migrate:run
            success "数据库迁移完成"
        else
            warning "迁移命令不可用，跳过迁移"
        fi
    else
        warning "ThinkPHP命令行工具不存在，跳过迁移"
    fi
}

# 清理缓存
clear_cache() {
    log "清理应用缓存..."
    
    if [ -f "/var/www/html/think" ]; then
        php /var/www/html/think clear
        php /var/www/html/think optimize:config
        success "缓存清理完成"
    else
        # 手动清理缓存目录
        rm -rf /var/www/html/runtime/cache/*
        rm -rf /var/www/html/runtime/temp/*
        success "手动缓存清理完成"
    fi
}

# 启动健康检查
start_health_check() {
    log "启动健康检查..."
    
    # 创建健康检查脚本
    cat > /var/www/html/public/health.php << 'EOF'
<?php
header('Content-Type: application/json');

$health = [
    'status' => 'healthy',
    'timestamp' => date('Y-m-d H:i:s'),
    'version' => '2.0.0',
    'services' => []
];

// 检查数据库
try {
    $pdo = new PDO(
        "mysql:host={$_ENV['DB_HOST']};port={$_ENV['DB_PORT']};dbname={$_ENV['DB_DATABASE']}", 
        $_ENV['DB_USERNAME'], 
        $_ENV['DB_PASSWORD']
    );
    $pdo->query('SELECT 1');
    $health['services']['database'] = 'healthy';
} catch (Exception $e) {
    $health['services']['database'] = 'unhealthy';
    $health['status'] = 'unhealthy';
}

// 检查Redis
try {
    $redis = new Redis();
    $redis->connect($_ENV['REDIS_HOST'], $_ENV['REDIS_PORT']);
    $redis->ping();
    $health['services']['redis'] = 'healthy';
} catch (Exception $e) {
    $health['services']['redis'] = 'unhealthy';
}

// 检查磁盘空间
$diskFree = disk_free_space('/var/www/html');
$diskTotal = disk_total_space('/var/www/html');
$diskUsage = (($diskTotal - $diskFree) / $diskTotal) * 100;

if ($diskUsage > 90) {
    $health['services']['disk'] = 'critical';
    $health['status'] = 'unhealthy';
} elseif ($diskUsage > 80) {
    $health['services']['disk'] = 'warning';
} else {
    $health['services']['disk'] = 'healthy';
}

$health['disk_usage'] = round($diskUsage, 2) . '%';

http_response_code($health['status'] === 'healthy' ? 200 : 503);
echo json_encode($health, JSON_PRETTY_PRINT);
EOF

    success "健康检查启动完成"
}

# 信号处理
handle_signal() {
    log "接收到停止信号，正在关闭服务..."
    
    # 停止supervisor管理的进程
    if [ -f "/var/run/supervisord.pid" ]; then
        supervisorctl stop all
        kill -TERM $(cat /var/run/supervisord.pid)
    fi
    
    success "服务已停止"
    exit 0
}

# 设置信号处理
trap 'handle_signal' TERM INT

# 主函数
main() {
    log "启动民间空放贷后管理系统 v2.0"
    
    # 等待依赖服务
    wait_for_database
    wait_for_redis
    
    # 初始化应用
    initialize_app
    
    # 运行数据库迁移
    run_migrations
    
    # 清理缓存
    clear_cache
    
    # 启动健康检查
    start_health_check
    
    # 启动supervisor
    log "启动Supervisor..."
    exec /usr/bin/supervisord -c /etc/supervisor/conf.d/supervisord.conf
}

# 执行主函数
main "$@"
