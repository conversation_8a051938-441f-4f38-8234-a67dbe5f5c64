<?php
declare (strict_types = 1);

namespace app\common\model;

use think\Model;

class Notification extends Model
{
    protected $name = 'notifications';

    protected $schema = [
        'id' => 'int',
        'title' => 'string',
        'content' => 'string',
        'type' => 'string',
        'level' => 'string',
        'send_type' => 'string',
        'target_type' => 'string',
        'target_users' => 'json',
        'send_time' => 'datetime',
        'sent_count' => 'int',
        'success_count' => 'int',
        'status' => 'string',
        'template_id' => 'string',
        'template_params' => 'json',
        'created_by' => 'int',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    protected $json = ['target_users','template_params'];
}


