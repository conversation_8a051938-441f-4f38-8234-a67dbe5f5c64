<template>
  <div class="blacklist-management">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>黑名单管理</span>
          <el-button type="primary" @click="showCreateDialog">新增黑名单</el-button>
        </div>
      </template>
      
      <!-- 搜索区域 -->
      <div class="search-area">
        <el-form :inline="true" :model="searchForm">
          <el-form-item label="类型">
            <el-select v-model="searchForm.type" placeholder="请选择类型" clearable>
              <el-option label="手机号" value="phone"/>
              <el-option label="身份证" value="id_card"/>
              <el-option label="姓名" value="name"/>
              <el-option label="公司" value="company"/>
              <el-option label="其他" value="other"/>
            </el-select>
          </el-form-item>
          
          <el-form-item label="关键词">
            <el-input
              v-model="searchForm.keyword"
              placeholder="搜索值"
              clearable
            />
          </el-form-item>
          
          <el-form-item>
            <el-button type="primary" @click="handleSearch">搜索</el-button>
            <el-button @click="resetSearch">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
      
      <!-- 表格 -->
      <el-table
        :data="tableData"
        :loading="tableLoading"
        style="width: 100%"
      >
        <el-table-column prop="type" label="类型" width="100">
          <template #default="scope">
            <el-tag :type="getTypeTagType(scope.row.type)">
              {{ getTypeText(scope.row.type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="value" label="值" min-width="150"/>
        <el-table-column prop="reason" label="原因" min-width="200"/>
        <el-table-column prop="risk_level" label="风险等级" width="100">
          <template #default="scope">
            <el-tag :type="getRiskLevelTagType(scope.row.risk_level)">
              {{ getRiskLevelText(scope.row.risk_level) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="is_active" label="状态" width="80">
          <template #default="scope">
            <el-tag :type="scope.row.is_active ? 'success' : 'danger'">
              {{ scope.row.is_active ? '有效' : '无效' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="创建时间" width="160"/>
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="scope">
            <el-button size="small" @click="editRecord(scope.row)">编辑</el-button>
            <el-button 
              size="small" 
              type="danger" 
              @click="deleteRecord(scope.row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.limit"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 创建/编辑对话框 -->
    <el-dialog
      v-model="createDialogVisible"
      :title="editMode ? '编辑黑名单' : '新增黑名单'"
      width="500px"
    >
      <el-form
        ref="createFormRef"
        :model="createForm"
        :rules="createRules"
        label-width="80px"
      >
        <el-form-item label="类型" prop="type">
          <el-select v-model="createForm.type" placeholder="请选择类型" style="width: 100%">
            <el-option label="手机号" value="phone"/>
            <el-option label="身份证" value="id_card"/>
            <el-option label="姓名" value="name"/>
            <el-option label="公司" value="company"/>
            <el-option label="其他" value="other"/>
          </el-select>
        </el-form-item>
        
        <el-form-item label="值" prop="value">
          <el-input v-model="createForm.value" placeholder="请输入值"/>
        </el-form-item>
        
        <el-form-item label="原因" prop="reason">
          <el-input
            v-model="createForm.reason"
            type="textarea"
            :rows="3"
            placeholder="请输入原因"
          />
        </el-form-item>
        
        <el-form-item label="风险等级" prop="risk_level">
          <el-select v-model="createForm.risk_level" placeholder="请选择风险等级" style="width: 100%">
            <el-option label="低" value="low"/>
            <el-option label="中" value="medium"/>
            <el-option label="高" value="high"/>
            <el-option label="严重" value="critical"/>
          </el-select>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="createDialogVisible = false">取消</el-button>
        <el-button type="primary" :loading="createLoading" @click="handleCreate">
          {{ editMode ? '更新' : '创建' }}
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

export default {
  name: 'BlacklistManagement',
  setup() {
    const tableLoading = ref(false)
    const createLoading = ref(false)
    const createDialogVisible = ref(false)
    const createFormRef = ref()
    const editMode = ref(false)
    
    const tableData = ref([])
    
    const pagination = reactive({
      page: 1,
      limit: 20,
      total: 0
    })
    
    const searchForm = reactive({
      type: '',
      keyword: ''
    })
    
    const createForm = reactive({
      type: '',
      value: '',
      reason: '',
      risk_level: 'medium'
    })
    
    const createRules = {
      type: [{ required: true, message: '请选择类型', trigger: 'change' }],
      value: [{ required: true, message: '请输入值', trigger: 'blur' }],
      reason: [{ required: true, message: '请输入原因', trigger: 'blur' }],
      risk_level: [{ required: true, message: '请选择风险等级', trigger: 'change' }]
    }
    
    // 获取表格数据
    const getTableData = async () => {
      try {
        tableLoading.value = true
        // TODO: 调用实际API
        // const data = await blacklistAPI.getList({...searchForm, ...pagination})
        // tableData.value = data.data
        // pagination.total = data.total
        
        // 模拟数据
        tableData.value = [
          {
            id: 1,
            type: 'phone',
            value: '13812345678',
            reason: '多次逾期还款',
            risk_level: 'high',
            is_active: 1,
            created_at: '2024-01-15 10:30:00'
          }
        ]
        pagination.total = 1
      } catch (error) {
        ElMessage.error('获取数据失败')
      } finally {
        tableLoading.value = false
      }
    }
    
    // 显示创建对话框
    const showCreateDialog = () => {
      editMode.value = false
      resetCreateForm()
      createDialogVisible.value = true
    }
    
    // 编辑记录
    const editRecord = (row) => {
      editMode.value = true
      Object.assign(createForm, row)
      createDialogVisible.value = true
    }
    
    // 删除记录
    const deleteRecord = async (row) => {
      try {
        await ElMessageBox.confirm('确定要删除这条记录吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        
        // TODO: 调用删除API
        // await blacklistAPI.delete(row.id)
        ElMessage.success('删除成功')
        getTableData()
      } catch (error) {
        if (error !== 'cancel') {
          ElMessage.error('删除失败')
        }
      }
    }
    
    // 创建/更新记录
    const handleCreate = async () => {
      try {
        const valid = await createFormRef.value.validate()
        if (!valid) return
        
        createLoading.value = true
        
        // TODO: 调用API
        if (editMode.value) {
          // await blacklistAPI.update(createForm.id, createForm)
          ElMessage.success('更新成功')
        } else {
          // await blacklistAPI.create(createForm)
          ElMessage.success('创建成功')
        }
        
        createDialogVisible.value = false
        getTableData()
      } catch (error) {
        ElMessage.error(editMode.value ? '更新失败' : '创建失败')
      } finally {
        createLoading.value = false
      }
    }
    
    // 重置表单
    const resetCreateForm = () => {
      Object.assign(createForm, {
        type: '',
        value: '',
        reason: '',
        risk_level: 'medium'
      })
      createFormRef.value?.clearValidate()
    }
    
    // 搜索
    const handleSearch = () => {
      pagination.page = 1
      getTableData()
    }
    
    // 重置搜索
    const resetSearch = () => {
      Object.assign(searchForm, {
        type: '',
        keyword: ''
      })
      pagination.page = 1
      getTableData()
    }
    
    // 分页处理
    const handleSizeChange = (size) => {
      pagination.limit = size
      pagination.page = 1
      getTableData()
    }
    
    const handleCurrentChange = (page) => {
      pagination.page = page
      getTableData()
    }
    
    // 工具函数
    const getTypeText = (type) => {
      const types = {
        phone: '手机号',
        id_card: '身份证',
        name: '姓名',
        company: '公司',
        other: '其他'
      }
      return types[type] || type
    }
    
    const getTypeTagType = (type) => {
      const types = {
        phone: 'primary',
        id_card: 'warning',
        name: 'success',
        company: 'info',
        other: ''
      }
      return types[type] || ''
    }
    
    const getRiskLevelText = (level) => {
      const levels = {
        low: '低',
        medium: '中',
        high: '高',
        critical: '严重'
      }
      return levels[level] || level
    }
    
    const getRiskLevelTagType = (level) => {
      const types = {
        low: 'success',
        medium: 'warning',
        high: 'danger',
        critical: 'danger'
      }
      return types[level] || ''
    }
    
    onMounted(() => {
      getTableData()
    })
    
    return {
      // 响应式数据
      tableLoading,
      createLoading,
      createDialogVisible,
      createFormRef,
      editMode,
      tableData,
      pagination,
      searchForm,
      createForm,
      createRules,
      
      // 方法
      getTableData,
      showCreateDialog,
      editRecord,
      deleteRecord,
      handleCreate,
      resetCreateForm,
      handleSearch,
      resetSearch,
      handleSizeChange,
      handleCurrentChange,
      getTypeText,
      getTypeTagType,
      getRiskLevelText,
      getRiskLevelTagType
    }
  }
}
</script>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-area {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.pagination-wrapper {
  margin-top: 20px;
  text-align: right;
}
</style>
