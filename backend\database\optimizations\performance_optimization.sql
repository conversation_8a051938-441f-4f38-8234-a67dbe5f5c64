-- 数据库性能优化脚本
-- 民间空放贷后管理系统 v2.0
-- 执行前请备份数据库

USE iapp;

-- ==================== 索引优化 ====================

-- 1. 客户表索引优化
-- 添加复合索引提升查询性能
ALTER TABLE customers 
ADD INDEX idx_customers_status_risk (status, risk_level),
ADD INDEX idx_customers_agent_status (agent_id, status),
ADD INDEX idx_customers_create_time (create_time),
ADD INDEX idx_customers_name_phone (name, phone),
ADD INDEX idx_customers_risk_score (risk_score);

-- 2. 借款申请表索引优化
ALTER TABLE loan_applications 
ADD INDEX idx_loan_apps_customer_status (customer_id, status),
ADD INDEX idx_loan_apps_status_time (status, submit_time),
ADD INDEX idx_loan_apps_reviewer (reviewer_id, review_time),
ADD INDEX idx_loan_apps_amount (amount),
ADD INDEX idx_loan_apps_apply_no (apply_no);

-- 3. 代理商表索引优化
ALTER TABLE agents 
ADD INDEX idx_agents_level_status (level, status),
ADD INDEX idx_agents_commission (commission_rate),
ADD INDEX idx_agents_performance (total_customers, total_amount);

-- 4. 投资人表索引优化
ALTER TABLE investors 
ADD INDEX idx_investors_type_status (type, status),
ADD INDEX idx_investors_contact (contact_phone);

-- 5. 催收员表索引优化
ALTER TABLE collectors 
ADD INDEX idx_collectors_level_status (level, status),
ADD INDEX idx_collectors_performance (success_rate, completed_tasks);

-- ==================== 表结构优化 ====================

-- 1. 优化客户表字段类型和长度
ALTER TABLE customers 
MODIFY COLUMN id BIGINT UNSIGNED AUTO_INCREMENT,
MODIFY COLUMN agent_id BIGINT UNSIGNED NULL,
MODIFY COLUMN phone VARCHAR(20) NOT NULL COMMENT '手机号',
MODIFY COLUMN id_card VARCHAR(20) NOT NULL COMMENT '身份证号(加密存储)',
MODIFY COLUMN name VARCHAR(50) NOT NULL COMMENT '客户姓名',
MODIFY COLUMN industry VARCHAR(100) NULL COMMENT '行业',
MODIFY COLUMN monthly_income DECIMAL(12,2) NULL COMMENT '月收入',
MODIFY COLUMN residence_address TEXT NULL COMMENT '居住地址',
MODIFY COLUMN work_address TEXT NULL COMMENT '工作地址',
MODIFY COLUMN risk_score SMALLINT UNSIGNED DEFAULT 0 COMMENT '风险评分(0-1000)',
ADD COLUMN gender ENUM('male','female','unknown') NULL COMMENT '性别' AFTER id_card,
ADD COLUMN birth_date DATE NULL COMMENT '出生日期' AFTER gender,
ADD COLUMN education ENUM('primary','middle','high','college','university','graduate') NULL COMMENT '学历' AFTER industry,
ADD COLUMN marital_status ENUM('single','married','divorced','widowed') NULL COMMENT '婚姻状况' AFTER education,
ADD COLUMN work_years TINYINT UNSIGNED NULL COMMENT '工作年限' AFTER industry,
ADD COLUMN company VARCHAR(200) NULL COMMENT '公司名称' AFTER work_years,
ADD COLUMN position VARCHAR(100) NULL COMMENT '职位' AFTER company,
ADD COLUMN source ENUM('direct','agent','referral','online') DEFAULT 'direct' COMMENT '客户来源' AFTER agent_id,
ADD COLUMN last_login_at TIMESTAMP NULL COMMENT '最后登录时间' AFTER source,
ADD COLUMN deleted_at TIMESTAMP NULL COMMENT '软删除时间' AFTER update_time;

-- 2. 优化借款申请表
ALTER TABLE loan_applications 
MODIFY COLUMN id BIGINT UNSIGNED AUTO_INCREMENT,
MODIFY COLUMN customer_id BIGINT UNSIGNED NOT NULL,
MODIFY COLUMN reviewer_id BIGINT UNSIGNED NULL,
MODIFY COLUMN amount DECIMAL(12,2) NOT NULL COMMENT '申请金额',
MODIFY COLUMN cycle SMALLINT UNSIGNED NOT NULL COMMENT '借款周期(天)',
ADD COLUMN interest_rate DECIMAL(6,4) NULL COMMENT '利率' AFTER amount,
ADD COLUMN repayment_method ENUM('equal_installment','equal_principal','interest_first','bullet') DEFAULT 'equal_installment' COMMENT '还款方式' AFTER cycle,
ADD COLUMN risk_assessment_id BIGINT UNSIGNED NULL COMMENT '风险评估ID' AFTER reviewer_id,
ADD COLUMN ai_score SMALLINT UNSIGNED NULL COMMENT 'AI评分' AFTER risk_assessment_id,
ADD COLUMN approval_amount DECIMAL(12,2) NULL COMMENT '批准金额' AFTER ai_score,
ADD COLUMN rejection_reason TEXT NULL COMMENT '拒绝原因' AFTER review_notes,
ADD COLUMN deleted_at TIMESTAMP NULL COMMENT '软删除时间' AFTER create_time;

-- 3. 优化代理商表
ALTER TABLE agents 
MODIFY COLUMN id BIGINT UNSIGNED AUTO_INCREMENT,
MODIFY COLUMN total_customers INT UNSIGNED DEFAULT 0,
MODIFY COLUMN total_loans INT UNSIGNED DEFAULT 0,
MODIFY COLUMN total_amount DECIMAL(15,2) UNSIGNED DEFAULT 0.00,
MODIFY COLUMN total_commission DECIMAL(15,2) UNSIGNED DEFAULT 0.00,
MODIFY COLUMN available_commission DECIMAL(15,2) UNSIGNED DEFAULT 0.00,
ADD COLUMN email VARCHAR(100) NULL COMMENT '邮箱' AFTER phone,
ADD COLUMN id_card VARCHAR(20) NULL COMMENT '身份证号' AFTER email,
ADD COLUMN bank_account VARCHAR(50) NULL COMMENT '银行账号' AFTER id_card,
ADD COLUMN bank_name VARCHAR(100) NULL COMMENT '开户银行' AFTER bank_account,
ADD COLUMN region VARCHAR(100) NULL COMMENT '负责区域' AFTER bank_name,
ADD COLUMN last_login_at TIMESTAMP NULL COMMENT '最后登录时间' AFTER region,
ADD COLUMN deleted_at TIMESTAMP NULL COMMENT '软删除时间' AFTER update_time;

-- ==================== 新增必要表 ====================

-- 1. 系统配置表
CREATE TABLE IF NOT EXISTS system_configs (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    `key` VARCHAR(100) NOT NULL COMMENT '配置键',
    `value` TEXT NOT NULL COMMENT '配置值',
    `type` VARCHAR(50) DEFAULT 'system' COMMENT '配置类型',
    data_type ENUM('string','int','float','bool','json','array') DEFAULT 'string' COMMENT '数据类型',
    description VARCHAR(500) NULL COMMENT '配置描述',
    sort INT UNSIGNED DEFAULT 0 COMMENT '排序',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    UNIQUE KEY uk_key_type (`key`, `type`),
    INDEX idx_type (`type`),
    INDEX idx_sort (sort)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统配置表';

-- 2. 风险评估记录表
CREATE TABLE IF NOT EXISTS risk_assessments (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    customer_id BIGINT UNSIGNED NOT NULL COMMENT '客户ID',
    application_id BIGINT UNSIGNED NULL COMMENT '申请ID',
    assessment_type ENUM('manual','ai','rule','model') NOT NULL COMMENT '评估类型',
    provider VARCHAR(50) NULL COMMENT 'AI提供商',
    model_version VARCHAR(50) NULL COMMENT '模型版本',
    input_data JSON NULL COMMENT '输入数据',
    output_data JSON NULL COMMENT '输出数据',
    overall_score SMALLINT UNSIGNED NULL COMMENT '综合评分',
    risk_level ENUM('low','medium','high','very_high') NULL COMMENT '风险等级',
    decision ENUM('pass','reject','manual','pending') NULL COMMENT '决策结果',
    decision_reason TEXT NULL COMMENT '决策原因',
    risk_factors JSON NULL COMMENT '风险因素',
    positive_factors JSON NULL COMMENT '积极因素',
    recommended_amount DECIMAL(12,2) NULL COMMENT '建议额度',
    assessment_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '评估时间',
    assessor_id BIGINT UNSIGNED NULL COMMENT '评估人ID',
    assessor_type ENUM('system','admin','risk_controller') DEFAULT 'system' COMMENT '评估人类型',
    api_response_time INT UNSIGNED NULL COMMENT 'API响应时间(毫秒)',
    api_cost DECIMAL(10,4) NULL COMMENT 'API成本',
    status ENUM('success','failed','timeout','error') DEFAULT 'success' COMMENT '评估状态',
    error_message TEXT NULL COMMENT '错误信息',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_customer_id (customer_id),
    INDEX idx_application_id (application_id),
    INDEX idx_assessment_type (assessment_type),
    INDEX idx_risk_level (risk_level),
    INDEX idx_decision (decision),
    INDEX idx_assessment_time (assessment_time),
    INDEX idx_overall_score (overall_score),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='风险评估记录表';

-- 3. 操作日志表
CREATE TABLE IF NOT EXISTS operation_logs (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT UNSIGNED NULL COMMENT '操作用户ID',
    user_type ENUM('admin','customer','agent','investor','collector','system') NOT NULL COMMENT '用户类型',
    module VARCHAR(50) NOT NULL COMMENT '模块名称',
    action VARCHAR(100) NOT NULL COMMENT '操作动作',
    target_type VARCHAR(50) NULL COMMENT '目标类型',
    target_id BIGINT UNSIGNED NULL COMMENT '目标ID',
    description TEXT NULL COMMENT '操作描述',
    request_data JSON NULL COMMENT '请求数据',
    response_data JSON NULL COMMENT '响应数据',
    ip_address VARCHAR(45) NULL COMMENT 'IP地址',
    user_agent TEXT NULL COMMENT '用户代理',
    execution_time INT UNSIGNED NULL COMMENT '执行时间(毫秒)',
    status ENUM('success','failed','error') DEFAULT 'success' COMMENT '操作状态',
    error_message TEXT NULL COMMENT '错误信息',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_user_id (user_id),
    INDEX idx_user_type (user_type),
    INDEX idx_module (module),
    INDEX idx_action (action),
    INDEX idx_target (target_type, target_id),
    INDEX idx_created_at (created_at),
    INDEX idx_status (status),
    INDEX idx_ip_address (ip_address)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='操作日志表';

-- 4. 性能监控表
CREATE TABLE IF NOT EXISTS performance_metrics (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    metric_type ENUM('api','database','system','business') NOT NULL COMMENT '指标类型',
    metric_name VARCHAR(100) NOT NULL COMMENT '指标名称',
    metric_value DECIMAL(15,4) NOT NULL COMMENT '指标值',
    metric_unit VARCHAR(20) NULL COMMENT '指标单位',
    tags JSON NULL COMMENT '标签',
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '时间戳',
    
    INDEX idx_metric_type (metric_type),
    INDEX idx_metric_name (metric_name),
    INDEX idx_timestamp (timestamp),
    INDEX idx_type_name_time (metric_type, metric_name, timestamp)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='性能监控表';

-- ==================== 数据库配置优化 ====================

-- 设置字符集和排序规则
ALTER DATABASE iapp CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- ==================== 分区表设计 ====================

-- 对操作日志表按月分区（如果数据量大）
-- ALTER TABLE operation_logs 
-- PARTITION BY RANGE (YEAR(created_at) * 100 + MONTH(created_at)) (
--     PARTITION p202501 VALUES LESS THAN (202502),
--     PARTITION p202502 VALUES LESS THAN (202503),
--     PARTITION p202503 VALUES LESS THAN (202504),
--     PARTITION p202504 VALUES LESS THAN (202505),
--     PARTITION p202505 VALUES LESS THAN (202506),
--     PARTITION p202506 VALUES LESS THAN (202507),
--     PARTITION p202507 VALUES LESS THAN (202508),
--     PARTITION p202508 VALUES LESS THAN (202509),
--     PARTITION p202509 VALUES LESS THAN (202510),
--     PARTITION p202510 VALUES LESS THAN (202511),
--     PARTITION p202511 VALUES LESS THAN (202512),
--     PARTITION p202512 VALUES LESS THAN (202513),
--     PARTITION p_future VALUES LESS THAN MAXVALUE
-- );

-- ==================== 触发器创建 ====================

-- 客户表更新时间触发器
DELIMITER $$
CREATE TRIGGER tr_customers_update_time 
BEFORE UPDATE ON customers
FOR EACH ROW
BEGIN
    SET NEW.update_time = CURRENT_TIMESTAMP;
END$$
DELIMITER ;

-- 代理商统计数据更新触发器
DELIMITER $$
CREATE TRIGGER tr_agents_stats_update 
AFTER INSERT ON loan_applications
FOR EACH ROW
BEGIN
    IF NEW.status = 'approved' THEN
        UPDATE agents 
        SET total_loans = total_loans + 1,
            total_amount = total_amount + NEW.amount
        WHERE id = (SELECT agent_id FROM customers WHERE id = NEW.customer_id);
    END IF;
END$$
DELIMITER ;

-- ==================== 视图创建 ====================

-- 客户风险统计视图
CREATE OR REPLACE VIEW v_customer_risk_stats AS
SELECT 
    risk_level,
    COUNT(*) as customer_count,
    AVG(risk_score) as avg_risk_score,
    AVG(monthly_income) as avg_monthly_income
FROM customers 
WHERE deleted_at IS NULL
GROUP BY risk_level;

-- 代理商业绩统计视图
CREATE OR REPLACE VIEW v_agent_performance AS
SELECT 
    a.id,
    a.name,
    a.level,
    a.total_customers,
    a.total_loans,
    a.total_amount,
    a.total_commission,
    a.commission_rate,
    CASE 
        WHEN a.total_customers > 0 THEN a.total_loans / a.total_customers 
        ELSE 0 
    END as loan_conversion_rate
FROM agents a
WHERE a.status = 1;

-- ==================== 存储过程创建 ====================

-- 客户风险评分更新存储过程
DELIMITER $$
CREATE PROCEDURE sp_update_customer_risk_score(
    IN p_customer_id BIGINT UNSIGNED,
    IN p_risk_score SMALLINT UNSIGNED,
    IN p_risk_level ENUM('low','medium','high','very_high')
)
BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;
    
    START TRANSACTION;
    
    UPDATE customers 
    SET risk_score = p_risk_score,
        risk_level = p_risk_level,
        update_time = CURRENT_TIMESTAMP
    WHERE id = p_customer_id;
    
    INSERT INTO operation_logs (
        user_type, module, action, target_type, target_id, 
        description, status, created_at
    ) VALUES (
        'system', 'risk_management', 'update_risk_score', 'customer', p_customer_id,
        CONCAT('更新客户风险评分: ', p_risk_score, ', 风险等级: ', p_risk_level),
        'success', CURRENT_TIMESTAMP
    );
    
    COMMIT;
END$$
DELIMITER ;

-- ==================== 初始化配置数据 ====================

-- 插入系统配置
INSERT IGNORE INTO system_configs (`key`, `value`, `type`, description) VALUES
('ai_risk_provider', 'deepseek', 'ai', 'AI风控提供商'),
('ai_risk_model_version', 'v2.0', 'ai', 'AI风控模型版本'),
('max_loan_amount', '200000', 'business', '最大放款金额'),
('min_loan_amount', '1000', 'business', '最小放款金额'),
('default_interest_rate', '0.15', 'business', '默认年利率'),
('risk_score_threshold_low', '80', 'risk', '低风险评分阈值'),
('risk_score_threshold_medium', '60', 'risk', '中风险评分阈值'),
('risk_score_threshold_high', '40', 'risk', '高风险评分阈值'),
('auto_approval_enabled', 'true', 'system', '是否启用自动审批'),
('auto_approval_max_amount', '50000', 'system', '自动审批最大金额'),
('cache_expire_time', '3600', 'system', '缓存过期时间(秒)'),
('api_rate_limit', '1000', 'system', 'API请求频率限制(每分钟)');

-- 优化完成提示
SELECT 'Database optimization completed successfully!' as message;
