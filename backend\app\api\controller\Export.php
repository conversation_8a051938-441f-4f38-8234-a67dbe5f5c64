<?php
declare (strict_types = 1);

namespace app\api\controller;

use app\common\model\LoanDisbursementRecord;
use app\common\model\RepaymentRecord;
use think\Request;

class Export
{
    /** 导出放款记录 CSV */
    public function disbursements(Request $request)
    {
        $status = (string) $request->param('status','');
        $dateFrom = (string) $request->param('date_from','');
        $dateTo = (string) $request->param('date_to','');

        $query = LoanDisbursementRecord::order('created_at','desc');
        if ($status) $query->where('status',$status);
        if ($dateFrom) $query->where('loan_date','>=',$dateFrom);
        if ($dateTo) $query->where('loan_date','<=',$dateTo);
        $rows = $query->limit(5000)->select()->toArray();

        $headers = ['业务流水号','放款日期','客户','放款金额','已回款','未回款','状态'];
        $csv = implode(',', $headers) . "\r\n";
        foreach ($rows as $r) {
            $csv .= implode(',', [
                $r['business_flow_no'],
                $r['loan_date'],
                $r['customer_name'],
                $r['loan_amount'],
                $r['total_repaid_amount'],
                $r['remaining_amount'],
                $r['status'],
            ]) . "\r\n";
        }

        return response($csv, 200, [
            'Content-Type' => 'text/csv; charset=UTF-8',
            'Content-Disposition' => 'attachment; filename="disbursements.csv"'
        ]);
    }

    /** 导出还款记录 CSV */
    public function repayments(Request $request)
    {
        $customerId = (string) $request->param('customer_id','');
        $method = (string) $request->param('repayment_method','');

        $query = RepaymentRecord::order('created_at','desc');
        if ($customerId) $query->where('customer_id',$customerId);
        if ($method) $query->where('repayment_method',$method);
        $rows = $query->limit(5000)->select()->toArray();

        $headers = ['还款流水号','客户','时间','金额','方式','类型','状态'];
        $csv = implode(',', $headers) . "\r\n";
        foreach ($rows as $r) {
            $csv .= implode(',', [
                $r['business_flow_no'],
                $r['customer_name'],
                $r['repayment_date'],
                $r['repayment_amount'],
                $r['repayment_method'],
                $r['repayment_type'],
                $r['status'],
            ]) . "\r\n";
        }

        return response($csv, 200, [
            'Content-Type' => 'text/csv; charset=UTF-8',
            'Content-Disposition' => 'attachment; filename="repayments.csv"'
        ]);
    }
}


