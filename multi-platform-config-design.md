# 多端配置管理系统设计文档

## 一、总后台配置系统架构

### 1. 配置系统表结构设计

```sql
-- 应用配置表 (app_configs)
CREATE TABLE app_configs (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    platform_type ENUM('web','app','miniprogram','h5') NOT NULL COMMENT '平台类型',
    platform_name VARCHAR(50) NOT NULL COMMENT '平台名称（如mp-weixin,mp-alipay）',
    app_type ENUM('admin','customer','agent','investor','collection') NOT NULL COMMENT '应用类型',
    config_key VARCHAR(100) NOT NULL COMMENT '配置键',
    config_value TEXT NOT NULL COMMENT '配置值（JSON格式）',
    config_type ENUM('basic','ui','business','security') NOT NULL COMMENT '配置类型',
    description VARCHAR(255) COMMENT '配置描述',
    is_active TINYINT DEFAULT 1 COMMENT '是否启用',
    created_by INT COMMENT '创建人ID',
    updated_by INT COMMENT '更新人ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    UNIQUE KEY unique_config (platform_type, platform_name, app_type, config_key),
    INDEX idx_platform (platform_type, platform_name),
    INDEX idx_app_type (app_type),
    INDEX idx_config_type (config_type)
) COMMENT='多端应用配置表';

-- 小程序装修配置表 (miniprogram_themes)
CREATE TABLE miniprogram_themes (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    app_type ENUM('admin','customer','agent','investor','collection') NOT NULL COMMENT '应用类型',
    platform_name VARCHAR(50) NOT NULL COMMENT '小程序平台（mp-weixin,mp-alipay等）',
    theme_name VARCHAR(100) NOT NULL COMMENT '主题名称',
    theme_config JSON NOT NULL COMMENT '主题配置（JSON格式）',
    is_default TINYINT DEFAULT 0 COMMENT '是否默认主题',
    is_active TINYINT DEFAULT 1 COMMENT '是否启用',
    preview_image VARCHAR(500) COMMENT '预览图片',
    created_by INT COMMENT '创建人ID',
    updated_by INT COMMENT '更新人ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    UNIQUE KEY unique_theme (app_type, platform_name, theme_name),
    INDEX idx_app_platform (app_type, platform_name)
) COMMENT='小程序主题装修配置表';

-- 页面布局配置表 (page_layouts)
CREATE TABLE page_layouts (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    app_type ENUM('admin','customer','agent','investor','collection') NOT NULL,
    platform_type ENUM('web','app','miniprogram','h5') NOT NULL,
    platform_name VARCHAR(50) NOT NULL,
    page_name VARCHAR(100) NOT NULL COMMENT '页面名称',
    layout_config JSON NOT NULL COMMENT '布局配置',
    components JSON COMMENT '组件配置',
    is_active TINYINT DEFAULT 1,
    sort_order INT DEFAULT 0 COMMENT '排序',
    created_by INT,
    updated_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    UNIQUE KEY unique_page (app_type, platform_type, platform_name, page_name),
    INDEX idx_app_platform (app_type, platform_type, platform_name)
) COMMENT='页面布局配置表';

-- 组件库表 (component_library)
CREATE TABLE component_library (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    component_name VARCHAR(100) NOT NULL COMMENT '组件名称',
    component_type VARCHAR(50) NOT NULL COMMENT '组件类型',
    component_code TEXT NOT NULL COMMENT '组件代码',
    default_config JSON COMMENT '默认配置',
    config_schema JSON COMMENT '配置结构',
    preview_image VARCHAR(500) COMMENT '预览图片',
    description TEXT COMMENT '组件描述',
    supported_platforms JSON COMMENT '支持平台',
    is_active TINYINT DEFAULT 1,
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    UNIQUE KEY unique_component (component_name, component_type),
    INDEX idx_component_type (component_type)
) COMMENT='组件库表';
```

### 2. 配置管理服务类

```php
// app/common/service/ConfigService.php
<?php

namespace app\common\service;

use app\common\model\AppConfig;
use app\common\model\MiniprogramTheme;
use app\common\model\PageLayout;
use think\facade\Cache;

class ConfigService
{
    /**
     * 获取应用配置
     * @param string $platformType 平台类型
     * @param string $platformName 平台名称
     * @param string $appType 应用类型
     * @param string $configKey 配置键
     * @return mixed
     */
    public static function getConfig($platformType, $platformName, $appType, $configKey = null)
    {
        $cacheKey = "app_config:{$platformType}:{$platformName}:{$appType}";
        
        $configs = Cache::remember($cacheKey, function() use ($platformType, $platformName, $appType) {
            return AppConfig::where([
                'platform_type' => $platformType,
                'platform_name' => $platformName,
                'app_type' => $appType,
                'is_active' => 1
            ])->column('config_value', 'config_key');
        }, 3600);
        
        if ($configKey) {
            return isset($configs[$configKey]) ? json_decode($configs[$configKey], true) : null;
        }
        
        // 解析所有配置值
        $result = [];
        foreach ($configs as $key => $value) {
            $result[$key] = json_decode($value, true);
        }
        
        return $result;
    }
    
    /**
     * 设置应用配置
     */
    public static function setConfig($platformType, $platformName, $appType, $configKey, $configValue, $configType = 'basic')
    {
        $config = AppConfig::where([
            'platform_type' => $platformType,
            'platform_name' => $platformName,
            'app_type' => $appType,
            'config_key' => $configKey
        ])->find();
        
        $data = [
            'platform_type' => $platformType,
            'platform_name' => $platformName,
            'app_type' => $appType,
            'config_key' => $configKey,
            'config_value' => json_encode($configValue, JSON_UNESCAPED_UNICODE),
            'config_type' => $configType,
            'updated_by' => request()->user_id ?? 0
        ];
        
        if ($config) {
            $config->save($data);
        } else {
            $data['created_by'] = request()->user_id ?? 0;
            AppConfig::create($data);
        }
        
        // 清除缓存
        $cacheKey = "app_config:{$platformType}:{$platformName}:{$appType}";
        Cache::delete($cacheKey);
        
        return true;
    }
    
    /**
     * 批量设置配置
     */
    public static function setBatchConfig($platformType, $platformName, $appType, $configs)
    {
        foreach ($configs as $configKey => $configData) {
            self::setConfig(
                $platformType, 
                $platformName, 
                $appType, 
                $configKey, 
                $configData['value'], 
                $configData['type'] ?? 'basic'
            );
        }
        
        return true;
    }
    
    /**
     * 获取小程序主题配置
     */
    public static function getMiniprogramTheme($appType, $platformName, $themeName = null)
    {
        $where = [
            'app_type' => $appType,
            'platform_name' => $platformName,
            'is_active' => 1
        ];
        
        if ($themeName) {
            $where['theme_name'] = $themeName;
            return MiniprogramTheme::where($where)->find();
        } else {
            // 获取默认主题
            $where['is_default'] = 1;
            $theme = MiniprogramTheme::where($where)->find();
            
            if (!$theme) {
                // 如果没有默认主题，获取第一个主题
                unset($where['is_default']);
                $theme = MiniprogramTheme::where($where)->order('id', 'asc')->find();
            }
            
            return $theme;
        }
    }
    
    /**
     * 保存小程序主题
     */
    public static function saveMiniprogramTheme($appType, $platformName, $themeName, $themeConfig, $isDefault = false)
    {
        $theme = MiniprogramTheme::where([
            'app_type' => $appType,
            'platform_name' => $platformName,
            'theme_name' => $themeName
        ])->find();
        
        $data = [
            'app_type' => $appType,
            'platform_name' => $platformName,
            'theme_name' => $themeName,
            'theme_config' => json_encode($themeConfig, JSON_UNESCAPED_UNICODE),
            'is_default' => $isDefault ? 1 : 0,
            'updated_by' => request()->user_id ?? 0
        ];
        
        if ($theme) {
            $theme->save($data);
        } else {
            $data['created_by'] = request()->user_id ?? 0;
            $theme = MiniprogramTheme::create($data);
        }
        
        // 如果设置为默认主题，取消其他默认主题
        if ($isDefault) {
            MiniprogramTheme::where([
                'app_type' => $appType,
                'platform_name' => $platformName,
                'id' => ['<>', $theme->id]
            ])->update(['is_default' => 0]);
        }
        
        return $theme;
    }
}
```

### 3. 配置管理控制器

```php
// app/admin/controller/ConfigController.php
<?php

namespace app\admin\controller;

use app\common\service\ConfigService;
use app\common\service\MiniprogramService;

class ConfigController extends BaseController
{
    /**
     * 配置管理首页
     */
    public function index()
    {
        $platformTypes = [
            'web' => 'Web管理台',
            'app' => 'APP应用',
            'miniprogram' => '小程序',
            'h5' => 'H5页面'
        ];
        
        $appTypes = [
            'admin' => '管理端',
            'customer' => '客户端',
            'agent' => '中介端',
            'investor' => '资方端',
            'collection' => '催收端'
        ];
        
        $platforms = [
            'miniprogram' => [
                'mp-weixin' => '微信小程序',
                'mp-alipay' => '支付宝小程序',
                'mp-baidu' => '百度小程序',
                'mp-toutiao' => '头条小程序'
            ]
        ];
        
        return $this->view->assign([
            'platformTypes' => $platformTypes,
            'appTypes' => $appTypes,
            'platforms' => $platforms
        ])->fetch();
    }
    
    /**
     * 获取配置列表
     */
    public function getConfigs()
    {
        $platformType = $this->request->param('platform_type');
        $platformName = $this->request->param('platform_name');
        $appType = $this->request->param('app_type');
        
        $configs = ConfigService::getConfig($platformType, $platformName, $appType);
        
        return json(['code' => 200, 'data' => $configs]);
    }
    
    /**
     * 保存配置
     */
    public function saveConfig()
    {
        $data = $this->request->param();
        
        $result = ConfigService::setBatchConfig(
            $data['platform_type'],
            $data['platform_name'],
            $data['app_type'],
            $data['configs']
        );
        
        if ($result) {
            return json(['code' => 200, 'message' => '配置保存成功']);
        } else {
            return json(['code' => 500, 'message' => '配置保存失败']);
        }
    }
    
    /**
     * 小程序装修配置
     */
    public function miniprogramCustomize()
    {
        $appType = $this->request->param('app_type', 'customer');
        $platformName = $this->request->param('platform_name', 'mp-weixin');
        
        // 获取当前主题
        $currentTheme = ConfigService::getMiniprogramTheme($appType, $platformName);
        
        // 获取组件库
        $components = MiniprogramService::getComponentLibrary();
        
        return $this->view->assign([
            'appType' => $appType,
            'platformName' => $platformName,
            'currentTheme' => $currentTheme,
            'components' => $components
        ])->fetch();
    }
    
    /**
     * 保存小程序主题
     */
    public function saveMiniprogramTheme()
    {
        $data = $this->request->param();
        
        $result = ConfigService::saveMiniprogramTheme(
            $data['app_type'],
            $data['platform_name'],
            $data['theme_name'],
            $data['theme_config'],
            $data['is_default'] ?? false
        );
        
        if ($result) {
            return json(['code' => 200, 'message' => '主题保存成功', 'data' => $result]);
        } else {
            return json(['code' => 500, 'message' => '主题保存失败']);
        }
    }
}
```

## 二、小程序可视化装修系统

### 1. 小程序装修服务

```php
// app/common/service/MiniprogramService.php
<?php

namespace app\common\service;

use app\common\model\ComponentLibrary;
use app\common\model\PageLayout;

class MiniprogramService
{
    /**
     * 获取组件库
     */
    public static function getComponentLibrary($componentType = null)
    {
        $where = ['is_active' => 1];
        if ($componentType) {
            $where['component_type'] = $componentType;
        }
        
        return ComponentLibrary::where($where)->order('component_type', 'asc')->select();
    }
    
    /**
     * 获取页面布局
     */
    public static function getPageLayout($appType, $platformName, $pageName)
    {
        return PageLayout::where([
            'app_type' => $appType,
            'platform_type' => 'miniprogram',
            'platform_name' => $platformName,
            'page_name' => $pageName,
            'is_active' => 1
        ])->find();
    }
    
    /**
     * 保存页面布局
     */
    public static function savePageLayout($appType, $platformName, $pageName, $layoutConfig, $components)
    {
        $layout = self::getPageLayout($appType, $platformName, $pageName);
        
        $data = [
            'app_type' => $appType,
            'platform_type' => 'miniprogram',
            'platform_name' => $platformName,
            'page_name' => $pageName,
            'layout_config' => json_encode($layoutConfig, JSON_UNESCAPED_UNICODE),
            'components' => json_encode($components, JSON_UNESCAPED_UNICODE),
            'updated_by' => request()->user_id ?? 0
        ];
        
        if ($layout) {
            $layout->save($data);
        } else {
            $data['created_by'] = request()->user_id ?? 0;
            $layout = PageLayout::create($data);
        }
        
        return $layout;
    }
    
    /**
     * 生成小程序配置文件
     */
    public static function generateMiniprogramConfig($appType, $platformName)
    {
        // 获取基础配置
        $baseConfig = ConfigService::getConfig('miniprogram', $platformName, $appType);
        
        // 获取主题配置
        $themeConfig = ConfigService::getMiniprogramTheme($appType, $platformName);
        
        // 获取页面配置
        $pages = self::getAppPages($appType, $platformName);
        
        $config = [
            'app' => [
                'appid' => $baseConfig['appid'] ?? '',
                'name' => $baseConfig['app_name'] ?? '',
                'version' => $baseConfig['version'] ?? '1.0.0'
            ],
            'theme' => $themeConfig ? $themeConfig['theme_config'] : [],
            'pages' => $pages,
            'tabBar' => $baseConfig['tab_bar'] ?? [],
            'window' => $baseConfig['window'] ?? [],
            'networkTimeout' => $baseConfig['network_timeout'] ?? []
        ];
        
        return $config;
    }
    
    /**
     * 获取应用页面配置
     */
    private static function getAppPages($appType, $platformName)
    {
        $layouts = PageLayout::where([
            'app_type' => $appType,
            'platform_type' => 'miniprogram',
            'platform_name' => $platformName,
            'is_active' => 1
        ])->order('sort_order', 'asc')->select();
        
        $pages = [];
        foreach ($layouts as $layout) {
            $pages[$layout['page_name']] = [
                'layout' => json_decode($layout['layout_config'], true),
                'components' => json_decode($layout['components'], true)
            ];
        }
        
        return $pages;
    }
}
```

### 2. 小程序装修页面

```html
<!-- app/admin/view/config/miniprogram_customize.html -->
<!DOCTYPE html>
<html>
<head>
    <title>小程序装修配置</title>
    <link rel="stylesheet" href="/static/css/admin.css">
    <link rel="stylesheet" href="/static/plugins/element-ui/index.css">
</head>
<body>
    <div id="app">
        <el-container>
            <!-- 左侧组件库 -->
            <el-aside width="300px" class="component-sidebar">
                <div class="sidebar-header">
                    <h3>组件库</h3>
                </div>
                <div class="component-list">
                    <el-collapse v-model="activeComponents">
                        <el-collapse-item 
                            v-for="(components, type) in componentLibrary" 
                            :key="type" 
                            :title="getComponentTypeName(type)" 
                            :name="type"
                        >
                            <div 
                                v-for="component in components" 
                                :key="component.id"
                                class="component-item"
                                draggable="true"
                                @dragstart="onDragStart(component)"
                            >
                                <img :src="component.preview_image" :alt="component.component_name">
                                <span>{{ component.component_name }}</span>
                            </div>
                        </el-collapse-item>
                    </el-collapse>
                </div>
            </el-aside>
            
            <!-- 中间画布区域 -->
            <el-main class="canvas-area">
                <div class="canvas-header">
                    <el-button-group>
                        <el-button 
                            v-for="page in pages" 
                            :key="page.name"
                            :type="currentPage === page.name ? 'primary' : 'default'"
                            @click="switchPage(page.name)"
                        >
                            {{ page.title }}
                        </el-button>
                    </el-button-group>
                    <div class="canvas-actions">
                        <el-button @click="previewMiniprogram">预览</el-button>
                        <el-button type="primary" @click="savePage">保存</el-button>
                    </div>
                </div>
                
                <div class="canvas-container">
                    <div 
                        class="miniprogram-canvas"
                        @drop="onDrop"
                        @dragover.prevent
                    >
                        <div 
                            v-for="(component, index) in currentPageComponents" 
                            :key="index"
                            class="canvas-component"
                            :class="{ active: selectedComponent === index }"
                            @click="selectComponent(index)"
                        >
                            <component 
                                :is="component.type"
                                :config="component.config"
                                :data="component.data"
                            ></component>
                            <div class="component-toolbar">
                                <el-button-group>
                                    <el-button size="mini" @click="moveUp(index)">↑</el-button>
                                    <el-button size="mini" @click="moveDown(index)">↓</el-button>
                                    <el-button size="mini" @click="copyComponent(index)">复制</el-button>
                                    <el-button size="mini" type="danger" @click="deleteComponent(index)">删除</el-button>
                                </el-button-group>
                            </div>
                        </div>
                    </div>
                </div>
            </el-main>
            
            <!-- 右侧属性配置 -->
            <el-aside width="350px" class="property-sidebar">
                <div class="sidebar-header">
                    <h3>属性配置</h3>
                </div>
                
                <!-- 页面配置 -->
                <el-card v-if="selectedComponent === null" class="property-card">
                    <div slot="header">页面设置</div>
                    <el-form label-width="80px">
                        <el-form-item label="页面标题">
                            <el-input v-model="currentPageConfig.title"></el-input>
                        </el-form-item>
                        <el-form-item label="背景颜色">
                            <el-color-picker v-model="currentPageConfig.backgroundColor"></el-color-picker>
                        </el-form-item>
                        <el-form-item label="背景图片">
                            <el-upload
                                :action="uploadUrl"
                                :show-file-list="false"
                                :on-success="onBackgroundUpload"
                            >
                                <el-button size="small" type="primary">上传图片</el-button>
                            </el-upload>
                        </el-form-item>
                    </el-form>
                </el-card>
                
                <!-- 组件配置 -->
                <el-card v-else class="property-card">
                    <div slot="header">组件配置</div>
                    <component-config-form
                        :component="currentPageComponents[selectedComponent]"
                        @change="onComponentConfigChange"
                    ></component-config-form>
                </el-card>
                
                <!-- 主题配置 -->
                <el-card class="property-card">
                    <div slot="header">主题配置</div>
                    <el-form label-width="80px">
                        <el-form-item label="主色调">
                            <el-color-picker v-model="themeConfig.primaryColor"></el-color-picker>
                        </el-form-item>
                        <el-form-item label="辅助色">
                            <el-color-picker v-model="themeConfig.secondaryColor"></el-color-picker>
                        </el-form-item>
                        <el-form-item label="文字颜色">
                            <el-color-picker v-model="themeConfig.textColor"></el-color-picker>
                        </el-form-item>
                        <el-form-item label="边框半径">
                            <el-slider v-model="themeConfig.borderRadius" :max="20"></el-slider>
                        </el-form-item>
                    </el-form>
                </el-card>
            </el-aside>
        </el-container>
        
        <!-- 预览对话框 -->
        <el-dialog title="小程序预览" :visible.sync="previewVisible" width="400px">
            <div class="miniprogram-preview">
                <iframe :src="previewUrl" width="350" height="600"></iframe>
            </div>
        </el-dialog>
    </div>
    
    <script src="/static/js/vue.js"></script>
    <script src="/static/plugins/element-ui/index.js"></script>
    <script src="/static/js/miniprogram-customize.js"></script>
</body>
</html>
```

### 3. 小程序装修JavaScript

```javascript
// static/js/miniprogram-customize.js
new Vue({
    el: '#app',
    data: {
        // 应用基本信息
        appType: '{$appType}',
        platformName: '{$platformName}',
        
        // 组件库
        componentLibrary: {},
        activeComponents: ['basic', 'business'],
        
        // 页面管理
        pages: [
            { name: 'index', title: '首页' },
            { name: 'apply', title: '申请页' },
            { name: 'profile', title: '个人中心' }
        ],
        currentPage: 'index',
        currentPageConfig: {
            title: '首页',
            backgroundColor: '#ffffff',
            backgroundImage: ''
        },
        currentPageComponents: [],
        
        // 选中状态
        selectedComponent: null,
        
        // 主题配置
        themeConfig: {
            primaryColor: '#1890ff',
            secondaryColor: '#52c41a',
            textColor: '#333333',
            borderRadius: 4
        },
        
        // 预览
        previewVisible: false,
        previewUrl: '',
        
        // 其他
        uploadUrl: '/admin/upload/image'
    },
    
    mounted() {
        this.loadComponentLibrary();
        this.loadPageData();
        this.loadThemeConfig();
    },
    
    methods: {
        // 加载组件库
        loadComponentLibrary() {
            axios.get('/admin/config/getComponentLibrary').then(response => {
                if (response.data.code === 200) {
                    this.componentLibrary = this.groupComponentsByType(response.data.data);
                }
            });
        },
        
        // 按类型分组组件
        groupComponentsByType(components) {
            const grouped = {};
            components.forEach(component => {
                if (!grouped[component.component_type]) {
                    grouped[component.component_type] = [];
                }
                grouped[component.component_type].push(component);
            });
            return grouped;
        },
        
        // 获取组件类型名称
        getComponentTypeName(type) {
            const typeNames = {
                'basic': '基础组件',
                'form': '表单组件',
                'business': '业务组件',
                'chart': '图表组件'
            };
            return typeNames[type] || type;
        },
        
        // 加载页面数据
        loadPageData() {
            axios.get('/admin/config/getPageLayout', {
                params: {
                    app_type: this.appType,
                    platform_name: this.platformName,
                    page_name: this.currentPage
                }
            }).then(response => {
                if (response.data.code === 200 && response.data.data) {
                    this.currentPageConfig = response.data.data.layout_config || this.currentPageConfig;
                    this.currentPageComponents = response.data.data.components || [];
                }
            });
        },
        
        // 加载主题配置
        loadThemeConfig() {
            axios.get('/admin/config/getMiniprogramTheme', {
                params: {
                    app_type: this.appType,
                    platform_name: this.platformName
                }
            }).then(response => {
                if (response.data.code === 200 && response.data.data) {
                    this.themeConfig = Object.assign(this.themeConfig, response.data.data.theme_config);
                }
            });
        },
        
        // 切换页面
        switchPage(pageName) {
            this.savePage(); // 保存当前页面
            this.currentPage = pageName;
            this.selectedComponent = null;
            this.loadPageData();
        },
        
        // 拖拽开始
        onDragStart(component) {
            event.dataTransfer.setData('component', JSON.stringify(component));
        },
        
        // 拖拽放置
        onDrop(event) {
            event.preventDefault();
            const componentData = JSON.parse(event.dataTransfer.getData('component'));
            
            const newComponent = {
                type: componentData.component_name,
                config: JSON.parse(componentData.default_config || '{}'),
                data: {}
            };
            
            this.currentPageComponents.push(newComponent);
        },
        
        // 选中组件
        selectComponent(index) {
            this.selectedComponent = index;
        },
        
        // 组件配置变更
        onComponentConfigChange(config) {
            if (this.selectedComponent !== null) {
                this.$set(this.currentPageComponents[this.selectedComponent], 'config', config);
            }
        },
        
        // 移动组件
        moveUp(index) {
            if (index > 0) {
                const component = this.currentPageComponents.splice(index, 1)[0];
                this.currentPageComponents.splice(index - 1, 0, component);
                this.selectedComponent = index - 1;
            }
        },
        
        moveDown(index) {
            if (index < this.currentPageComponents.length - 1) {
                const component = this.currentPageComponents.splice(index, 1)[0];
                this.currentPageComponents.splice(index + 1, 0, component);
                this.selectedComponent = index + 1;
            }
        },
        
        // 复制组件
        copyComponent(index) {
            const component = JSON.parse(JSON.stringify(this.currentPageComponents[index]));
            this.currentPageComponents.splice(index + 1, 0, component);
        },
        
        // 删除组件
        deleteComponent(index) {
            this.$confirm('确定删除此组件吗？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.currentPageComponents.splice(index, 1);
                this.selectedComponent = null;
            });
        },
        
        // 背景图片上传
        onBackgroundUpload(response) {
            if (response.code === 200) {
                this.currentPageConfig.backgroundImage = response.data.url;
            }
        },
        
        // 保存页面
        savePage() {
            const data = {
                app_type: this.appType,
                platform_name: this.platformName,
                page_name: this.currentPage,
                layout_config: this.currentPageConfig,
                components: this.currentPageComponents
            };
            
            axios.post('/admin/config/savePageLayout', data).then(response => {
                if (response.data.code === 200) {
                    this.$message.success('页面保存成功');
                } else {
                    this.$message.error('页面保存失败：' + response.data.message);
                }
            });
        },
        
        // 保存主题
        saveTheme() {
            const data = {
                app_type: this.appType,
                platform_name: this.platformName,
                theme_name: 'default',
                theme_config: this.themeConfig,
                is_default: true
            };
            
            axios.post('/admin/config/saveMiniprogramTheme', data).then(response => {
                if (response.data.code === 200) {
                    this.$message.success('主题保存成功');
                } else {
                    this.$message.error('主题保存失败：' + response.data.message);
                }
            });
        },
        
        // 预览小程序
        previewMiniprogram() {
            this.savePage();
            this.saveTheme();
            
            // 生成预览链接
            this.previewUrl = `/miniprogram/preview?app_type=${this.appType}&platform_name=${this.platformName}`;
            this.previewVisible = true;
        }
    },
    
    watch: {
        themeConfig: {
            handler() {
                this.saveTheme();
            },
            deep: true
        }
    }
});
```
