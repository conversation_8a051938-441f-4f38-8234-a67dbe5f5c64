# 小程序组件库设计

## 一、组件库架构

### 1. 基础组件

```javascript
// 文本组件
const TextComponent = {
    name: 'text-component',
    type: 'basic',
    template: `
        <view class="text-component" :style="textStyle">
            {{ config.content }}
        </view>
    `,
    props: ['config'],
    computed: {
        textStyle() {
            return {
                color: this.config.color || '#333',
                fontSize: this.config.fontSize + 'px' || '14px',
                textAlign: this.config.textAlign || 'left',
                fontWeight: this.config.fontWeight || 'normal',
                lineHeight: this.config.lineHeight || '1.5'
            }
        }
    },
    defaultConfig: {
        content: '请输入文本内容',
        color: '#333333',
        fontSize: 14,
        textAlign: 'left',
        fontWeight: 'normal',
        lineHeight: 1.5
    },
    configSchema: [
        { field: 'content', label: '文本内容', type: 'textarea' },
        { field: 'color', label: '文字颜色', type: 'color' },
        { field: 'fontSize', label: '字体大小', type: 'number', min: 12, max: 30 },
        { field: 'textAlign', label: '对齐方式', type: 'select', options: [
            { label: '左对齐', value: 'left' },
            { label: '居中', value: 'center' },
            { label: '右对齐', value: 'right' }
        ]},
        { field: 'fontWeight', label: '字体粗细', type: 'select', options: [
            { label: '正常', value: 'normal' },
            { label: '粗体', value: 'bold' }
        ]}
    ]
};

// 图片组件
const ImageComponent = {
    name: 'image-component',
    type: 'basic',
    template: `
        <view class="image-component" :style="containerStyle">
            <image 
                :src="config.src" 
                :mode="config.mode"
                :style="imageStyle"
                @click="handleClick"
            />
        </view>
    `,
    props: ['config'],
    computed: {
        containerStyle() {
            return {
                textAlign: this.config.align || 'center'
            }
        },
        imageStyle() {
            return {
                width: this.config.width + 'px' || '100%',
                height: this.config.height + 'px' || 'auto',
                borderRadius: this.config.borderRadius + 'px' || '0px'
            }
        }
    },
    methods: {
        handleClick() {
            if (this.config.clickAction === 'link' && this.config.link) {
                uni.navigateTo({ url: this.config.link });
            }
        }
    },
    defaultConfig: {
        src: '/static/images/placeholder.png',
        mode: 'aspectFit',
        width: 200,
        height: 150,
        borderRadius: 0,
        align: 'center',
        clickAction: 'none',
        link: ''
    },
    configSchema: [
        { field: 'src', label: '图片地址', type: 'upload' },
        { field: 'mode', label: '缩放模式', type: 'select', options: [
            { label: '缩放', value: 'aspectFit' },
            { label: '填充', value: 'aspectFill' },
            { label: '拉伸', value: 'scaleToFill' }
        ]},
        { field: 'width', label: '宽度', type: 'number', min: 50, max: 750 },
        { field: 'height', label: '高度', type: 'number', min: 50, max: 500 },
        { field: 'borderRadius', label: '圆角', type: 'number', min: 0, max: 50 },
        { field: 'align', label: '对齐方式', type: 'select', options: [
            { label: '左对齐', value: 'left' },
            { label: '居中', value: 'center' },
            { label: '右对齐', value: 'right' }
        ]},
        { field: 'clickAction', label: '点击行为', type: 'select', options: [
            { label: '无动作', value: 'none' },
            { label: '跳转链接', value: 'link' }
        ]},
        { field: 'link', label: '跳转链接', type: 'text', showIf: 'clickAction=link' }
    ]
};

// 按钮组件
const ButtonComponent = {
    name: 'button-component',
    type: 'basic',
    template: `
        <view class="button-component" :style="containerStyle">
            <button 
                :class="buttonClass"
                :style="buttonStyle"
                @click="handleClick"
            >
                {{ config.text }}
            </button>
        </view>
    `,
    props: ['config'],
    computed: {
        containerStyle() {
            return {
                textAlign: this.config.align || 'center',
                padding: '10px'
            }
        },
        buttonClass() {
            return `btn-${this.config.type || 'primary'}`;
        },
        buttonStyle() {
            return {
                backgroundColor: this.config.backgroundColor || '#1890ff',
                color: this.config.textColor || '#ffffff',
                borderRadius: this.config.borderRadius + 'px' || '4px',
                padding: this.config.padding || '12px 24px',
                fontSize: this.config.fontSize + 'px' || '16px',
                border: 'none',
                width: this.config.width || 'auto'
            }
        }
    },
    methods: {
        handleClick() {
            switch(this.config.action) {
                case 'link':
                    uni.navigateTo({ url: this.config.link });
                    break;
                case 'call':
                    uni.makePhoneCall({ phoneNumber: this.config.phone });
                    break;
                case 'submit':
                    this.$emit('submit');
                    break;
            }
        }
    },
    defaultConfig: {
        text: '按钮文字',
        type: 'primary',
        backgroundColor: '#1890ff',
        textColor: '#ffffff',
        borderRadius: 4,
        padding: '12px 24px',
        fontSize: 16,
        width: 'auto',
        align: 'center',
        action: 'none',
        link: '',
        phone: ''
    },
    configSchema: [
        { field: 'text', label: '按钮文字', type: 'text' },
        { field: 'type', label: '按钮类型', type: 'select', options: [
            { label: '主要按钮', value: 'primary' },
            { label: '次要按钮', value: 'secondary' },
            { label: '成功按钮', value: 'success' },
            { label: '警告按钮', value: 'warning' },
            { label: '危险按钮', value: 'danger' }
        ]},
        { field: 'backgroundColor', label: '背景颜色', type: 'color' },
        { field: 'textColor', label: '文字颜色', type: 'color' },
        { field: 'borderRadius', label: '圆角', type: 'number', min: 0, max: 20 },
        { field: 'fontSize', label: '字体大小', type: 'number', min: 12, max: 24 },
        { field: 'align', label: '对齐方式', type: 'select', options: [
            { label: '左对齐', value: 'left' },
            { label: '居中', value: 'center' },
            { label: '右对齐', value: 'right' }
        ]},
        { field: 'action', label: '点击行为', type: 'select', options: [
            { label: '无动作', value: 'none' },
            { label: '跳转页面', value: 'link' },
            { label: '拨打电话', value: 'call' },
            { label: '提交表单', value: 'submit' }
        ]},
        { field: 'link', label: '跳转链接', type: 'text', showIf: 'action=link' },
        { field: 'phone', label: '电话号码', type: 'text', showIf: 'action=call' }
    ]
};
```

### 2. 表单组件

```javascript
// 输入框组件
const InputComponent = {
    name: 'input-component',
    type: 'form',
    template: `
        <view class="input-component">
            <view v-if="config.label" class="input-label">{{ config.label }}</view>
            <input 
                :type="config.inputType"
                :placeholder="config.placeholder"
                :value="value"
                :maxlength="config.maxlength"
                :disabled="config.disabled"
                :style="inputStyle"
                @input="handleInput"
                @blur="handleBlur"
            />
            <view v-if="config.required" class="required-mark">*</view>
            <view v-if="errorMessage" class="error-message">{{ errorMessage }}</view>
        </view>
    `,
    props: ['config', 'value'],
    data() {
        return {
            errorMessage: ''
        }
    },
    computed: {
        inputStyle() {
            return {
                border: '1px solid ' + (this.config.borderColor || '#ddd'),
                borderRadius: this.config.borderRadius + 'px' || '4px',
                padding: this.config.padding || '12px',
                fontSize: this.config.fontSize + 'px' || '14px',
                backgroundColor: this.config.backgroundColor || '#ffffff'
            }
        }
    },
    methods: {
        handleInput(e) {
            this.$emit('input', e.detail.value);
            this.validate(e.detail.value);
        },
        handleBlur(e) {
            this.validate(e.detail.value);
        },
        validate(value) {
            this.errorMessage = '';
            
            if (this.config.required && !value) {
                this.errorMessage = this.config.label + '不能为空';
                return false;
            }
            
            if (this.config.pattern && !new RegExp(this.config.pattern).test(value)) {
                this.errorMessage = this.config.errorMessage || '格式不正确';
                return false;
            }
            
            return true;
        }
    },
    defaultConfig: {
        label: '输入框',
        placeholder: '请输入内容',
        inputType: 'text',
        maxlength: 100,
        required: false,
        disabled: false,
        borderColor: '#ddd',
        borderRadius: 4,
        padding: '12px',
        fontSize: 14,
        backgroundColor: '#ffffff',
        pattern: '',
        errorMessage: ''
    },
    configSchema: [
        { field: 'label', label: '标签文字', type: 'text' },
        { field: 'placeholder', label: '占位符', type: 'text' },
        { field: 'inputType', label: '输入类型', type: 'select', options: [
            { label: '文本', value: 'text' },
            { label: '数字', value: 'number' },
            { label: '密码', value: 'password' },
            { label: '手机号', value: 'tel' }
        ]},
        { field: 'maxlength', label: '最大长度', type: 'number', min: 1, max: 500 },
        { field: 'required', label: '必填项', type: 'switch' },
        { field: 'disabled', label: '禁用状态', type: 'switch' },
        { field: 'borderColor', label: '边框颜色', type: 'color' },
        { field: 'borderRadius', label: '圆角', type: 'number', min: 0, max: 20 },
        { field: 'fontSize', label: '字体大小', type: 'number', min: 12, max: 20 },
        { field: 'backgroundColor', label: '背景颜色', type: 'color' },
        { field: 'pattern', label: '验证正则', type: 'text' },
        { field: 'errorMessage', label: '错误提示', type: 'text' }
    ]
};

// 选择器组件
const PickerComponent = {
    name: 'picker-component',
    type: 'form',
    template: `
        <view class="picker-component">
            <view v-if="config.label" class="picker-label">{{ config.label }}</view>
            <picker 
                :mode="config.mode"
                :range="config.options"
                :value="selectedIndex"
                :range-key="config.rangeKey"
                :disabled="config.disabled"
                @change="handleChange"
            >
                <view class="picker-selector" :style="selectorStyle">
                    {{ displayValue || config.placeholder }}
                    <text class="picker-arrow">▼</text>
                </view>
            </picker>
            <view v-if="config.required" class="required-mark">*</view>
        </view>
    `,
    props: ['config', 'value'],
    computed: {
        selectedIndex() {
            if (this.config.mode === 'selector') {
                return this.config.options.findIndex(item => 
                    (typeof item === 'object' ? item[this.config.rangeKey] : item) === this.value
                );
            }
            return 0;
        },
        displayValue() {
            if (this.value && this.config.mode === 'selector') {
                const option = this.config.options[this.selectedIndex];
                return typeof option === 'object' ? option[this.config.rangeKey] : option;
            }
            return this.value;
        },
        selectorStyle() {
            return {
                border: '1px solid ' + (this.config.borderColor || '#ddd'),
                borderRadius: this.config.borderRadius + 'px' || '4px',
                padding: this.config.padding || '12px',
                fontSize: this.config.fontSize + 'px' || '14px',
                backgroundColor: this.config.backgroundColor || '#ffffff',
                color: this.value ? '#333' : '#999'
            }
        }
    },
    methods: {
        handleChange(e) {
            const index = e.detail.value;
            if (this.config.mode === 'selector') {
                const option = this.config.options[index];
                const value = typeof option === 'object' ? option.value : option;
                this.$emit('input', value);
            } else {
                this.$emit('input', e.detail.value);
            }
        }
    },
    defaultConfig: {
        label: '选择器',
        placeholder: '请选择',
        mode: 'selector',
        options: [
            { label: '选项1', value: 'option1' },
            { label: '选项2', value: 'option2' }
        ],
        rangeKey: 'label',
        required: false,
        disabled: false,
        borderColor: '#ddd',
        borderRadius: 4,
        padding: '12px',
        fontSize: 14,
        backgroundColor: '#ffffff'
    },
    configSchema: [
        { field: 'label', label: '标签文字', type: 'text' },
        { field: 'placeholder', label: '占位符', type: 'text' },
        { field: 'mode', label: '选择器类型', type: 'select', options: [
            { label: '普通选择器', value: 'selector' },
            { label: '日期选择器', value: 'date' },
            { label: '时间选择器', value: 'time' }
        ]},
        { field: 'options', label: '选项配置', type: 'json', showIf: 'mode=selector' },
        { field: 'required', label: '必填项', type: 'switch' },
        { field: 'disabled', label: '禁用状态', type: 'switch' },
        { field: 'borderColor', label: '边框颜色', type: 'color' },
        { field: 'borderRadius', label: '圆角', type: 'number', min: 0, max: 20 },
        { field: 'fontSize', label: '字体大小', type: 'number', min: 12, max: 20 },
        { field: 'backgroundColor', label: '背景颜色', type: 'color' }
    ]
};
```

### 3. 业务组件

```javascript
// 贷款申请卡片
const LoanApplyCard = {
    name: 'loan-apply-card',
    type: 'business',
    template: `
        <view class="loan-apply-card" :style="cardStyle">
            <view class="card-header">
                <text class="card-title">{{ config.title }}</text>
                <text class="card-subtitle">{{ config.subtitle }}</text>
            </view>
            <view class="card-content">
                <view class="amount-display">
                    <text class="amount-label">{{ config.amountLabel }}</text>
                    <text class="amount-value">{{ config.maxAmount }}</text>
                    <text class="amount-unit">{{ config.amountUnit }}</text>
                </view>
                <view class="features-list">
                    <text 
                        v-for="feature in config.features" 
                        :key="feature"
                        class="feature-item"
                    >
                        ✓ {{ feature }}
                    </text>
                </view>
            </view>
            <view class="card-footer">
                <button 
                    class="apply-button"
                    :style="buttonStyle"
                    @click="handleApply"
                >
                    {{ config.buttonText }}
                </button>
            </view>
        </view>
    `,
    props: ['config'],
    computed: {
        cardStyle() {
            return {
                backgroundColor: this.config.backgroundColor || '#ffffff',
                borderRadius: this.config.borderRadius + 'px' || '8px',
                boxShadow: this.config.showShadow ? '0 2px 8px rgba(0,0,0,0.1)' : 'none',
                margin: this.config.margin || '16px',
                padding: this.config.padding || '20px'
            }
        },
        buttonStyle() {
            return {
                backgroundColor: this.config.buttonColor || '#1890ff',
                color: this.config.buttonTextColor || '#ffffff',
                borderRadius: this.config.buttonRadius + 'px' || '4px'
            }
        }
    },
    methods: {
        handleApply() {
            uni.navigateTo({ url: this.config.applyUrl || '/pages/apply/index' });
        }
    },
    defaultConfig: {
        title: '快速借款',
        subtitle: '额度高 放款快 利息低',
        amountLabel: '最高可借',
        maxAmount: '50000',
        amountUnit: '元',
        features: ['实时审批', '极速放款', '随借随还'],
        buttonText: '立即申请',
        buttonColor: '#1890ff',
        buttonTextColor: '#ffffff',
        buttonRadius: 4,
        backgroundColor: '#ffffff',
        borderRadius: 8,
        showShadow: true,
        margin: '16px',
        padding: '20px',
        applyUrl: '/pages/apply/index'
    },
    configSchema: [
        { field: 'title', label: '卡片标题', type: 'text' },
        { field: 'subtitle', label: '副标题', type: 'text' },
        { field: 'amountLabel', label: '金额标签', type: 'text' },
        { field: 'maxAmount', label: '最大金额', type: 'text' },
        { field: 'amountUnit', label: '金额单位', type: 'text' },
        { field: 'features', label: '特色功能', type: 'tags' },
        { field: 'buttonText', label: '按钮文字', type: 'text' },
        { field: 'buttonColor', label: '按钮颜色', type: 'color' },
        { field: 'buttonTextColor', label: '按钮文字颜色', type: 'color' },
        { field: 'backgroundColor', label: '背景颜色', type: 'color' },
        { field: 'borderRadius', label: '圆角', type: 'number', min: 0, max: 20 },
        { field: 'showShadow', label: '显示阴影', type: 'switch' },
        { field: 'applyUrl', label: '申请页面链接', type: 'text' }
    ]
};

// 客户信息展示卡片
const CustomerInfoCard = {
    name: 'customer-info-card',
    type: 'business',
    template: `
        <view class="customer-info-card" :style="cardStyle">
            <view class="info-header">
                <image 
                    class="avatar"
                    :src="config.showAvatar ? (customerData.avatar || '/static/images/default-avatar.png') : ''"
                    v-if="config.showAvatar"
                />
                <view class="basic-info">
                    <text class="customer-name">{{ customerData.name || '客户姓名' }}</text>
                    <text class="customer-phone">{{ formatPhone(customerData.phone) || '手机号码' }}</text>
                </view>
            </view>
            <view class="info-content">
                <view 
                    v-for="field in config.displayFields" 
                    :key="field.key"
                    class="info-row"
                >
                    <text class="field-label">{{ field.label }}：</text>
                    <text class="field-value">{{ customerData[field.key] || '--' }}</text>
                </view>
            </view>
            <view class="info-actions" v-if="config.showActions">
                <button 
                    v-for="action in config.actions"
                    :key="action.key"
                    class="action-button"
                    :style="getActionButtonStyle(action)"
                    @click="handleAction(action)"
                >
                    {{ action.label }}
                </button>
            </view>
        </view>
    `,
    props: ['config', 'customerData'],
    methods: {
        formatPhone(phone) {
            if (!phone) return '';
            return phone.replace(/(\d{3})(\d{4})(\d{4})/, '$1****$3');
        },
        getActionButtonStyle(action) {
            return {
                backgroundColor: action.color || '#1890ff',
                color: action.textColor || '#ffffff'
            }
        },
        handleAction(action) {
            switch(action.type) {
                case 'call':
                    uni.makePhoneCall({ phoneNumber: this.customerData.phone });
                    break;
                case 'message':
                    // 发送短信逻辑
                    break;
                case 'navigate':
                    uni.navigateTo({ url: action.url });
                    break;
            }
        }
    },
    defaultConfig: {
        showAvatar: true,
        showActions: true,
        displayFields: [
            { key: 'id_card', label: '身份证号' },
            { key: 'address', label: '地址' },
            { key: 'industry', label: '行业' },
            { key: 'monthly_income', label: '月收入' }
        ],
        actions: [
            { key: 'call', label: '拨打电话', type: 'call', color: '#52c41a', textColor: '#ffffff' },
            { key: 'message', label: '发送短信', type: 'message', color: '#1890ff', textColor: '#ffffff' },
            { key: 'detail', label: '查看详情', type: 'navigate', url: '/pages/customer/detail', color: '#fa8c16', textColor: '#ffffff' }
        ],
        backgroundColor: '#ffffff',
        borderRadius: 8,
        showShadow: true,
        margin: '16px',
        padding: '20px'
    },
    configSchema: [
        { field: 'showAvatar', label: '显示头像', type: 'switch' },
        { field: 'showActions', label: '显示操作按钮', type: 'switch' },
        { field: 'displayFields', label: '显示字段', type: 'json' },
        { field: 'actions', label: '操作按钮', type: 'json', showIf: 'showActions=true' },
        { field: 'backgroundColor', label: '背景颜色', type: 'color' },
        { field: 'borderRadius', label: '圆角', type: 'number', min: 0, max: 20 },
        { field: 'showShadow', label: '显示阴影', type: 'switch' }
    ]
};

// 轮播图组件
const SwiperComponent = {
    name: 'swiper-component',
    type: 'business',
    template: `
        <view class="swiper-component">
            <swiper 
                :autoplay="config.autoplay"
                :interval="config.interval"
                :duration="config.duration"
                :circular="config.circular"
                :indicator-dots="config.showDots"
                :indicator-color="config.dotColor"
                :indicator-active-color="config.activeDotColor"
                :style="swiperStyle"
            >
                <swiper-item 
                    v-for="(item, index) in config.items" 
                    :key="index"
                >
                    <image 
                        :src="item.image"
                        :mode="config.imageMode"
                        class="swiper-image"
                        @click="handleItemClick(item)"
                    />
                    <view v-if="config.showTitle && item.title" class="swiper-title">
                        {{ item.title }}
                    </view>
                </swiper-item>
            </swiper>
        </view>
    `,
    props: ['config'],
    computed: {
        swiperStyle() {
            return {
                height: this.config.height + 'px' || '200px',
                borderRadius: this.config.borderRadius + 'px' || '0px'
            }
        }
    },
    methods: {
        handleItemClick(item) {
            if (item.link) {
                if (item.linkType === 'internal') {
                    uni.navigateTo({ url: item.link });
                } else {
                    uni.navigateTo({ url: '/pages/webview/index?url=' + encodeURIComponent(item.link) });
                }
            }
        }
    },
    defaultConfig: {
        autoplay: true,
        interval: 3000,
        duration: 500,
        circular: true,
        showDots: true,
        showTitle: false,
        dotColor: 'rgba(255,255,255,0.5)',
        activeDotColor: '#ffffff',
        height: 200,
        borderRadius: 0,
        imageMode: 'aspectFill',
        items: [
            {
                image: '/static/images/banner1.jpg',
                title: '轮播图1',
                link: '',
                linkType: 'internal'
            },
            {
                image: '/static/images/banner2.jpg',
                title: '轮播图2',
                link: '',
                linkType: 'internal'
            }
        ]
    },
    configSchema: [
        { field: 'autoplay', label: '自动播放', type: 'switch' },
        { field: 'interval', label: '切换间隔(毫秒)', type: 'number', min: 1000, max: 10000, showIf: 'autoplay=true' },
        { field: 'duration', label: '切换时长(毫秒)', type: 'number', min: 200, max: 2000 },
        { field: 'circular', label: '循环播放', type: 'switch' },
        { field: 'showDots', label: '显示指示器', type: 'switch' },
        { field: 'showTitle', label: '显示标题', type: 'switch' },
        { field: 'dotColor', label: '指示器颜色', type: 'color', showIf: 'showDots=true' },
        { field: 'activeDotColor', label: '活动指示器颜色', type: 'color', showIf: 'showDots=true' },
        { field: 'height', label: '轮播图高度', type: 'number', min: 100, max: 500 },
        { field: 'borderRadius', label: '圆角', type: 'number', min: 0, max: 20 },
        { field: 'imageMode', label: '图片模式', type: 'select', options: [
            { label: '缩放填充', value: 'aspectFill' },
            { label: '缩放适应', value: 'aspectFit' },
            { label: '拉伸填充', value: 'scaleToFill' }
        ]},
        { field: 'items', label: '轮播项配置', type: 'json' }
    ]
};
```

### 4. 组件配置表单

```javascript
// 动态组件配置表单
const ComponentConfigForm = {
    name: 'component-config-form',
    template: `
        <el-form :model="formData" label-width="100px">
            <el-form-item 
                v-for="schema in component.configSchema" 
                :key="schema.field"
                :label="schema.label"
                v-show="shouldShowField(schema)"
            >
                <!-- 文本输入 -->
                <el-input 
                    v-if="schema.type === 'text' || schema.type === 'textarea'"
                    v-model="formData[schema.field]"
                    :type="schema.type"
                    :placeholder="schema.placeholder"
                    @input="handleChange"
                />
                
                <!-- 数字输入 -->
                <el-input-number 
                    v-else-if="schema.type === 'number'"
                    v-model="formData[schema.field]"
                    :min="schema.min"
                    :max="schema.max"
                    :step="schema.step || 1"
                    @change="handleChange"
                />
                
                <!-- 颜色选择 -->
                <el-color-picker 
                    v-else-if="schema.type === 'color'"
                    v-model="formData[schema.field]"
                    @change="handleChange"
                />
                
                <!-- 开关 -->
                <el-switch 
                    v-else-if="schema.type === 'switch'"
                    v-model="formData[schema.field]"
                    @change="handleChange"
                />
                
                <!-- 下拉选择 -->
                <el-select 
                    v-else-if="schema.type === 'select'"
                    v-model="formData[schema.field]"
                    @change="handleChange"
                >
                    <el-option 
                        v-for="option in schema.options"
                        :key="option.value"
                        :label="option.label"
                        :value="option.value"
                    />
                </el-select>
                
                <!-- 文件上传 -->
                <el-upload
                    v-else-if="schema.type === 'upload'"
                    :action="uploadUrl"
                    :show-file-list="false"
                    :on-success="(response) => handleUploadSuccess(response, schema.field)"
                >
                    <el-button size="small" type="primary">上传文件</el-button>
                    <div v-if="formData[schema.field]" class="upload-preview">
                        <img :src="formData[schema.field]" style="width: 100px; height: 60px; object-fit: cover;">
                    </div>
                </el-upload>
                
                <!-- JSON编辑器 -->
                <el-input 
                    v-else-if="schema.type === 'json'"
                    v-model="jsonFields[schema.field]"
                    type="textarea"
                    :rows="4"
                    @blur="handleJsonChange(schema.field)"
                />
                
                <!-- 标签输入 -->
                <div v-else-if="schema.type === 'tags'">
                    <el-tag
                        v-for="(tag, index) in formData[schema.field]"
                        :key="index"
                        closable
                        @close="removeTag(schema.field, index)"
                    >
                        {{ tag }}
                    </el-tag>
                    <el-input
                        v-if="tagInputVisible[schema.field]"
                        ref="tagInput"
                        v-model="tagInputValue[schema.field]"
                        size="mini"
                        style="width: 80px; margin-left: 10px;"
                        @keyup.enter.native="addTag(schema.field)"
                        @blur="addTag(schema.field)"
                    />
                    <el-button 
                        v-else
                        size="small"
                        @click="showTagInput(schema.field)"
                    >
                        + 添加标签
                    </el-button>
                </div>
            </el-form-item>
        </el-form>
    `,
    props: ['component'],
    data() {
        return {
            formData: {},
            jsonFields: {},
            tagInputVisible: {},
            tagInputValue: {},
            uploadUrl: '/admin/upload/image'
        }
    },
    watch: {
        component: {
            handler(newComponent) {
                if (newComponent) {
                    this.formData = JSON.parse(JSON.stringify(newComponent.config || {}));
                    this.initJsonFields();
                }
            },
            immediate: true,
            deep: true
        }
    },
    methods: {
        shouldShowField(schema) {
            if (!schema.showIf) return true;
            
            const [field, condition] = schema.showIf.split('=');
            return this.formData[field] == condition;
        },
        
        handleChange() {
            this.$emit('change', this.formData);
        },
        
        handleUploadSuccess(response, field) {
            if (response.code === 200) {
                this.formData[field] = response.data.url;
                this.handleChange();
            }
        },
        
        initJsonFields() {
            this.component.configSchema.forEach(schema => {
                if (schema.type === 'json') {
                    this.jsonFields[schema.field] = JSON.stringify(this.formData[schema.field] || [], null, 2);
                }
            });
        },
        
        handleJsonChange(field) {
            try {
                this.formData[field] = JSON.parse(this.jsonFields[field]);
                this.handleChange();
            } catch (e) {
                this.$message.error('JSON格式错误');
            }
        },
        
        showTagInput(field) {
            this.$set(this.tagInputVisible, field, true);
            this.$nextTick(() => {
                this.$refs.tagInput.focus();
            });
        },
        
        addTag(field) {
            const value = this.tagInputValue[field];
            if (value && !this.formData[field].includes(value)) {
                this.formData[field].push(value);
                this.handleChange();
            }
            this.tagInputVisible[field] = false;
            this.tagInputValue[field] = '';
        },
        
        removeTag(field, index) {
            this.formData[field].splice(index, 1);
            this.handleChange();
        }
    }
};
```

## 二、组件数据库初始化

```sql
-- 插入基础组件
INSERT INTO component_library (component_name, component_type, component_code, default_config, config_schema, description, supported_platforms) VALUES
('text-component', 'basic', 'TextComponent', '{"content":"请输入文本内容","color":"#333333","fontSize":14}', '[{"field":"content","label":"文本内容","type":"textarea"}]', '文本组件', '["mp-weixin","mp-alipay","h5"]'),
('image-component', 'basic', 'ImageComponent', '{"src":"/static/images/placeholder.png","width":200,"height":150}', '[{"field":"src","label":"图片地址","type":"upload"}]', '图片组件', '["mp-weixin","mp-alipay","h5"]'),
('button-component', 'basic', 'ButtonComponent', '{"text":"按钮文字","backgroundColor":"#1890ff","textColor":"#ffffff"}', '[{"field":"text","label":"按钮文字","type":"text"}]', '按钮组件', '["mp-weixin","mp-alipay","h5"]'),
('input-component', 'form', 'InputComponent', '{"label":"输入框","placeholder":"请输入内容","required":false}', '[{"field":"label","label":"标签文字","type":"text"}]', '输入框组件', '["mp-weixin","mp-alipay","h5"]'),
('picker-component', 'form', 'PickerComponent', '{"label":"选择器","mode":"selector","options":[]}', '[{"field":"label","label":"标签文字","type":"text"}]', '选择器组件', '["mp-weixin","mp-alipay","h5"]'),
('loan-apply-card', 'business', 'LoanApplyCard', '{"title":"快速借款","maxAmount":"50000","features":["实时审批","极速放款"]}', '[{"field":"title","label":"卡片标题","type":"text"}]', '贷款申请卡片', '["mp-weixin","mp-alipay","h5"]'),
('customer-info-card', 'business', 'CustomerInfoCard', '{"showAvatar":true,"showActions":true}', '[{"field":"showAvatar","label":"显示头像","type":"switch"}]', '客户信息卡片', '["mp-weixin","mp-alipay","h5"]'),
('swiper-component', 'business', 'SwiperComponent', '{"autoplay":true,"interval":3000,"height":200}', '[{"field":"autoplay","label":"自动播放","type":"switch"}]', '轮播图组件', '["mp-weixin","mp-alipay","h5"]');
```
