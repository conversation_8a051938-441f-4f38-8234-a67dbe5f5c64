[unix_http_server]
file=/var/run/supervisor.sock
chmod=0700

[supervisord]
logfile=/var/log/supervisor/supervisord.log
pidfile=/var/run/supervisord.pid
childlogdir=/var/log/supervisor
nodaemon=true
user=root

[rpcinterface:supervisor]
supervisor.rpcinterface_factory = supervisor.rpcinterface:make_main_rpcinterface

[supervisorctl]
serverurl=unix:///var/run/supervisor.sock

[program:nginx]
command=nginx -g "daemon off;"
autostart=true
autorestart=true
priority=10
stdout_logfile=/var/log/supervisor/nginx.log
stderr_logfile=/var/log/supervisor/nginx_error.log
stdout_logfile_maxbytes=0
stderr_logfile_maxbytes=0

[program:php-fpm]
command=php-fpm -F
autostart=true
autorestart=true
priority=5
stdout_logfile=/var/log/supervisor/php-fpm.log
stderr_logfile=/var/log/supervisor/php-fpm_error.log
stdout_logfile_maxbytes=0
stderr_logfile_maxbytes=0

[program:queue-worker]
command=php /var/www/html/think queue:work --daemon
directory=/var/www/html
autostart=true
autorestart=true
priority=15
numprocs=2
process_name=%(program_name)s_%(process_num)02d
stdout_logfile=/var/log/supervisor/queue-worker.log
stderr_logfile=/var/log/supervisor/queue-worker_error.log
stdout_logfile_maxbytes=50MB
stderr_logfile_maxbytes=50MB
stdout_logfile_backups=10
stderr_logfile_backups=10
user=www
killasgroup=true
stopasgroup=true
stopsignal=TERM

[program:schedule]
command=php /var/www/html/think schedule:run
directory=/var/www/html
autostart=true
autorestart=true
priority=20
stdout_logfile=/var/log/supervisor/schedule.log
stderr_logfile=/var/log/supervisor/schedule_error.log
stdout_logfile_maxbytes=50MB
stderr_logfile_maxbytes=50MB
stdout_logfile_backups=10
stderr_logfile_backups=10
user=www

[program:log-monitor]
command=php /var/www/html/think log:monitor
directory=/var/www/html
autostart=true
autorestart=true
priority=25
stdout_logfile=/var/log/supervisor/log-monitor.log
stderr_logfile=/var/log/supervisor/log-monitor_error.log
stdout_logfile_maxbytes=50MB
stderr_logfile_maxbytes=50MB
stdout_logfile_backups=5
stderr_logfile_backups=5
user=www

[group:workers]
programs=queue-worker,schedule,log-monitor
priority=999
