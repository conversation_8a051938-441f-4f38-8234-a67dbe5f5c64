// 统一设计系统配置
// 用于所有前端应用的一致性设计

// ==================== 颜色系统 ====================
:root {
  // 主色调
  --primary-color: #1890ff;
  --primary-light: #40a9ff;
  --primary-dark: #096dd9;
  --primary-hover: #40a9ff;
  --primary-active: #096dd9;
  
  // 辅助色
  --success-color: #52c41a;
  --warning-color: #faad14;
  --error-color: #ff4d4f;
  --info-color: #1890ff;
  
  // 中性色
  --text-primary: #262626;
  --text-secondary: #595959;
  --text-tertiary: #8c8c8c;
  --text-disabled: #bfbfbf;
  
  // 背景色
  --bg-primary: #ffffff;
  --bg-secondary: #fafafa;
  --bg-tertiary: #f5f5f5;
  --bg-disabled: #f5f5f5;
  
  // 边框色
  --border-primary: #d9d9d9;
  --border-secondary: #f0f0f0;
  --border-light: #f5f5f5;
  
  // 阴影
  --shadow-light: 0 2px 8px rgba(0, 0, 0, 0.06);
  --shadow-medium: 0 4px 12px rgba(0, 0, 0, 0.1);
  --shadow-heavy: 0 8px 24px rgba(0, 0, 0, 0.15);
}

// ==================== 字体系统 ====================
:root {
  // 字体大小
  --font-size-xs: 12px;
  --font-size-sm: 14px;
  --font-size-base: 16px;
  --font-size-lg: 18px;
  --font-size-xl: 20px;
  --font-size-2xl: 24px;
  --font-size-3xl: 32px;
  
  // 行高
  --line-height-tight: 1.2;
  --line-height-base: 1.5;
  --line-height-loose: 1.8;
  
  // 字重
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  
  // 字体族
  --font-family-base: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  --font-family-mono: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;
}

// ==================== 间距系统 ====================
:root {
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-base: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  --spacing-2xl: 48px;
  --spacing-3xl: 64px;
}

// ==================== 圆角系统 ====================
:root {
  --border-radius-sm: 4px;
  --border-radius-base: 6px;
  --border-radius-lg: 8px;
  --border-radius-xl: 12px;
  --border-radius-2xl: 16px;
  --border-radius-full: 50%;
}

// ==================== 动画系统 ====================
:root {
  --transition-fast: 0.15s ease-in-out;
  --transition-base: 0.3s ease-in-out;
  --transition-slow: 0.5s ease-in-out;
  
  --easing-ease-in: cubic-bezier(0.4, 0, 1, 1);
  --easing-ease-out: cubic-bezier(0, 0, 0.2, 1);
  --easing-ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
}

// ==================== 响应式断点 ====================
$breakpoints: (
  xs: 480px,
  sm: 768px,
  md: 1024px,
  lg: 1280px,
  xl: 1536px
);

// 响应式混入
@mixin respond-to($breakpoint) {
  @if map-has-key($breakpoints, $breakpoint) {
    @media (min-width: map-get($breakpoints, $breakpoint)) {
      @content;
    }
  }
}

// ==================== 通用组件样式 ====================

// 按钮样式
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-sm) var(--spacing-base);
  border: 1px solid transparent;
  border-radius: var(--border-radius-base);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  line-height: var(--line-height-tight);
  text-decoration: none;
  cursor: pointer;
  transition: all var(--transition-fast);
  user-select: none;
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
  
  // 主要按钮
  &--primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
    
    &:hover:not(:disabled) {
      background-color: var(--primary-hover);
      border-color: var(--primary-hover);
    }
    
    &:active:not(:disabled) {
      background-color: var(--primary-active);
      border-color: var(--primary-active);
    }
  }
  
  // 次要按钮
  &--secondary {
    background-color: transparent;
    border-color: var(--border-primary);
    color: var(--text-primary);
    
    &:hover:not(:disabled) {
      border-color: var(--primary-color);
      color: var(--primary-color);
    }
  }
  
  // 危险按钮
  &--danger {
    background-color: var(--error-color);
    border-color: var(--error-color);
    color: white;
    
    &:hover:not(:disabled) {
      opacity: 0.8;
    }
  }
  
  // 按钮尺寸
  &--small {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: var(--font-size-sm);
  }
  
  &--large {
    padding: var(--spacing-base) var(--spacing-lg);
    font-size: var(--font-size-lg);
  }
}

// 卡片样式
.card {
  background-color: var(--bg-primary);
  border: 1px solid var(--border-secondary);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-light);
  overflow: hidden;
  
  &__header {
    padding: var(--spacing-base) var(--spacing-lg);
    border-bottom: 1px solid var(--border-secondary);
    background-color: var(--bg-secondary);
  }
  
  &__body {
    padding: var(--spacing-lg);
  }
  
  &__footer {
    padding: var(--spacing-base) var(--spacing-lg);
    border-top: 1px solid var(--border-secondary);
    background-color: var(--bg-secondary);
  }
}

// 表单样式
.form {
  &__group {
    margin-bottom: var(--spacing-base);
  }
  
  &__label {
    display: block;
    margin-bottom: var(--spacing-xs);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    color: var(--text-primary);
  }
  
  &__input {
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-base);
    border: 1px solid var(--border-primary);
    border-radius: var(--border-radius-base);
    font-size: var(--font-size-base);
    line-height: var(--line-height-base);
    transition: border-color var(--transition-fast);
    
    &:focus {
      outline: none;
      border-color: var(--primary-color);
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
    }
    
    &:disabled {
      background-color: var(--bg-disabled);
      color: var(--text-disabled);
      cursor: not-allowed;
    }
    
    &--error {
      border-color: var(--error-color);
      
      &:focus {
        box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.2);
      }
    }
  }
  
  &__error {
    margin-top: var(--spacing-xs);
    font-size: var(--font-size-xs);
    color: var(--error-color);
  }
}

// 状态样式
.status {
  display: inline-flex;
  align-items: center;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius-base);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  
  &--success {
    background-color: rgba(82, 196, 26, 0.1);
    color: var(--success-color);
  }
  
  &--warning {
    background-color: rgba(250, 173, 20, 0.1);
    color: var(--warning-color);
  }
  
  &--error {
    background-color: rgba(255, 77, 79, 0.1);
    color: var(--error-color);
  }
  
  &--info {
    background-color: rgba(24, 144, 255, 0.1);
    color: var(--info-color);
  }
}

// 工具类
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.flex { display: flex; }
.flex-col { flex-direction: column; }
.items-center { align-items: center; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }

.w-full { width: 100%; }
.h-full { height: 100%; }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: var(--spacing-xs); }
.mb-2 { margin-bottom: var(--spacing-sm); }
.mb-3 { margin-bottom: var(--spacing-base); }
.mb-4 { margin-bottom: var(--spacing-lg); }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: var(--spacing-xs); }
.mt-2 { margin-top: var(--spacing-sm); }
.mt-3 { margin-top: var(--spacing-base); }
.mt-4 { margin-top: var(--spacing-lg); }

// 动画类
.fade-enter-active,
.fade-leave-active {
  transition: opacity var(--transition-base);
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-enter-active,
.slide-leave-active {
  transition: transform var(--transition-base);
}

.slide-enter-from {
  transform: translateX(-100%);
}

.slide-leave-to {
  transform: translateX(100%);
}
