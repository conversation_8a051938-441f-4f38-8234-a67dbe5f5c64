<template>
  <div class="customer-management">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>客户管理</span>
          <el-button type="primary" @click="showCreateDialog">新增客户</el-button>
        </div>
      </template>
      
      <!-- 搜索区域 -->
      <div class="search-area">
        <el-form :inline="true" :model="searchForm">
          <el-form-item label="关键词">
            <el-input
              v-model="searchForm.keyword"
              placeholder="姓名、手机号、身份证号"
              clearable
              style="width: 200px"
            />
          </el-form-item>
          
          <el-form-item label="状态">
            <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
              <el-option label="正常" value="active"/>
              <el-option label="黑名单" value="blacklist"/>
              <el-option label="暂停" value="suspended"/>
            </el-select>
          </el-form-item>
          
          <el-form-item>
            <el-button type="primary" @click="handleSearch">搜索</el-button>
            <el-button @click="resetSearch">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
      
      <!-- 表格 -->
      <el-table
        v-loading="tableLoading"
        :data="tableData"
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55"/>
        
        <el-table-column prop="name" label="姓名" width="120"/>
        
        <el-table-column prop="phone" label="手机号" width="130">
          <template #default="scope">
            {{ maskPhone(scope.row.phone) }}
          </template>
        </el-table-column>
        
        <el-table-column prop="id_card" label="身份证号" width="150">
          <template #default="scope">
            {{ maskIdCard(scope.row.id_card) }}
          </template>
        </el-table-column>
        
        <el-table-column prop="age" label="年龄" width="80"/>
        
        <el-table-column prop="gender" label="性别" width="80">
          <template #default="scope">
            {{ getGenderText(scope.row.gender) }}
          </template>
        </el-table-column>
        
        <el-table-column prop="industry" label="行业" width="120"/>
        
        <el-table-column prop="monthly_income" label="月收入" width="120">
          <template #default="scope">
            <span v-if="scope.row.monthly_income">
              ¥{{ formatMoney(scope.row.monthly_income) }}
            </span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="risk_score" label="风险评分" width="100">
          <template #default="scope">
            <el-tag :type="getRiskScoreTagType(scope.row.risk_score)">
              {{ scope.row.risk_score }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="active_loans_count" label="当前借款" width="100"/>
        
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="getStatusTagType(scope.row.status)">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="created_at" label="创建时间" width="180">
          <template #default="scope">
            {{ formatDateTime(scope.row.created_at) }}
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <el-button
              type="primary"
              size="small"
              @click="viewDetail(scope.row)"
            >
              详情
            </el-button>
            <el-button
              type="warning"
              size="small"
              @click="editCustomer(scope.row)"
            >
              编辑
            </el-button>
            <el-button
              type="danger"
              size="small"
              @click="deleteCustomer(scope.row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <el-pagination
        v-model:current-page="pagination.page"
        v-model:page-size="pagination.limit"
        :page-sizes="[10, 20, 50, 100]"
        :total="pagination.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-card>
    
    <!-- 新增/编辑客户对话框 -->
    <el-dialog
      v-model="formDialogVisible"
      :title="formMode === 'create' ? '新增客户' : '编辑客户'"
      width="800px"
      @close="resetForm"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="formRules"
        label-width="120px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="姓名" prop="name">
              <el-input v-model="form.name" placeholder="请输入姓名"/>
            </el-form-item>
          </el-col>
          
          <el-col :span="12">
            <el-form-item label="手机号" prop="phone">
              <el-input v-model="form.phone" placeholder="请输入手机号"/>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="身份证号" prop="id_card">
              <el-input v-model="form.id_card" placeholder="请输入身份证号"/>
            </el-form-item>
          </el-col>
          
          <el-col :span="12">
            <el-form-item label="行业">
              <el-input v-model="form.industry" placeholder="请输入行业"/>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="月收入">
              <el-input-number
                v-model="form.monthly_income"
                :min="0"
                :precision="2"
                placeholder="请输入月收入"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          
          <el-col :span="12">
            <el-form-item label="状态">
              <el-select v-model="form.status" placeholder="请选择状态">
                <el-option label="正常" value="active"/>
                <el-option label="黑名单" value="blacklist"/>
                <el-option label="暂停" value="suspended"/>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item label="详细地址">
          <el-input
            v-model="form.address"
            type="textarea"
            :rows="3"
            placeholder="请输入详细地址"
          />
        </el-form-item>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="紧急联系人">
              <el-input v-model="form.emergency_contact" placeholder="请输入紧急联系人"/>
            </el-form-item>
          </el-col>
          
          <el-col :span="12">
            <el-form-item label="紧急联系电话">
              <el-input v-model="form.emergency_phone" placeholder="请输入紧急联系电话"/>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      
      <template #footer>
        <el-button @click="formDialogVisible = false">取消</el-button>
        <el-button type="primary" :loading="formLoading" @click="handleSubmit">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { customerAPI } from '../../api'

export default {
  name: 'CustomerManagement',
  setup() {
    const tableLoading = ref(false)
    const formLoading = ref(false)
    const formDialogVisible = ref(false)
    const formRef = ref()
    const formMode = ref('create') // create | edit
    
    const tableData = ref([])
    const selectedRows = ref([])
    
    const pagination = reactive({
      page: 1,
      limit: 20,
      total: 0
    })
    
    const searchForm = reactive({
      keyword: '',
      status: ''
    })
    
    const form = reactive({
      id: null,
      name: '',
      phone: '',
      id_card: '',
      industry: '',
      monthly_income: 0,
      address: '',
      emergency_contact: '',
      emergency_phone: '',
      status: 'active'
    })
    
    const formRules = {
      name: [
        { required: true, message: '请输入姓名', trigger: 'blur' },
        { min: 2, max: 20, message: '姓名长度在 2 到 20 个字符', trigger: 'blur' }
      ],
      phone: [
        { required: true, message: '请输入手机号', trigger: 'blur' },
        { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
      ],
      id_card: [
        { required: true, message: '请输入身份证号', trigger: 'blur' },
        { pattern: /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/, message: '请输入正确的身份证号', trigger: 'blur' }
      ]
    }
    
    // 计算属性
    const searchParams = computed(() => {
      return {
        page: pagination.page,
        limit: pagination.limit,
        ...searchForm
      }
    })
    
    // 获取客户列表
    const getTableData = async () => {
      try {
        tableLoading.value = true
        const data = await customerAPI.getCustomers(searchParams.value)
        
        tableData.value = data.items
        pagination.total = data.pagination.total
        
      } catch (error) {
        console.error('获取客户列表失败:', error)
      } finally {
        tableLoading.value = false
      }
    }
    
    // 显示创建对话框
    const showCreateDialog = () => {
      formMode.value = 'create'
      formDialogVisible.value = true
    }
    
    // 编辑客户
    const editCustomer = async (row) => {
      try {
        const customer = await customerAPI.getCustomer(row.id)
        Object.assign(form, customer)
        formMode.value = 'edit'
        formDialogVisible.value = true
      } catch (error) {
        console.error('获取客户详情失败:', error)
      }
    }
    
    // 提交表单
    const handleSubmit = async () => {
      try {
        await formRef.value.validate()
        formLoading.value = true
        
        if (formMode.value === 'create') {
          await customerAPI.createCustomer(form)
          ElMessage.success('客户创建成功')
        } else {
          await customerAPI.updateCustomer(form.id, form)
          ElMessage.success('客户更新成功')
        }
        
        formDialogVisible.value = false
        getTableData()
        
      } catch (error) {
        console.error('提交表单失败:', error)
      } finally {
        formLoading.value = false
      }
    }
    
    // 删除客户
    const deleteCustomer = async (row) => {
      try {
        await ElMessageBox.confirm(`确定要删除客户 ${row.name} 吗？`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        
        await customerAPI.deleteCustomer(row.id)
        ElMessage.success('客户删除成功')
        getTableData()
        
      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除客户失败:', error)
        }
      }
    }
    
    // 重置表单
    const resetForm = () => {
      Object.assign(form, {
        id: null,
        name: '',
        phone: '',
        id_card: '',
        industry: '',
        monthly_income: 0,
        address: '',
        emergency_contact: '',
        emergency_phone: '',
        status: 'active'
      })
    }
    
    // 搜索
    const handleSearch = () => {
      pagination.page = 1
      getTableData()
    }
    
    // 重置搜索
    const resetSearch = () => {
      Object.assign(searchForm, {
        keyword: '',
        status: ''
      })
      pagination.page = 1
      getTableData()
    }
    
    // 分页处理
    const handleSizeChange = (size) => {
      pagination.limit = size
      pagination.page = 1
      getTableData()
    }
    
    const handleCurrentChange = (page) => {
      pagination.page = page
      getTableData()
    }
    
    // 选择处理
    const handleSelectionChange = (selection) => {
      selectedRows.value = selection
    }
    
    // 查看详情
    const viewDetail = () => {
      ElMessage.info('详情功能开发中')
    }
    
    // 工具函数
    const formatMoney = (amount) => {
      return (amount || 0).toLocaleString()
    }
    
    const formatDateTime = (datetime) => {
      if (!datetime) return ''
      return new Date(datetime).toLocaleString()
    }
    
    const maskPhone = (phone) => {
      if (!phone || phone.length !== 11) return phone
      return `${phone.substr(0, 3)}****${phone.substr(7)}`
    }
    
    const maskIdCard = (idCard) => {
      if (!idCard || idCard.length !== 18) return idCard
      return `${idCard.substr(0, 6)}****${idCard.substr(-4)}`
    }
    
    const getGenderText = (gender) => {
      const genders = {
        male: '男',
        female: '女'
      }
      return genders[gender] || '-'
    }
    
    const getRiskScoreTagType = (score) => {
      if (score >= 80) return 'success'
      if (score >= 60) return 'warning'
      return 'danger'
    }
    
    const getStatusText = (status) => {
      const statuses = {
        active: '正常',
        blacklist: '黑名单',
        suspended: '暂停'
      }
      return statuses[status] || status
    }
    
    const getStatusTagType = (status) => {
      const types = {
        active: 'success',
        blacklist: 'danger',
        suspended: 'warning'
      }
      return types[status] || ''
    }
    
    onMounted(() => {
      getTableData()
    })
    
    return {
      // 响应式数据
      tableLoading,
      formLoading,
      formDialogVisible,
      formRef,
      formMode,
      tableData,
      selectedRows,
      pagination,
      searchForm,
      form,
      formRules,
      
      // 方法
      getTableData,
      showCreateDialog,
      editCustomer,
      handleSubmit,
      deleteCustomer,
      resetForm,
      handleSearch,
      resetSearch,
      handleSizeChange,
      handleCurrentChange,
      handleSelectionChange,
      viewDetail,
      formatMoney,
      formatDateTime,
      maskPhone,
      maskIdCard,
      getGenderText,
      getRiskScoreTagType,
      getStatusText,
      getStatusTagType
    }
  }
}
</script>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-area {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.el-pagination {
  margin-top: 20px;
  text-align: right;
}
</style>
