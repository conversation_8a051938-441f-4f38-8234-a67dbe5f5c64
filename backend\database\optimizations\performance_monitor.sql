-- 数据库性能监控查询脚本
-- 用于监控和分析数据库性能

USE iapp;

-- ==================== 基础性能监控 ====================

-- 1. 查看数据库大小和表大小
SELECT 
    'Database Size Analysis' as analysis_type,
    table_schema as database_name,
    table_name,
    ROUND(((data_length + index_length) / 1024 / 1024), 2) as size_mb,
    ROUND((data_length / 1024 / 1024), 2) as data_size_mb,
    ROUND((index_length / 1024 / 1024), 2) as index_size_mb,
    table_rows as estimated_rows
FROM information_schema.tables 
WHERE table_schema = 'iapp'
ORDER BY (data_length + index_length) DESC;

-- 2. 查看索引使用情况
SELECT 
    'Index Usage Analysis' as analysis_type,
    table_name,
    index_name,
    column_name,
    cardinality,
    CASE 
        WHEN cardinality = 0 THEN 'Unused or Low Cardinality'
        WHEN cardinality < 100 THEN 'Low Cardinality'
        WHEN cardinality < 1000 THEN 'Medium Cardinality'
        ELSE 'High Cardinality'
    END as cardinality_level
FROM information_schema.statistics 
WHERE table_schema = 'iapp'
ORDER BY table_name, cardinality DESC;

-- 3. 查看表的存储引擎和字符集
SELECT 
    'Table Configuration Analysis' as analysis_type,
    table_name,
    engine,
    table_collation,
    table_rows,
    avg_row_length,
    ROUND((data_length / 1024 / 1024), 2) as data_size_mb,
    create_time,
    update_time
FROM information_schema.tables 
WHERE table_schema = 'iapp'
ORDER BY table_name;

-- ==================== 查询性能分析 ====================

-- 4. 分析慢查询（需要开启慢查询日志）
-- SHOW VARIABLES LIKE 'slow_query_log%';
-- SHOW VARIABLES LIKE 'long_query_time';

-- 5. 查看当前运行的查询
SELECT 
    'Current Queries Analysis' as analysis_type,
    id,
    user,
    host,
    db,
    command,
    time,
    state,
    LEFT(info, 100) as query_preview
FROM information_schema.processlist 
WHERE command != 'Sleep' 
ORDER BY time DESC;

-- 6. 查看表锁定情况
SELECT 
    'Table Locks Analysis' as analysis_type,
    table_schema,
    table_name,
    table_locks_immediate,
    table_locks_waited,
    CASE 
        WHEN table_locks_immediate + table_locks_waited = 0 THEN 0
        ELSE ROUND((table_locks_waited / (table_locks_immediate + table_locks_waited)) * 100, 2)
    END as lock_wait_percentage
FROM information_schema.table_statistics 
WHERE table_schema = 'iapp'
ORDER BY lock_wait_percentage DESC;

-- ==================== 业务数据分析 ====================

-- 7. 客户数据分布分析
SELECT 
    'Customer Data Distribution' as analysis_type,
    'Total Customers' as metric,
    COUNT(*) as value,
    NULL as percentage
FROM customers
WHERE deleted_at IS NULL

UNION ALL

SELECT 
    'Customer Data Distribution' as analysis_type,
    CONCAT('Risk Level: ', risk_level) as metric,
    COUNT(*) as value,
    ROUND((COUNT(*) * 100.0 / (SELECT COUNT(*) FROM customers WHERE deleted_at IS NULL)), 2) as percentage
FROM customers 
WHERE deleted_at IS NULL
GROUP BY risk_level

UNION ALL

SELECT 
    'Customer Data Distribution' as analysis_type,
    CONCAT('Status: ', status) as metric,
    COUNT(*) as value,
    ROUND((COUNT(*) * 100.0 / (SELECT COUNT(*) FROM customers WHERE deleted_at IS NULL)), 2) as percentage
FROM customers 
WHERE deleted_at IS NULL
GROUP BY status

ORDER BY analysis_type, value DESC;

-- 8. 借款申请数据分析
SELECT 
    'Loan Application Analysis' as analysis_type,
    'Total Applications' as metric,
    COUNT(*) as value,
    NULL as avg_amount
FROM loan_applications
WHERE deleted_at IS NULL

UNION ALL

SELECT 
    'Loan Application Analysis' as analysis_type,
    CONCAT('Status: ', status) as metric,
    COUNT(*) as value,
    ROUND(AVG(amount), 2) as avg_amount
FROM loan_applications 
WHERE deleted_at IS NULL
GROUP BY status

ORDER BY analysis_type, value DESC;

-- 9. 代理商业绩分析
SELECT 
    'Agent Performance Analysis' as analysis_type,
    CONCAT('Level: ', level) as metric,
    COUNT(*) as agent_count,
    ROUND(AVG(total_customers), 2) as avg_customers,
    ROUND(AVG(total_amount), 2) as avg_total_amount,
    ROUND(AVG(commission_rate * 100), 4) as avg_commission_rate
FROM agents 
WHERE status = 1
GROUP BY level
ORDER BY avg_total_amount DESC;

-- ==================== 性能优化建议 ====================

-- 10. 检查缺失的索引（基于查询模式）
SELECT 
    'Missing Index Suggestions' as analysis_type,
    'customers' as table_name,
    'Consider adding composite index on (status, risk_level, agent_id)' as suggestion,
    'Frequently used in WHERE clauses together' as reason

UNION ALL

SELECT 
    'Missing Index Suggestions' as analysis_type,
    'loan_applications' as table_name,
    'Consider adding composite index on (customer_id, status, submit_time)' as suggestion,
    'Common query pattern for customer loan history' as reason

UNION ALL

SELECT 
    'Missing Index Suggestions' as analysis_type,
    'risk_assessments' as table_name,
    'Consider adding composite index on (customer_id, assessment_time)' as suggestion,
    'Frequently queried for customer risk history' as reason;

-- 11. 表维护建议
SELECT 
    'Table Maintenance Suggestions' as analysis_type,
    table_name,
    CASE 
        WHEN data_free > 0 THEN CONCAT('OPTIMIZE TABLE ', table_name, '; -- Reclaim ', ROUND(data_free/1024/1024, 2), 'MB free space')
        ELSE 'No optimization needed'
    END as maintenance_suggestion,
    ROUND(data_free/1024/1024, 2) as free_space_mb
FROM information_schema.tables 
WHERE table_schema = 'iapp' 
  AND data_free > 0
ORDER BY data_free DESC;

-- ==================== 实时监控查询 ====================

-- 12. 系统状态监控
SELECT 
    'System Status Monitor' as monitor_type,
    variable_name,
    variable_value
FROM information_schema.global_status 
WHERE variable_name IN (
    'Connections',
    'Max_used_connections',
    'Threads_connected',
    'Threads_running',
    'Queries',
    'Questions',
    'Slow_queries',
    'Opens',
    'Opened_tables',
    'Table_locks_immediate',
    'Table_locks_waited',
    'Key_read_requests',
    'Key_reads',
    'Key_write_requests',
    'Key_writes',
    'Innodb_buffer_pool_read_requests',
    'Innodb_buffer_pool_reads'
)
ORDER BY variable_name;

-- 13. InnoDB状态监控
SELECT 
    'InnoDB Status Monitor' as monitor_type,
    variable_name,
    variable_value
FROM information_schema.global_status 
WHERE variable_name LIKE 'Innodb%'
  AND variable_name IN (
    'Innodb_buffer_pool_size',
    'Innodb_buffer_pool_pages_total',
    'Innodb_buffer_pool_pages_free',
    'Innodb_buffer_pool_pages_data',
    'Innodb_buffer_pool_pages_dirty',
    'Innodb_buffer_pool_read_requests',
    'Innodb_buffer_pool_reads',
    'Innodb_data_reads',
    'Innodb_data_writes',
    'Innodb_data_read',
    'Innodb_data_written',
    'Innodb_rows_read',
    'Innodb_rows_inserted',
    'Innodb_rows_updated',
    'Innodb_rows_deleted'
)
ORDER BY variable_name;

-- ==================== 性能基准测试 ====================

-- 14. 查询性能测试
-- 测试客户查询性能
SET @start_time = NOW(6);
SELECT COUNT(*) FROM customers WHERE status = 'active' AND risk_level = 'low';
SET @end_time = NOW(6);
SELECT 
    'Query Performance Test' as test_type,
    'Customer Active Low Risk Count' as query_description,
    TIMESTAMPDIFF(MICROSECOND, @start_time, @end_time) / 1000 as execution_time_ms;

-- 测试复杂查询性能
SET @start_time = NOW(6);
SELECT 
    c.name,
    c.risk_level,
    COUNT(la.id) as application_count,
    SUM(la.amount) as total_amount
FROM customers c
LEFT JOIN loan_applications la ON c.id = la.customer_id
WHERE c.status = 'active'
GROUP BY c.id, c.name, c.risk_level
HAVING application_count > 0
ORDER BY total_amount DESC
LIMIT 10;
SET @end_time = NOW(6);
SELECT 
    'Query Performance Test' as test_type,
    'Complex Customer Loan Summary' as query_description,
    TIMESTAMPDIFF(MICROSECOND, @start_time, @end_time) / 1000 as execution_time_ms;

-- ==================== 清理和维护脚本 ====================

-- 15. 数据清理建议
SELECT 
    'Data Cleanup Suggestions' as suggestion_type,
    'operation_logs' as table_name,
    CONCAT('DELETE FROM operation_logs WHERE created_at < DATE_SUB(NOW(), INTERVAL 90 DAY);') as cleanup_sql,
    'Remove operation logs older than 90 days' as description

UNION ALL

SELECT 
    'Data Cleanup Suggestions' as suggestion_type,
    'performance_metrics' as table_name,
    CONCAT('DELETE FROM performance_metrics WHERE timestamp < DATE_SUB(NOW(), INTERVAL 30 DAY);') as cleanup_sql,
    'Remove performance metrics older than 30 days' as description;

-- 16. 定期维护任务
SELECT 
    'Maintenance Tasks' as task_type,
    'Daily' as frequency,
    'ANALYZE TABLE customers, loan_applications, agents;' as task_sql,
    'Update table statistics for query optimizer' as description

UNION ALL

SELECT 
    'Maintenance Tasks' as task_type,
    'Weekly' as frequency,
    'OPTIMIZE TABLE operation_logs, performance_metrics;' as task_sql,
    'Defragment tables and reclaim space' as description

UNION ALL

SELECT 
    'Maintenance Tasks' as task_type,
    'Monthly' as frequency,
    'CHECK TABLE customers, loan_applications, agents;' as task_sql,
    'Check table integrity' as description;

-- 监控完成提示
SELECT 'Database performance monitoring completed!' as message,
       NOW() as timestamp;
