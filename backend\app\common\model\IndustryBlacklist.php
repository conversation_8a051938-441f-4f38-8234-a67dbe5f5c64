<?php
declare (strict_types = 1);

namespace app\common\model;

use think\Model;

class IndustryBlacklist extends Model
{
    protected $name = 'industry_blacklist';

    protected $schema = [
        'id'            => 'int',
        'type'          => 'string',
        'value'         => 'string',
        'source'        => 'string',
        'reason'        => 'string',
        'risk_level'    => 'string',
        'effective_date'=> 'date',
        'expire_date'   => 'date',
        'is_active'     => 'int',
        'created_by'    => 'int',
        'created_at'    => 'datetime',
        'updated_at'    => 'datetime',
    ];
}


