<?php
/**
 * 安全配置文件
 * 民间空放贷后管理系统 v2.0
 */

return [
    // 基础安全设置
    'basic' => [
        // 是否启用安全模式
        'enabled' => true,
        
        // 调试模式下是否启用安全检查
        'debug_mode_security' => false,
        
        // 安全密钥
        'secret_key' => env('SECURITY_SECRET_KEY', 'iapp_security_key_2025'),
        
        // 加密算法
        'encryption_algorithm' => 'AES-256-CBC',
        
        // 哈希算法
        'hash_algorithm' => 'sha256',
    ],

    // XSS防护配置
    'xss' => [
        // 是否启用XSS防护
        'enabled' => true,
        
        // 是否清理输入数据
        'clean_input' => true,
        
        // 是否转义输出数据
        'escape_output' => true,
        
        // XSS检测模式
        'detection_patterns' => [
            '/<script[^>]*>.*?<\/script>/is',
            '/<iframe[^>]*>.*?<\/iframe>/is',
            '/javascript:/i',
            '/vbscript:/i',
            '/onload\s*=/i',
            '/onerror\s*=/i',
            '/onclick\s*=/i',
            '/onmouseover\s*=/i',
            '/onfocus\s*=/i',
            '/onblur\s*=/i',
        ],
    ],

    // CSRF防护配置
    'csrf' => [
        // 是否启用CSRF防护
        'enabled' => true,
        
        // CSRF令牌名称
        'token_name' => '_token',
        
        // CSRF令牌HTTP头名称
        'header_name' => 'X-CSRF-TOKEN',
        
        // 令牌有效期（秒）
        'token_lifetime' => 3600,
        
        // 是否对API接口启用CSRF检查
        'check_api' => false,
        
        // 排除的路径
        'exclude_paths' => [
            '/api/auth/login',
            '/api/webhook/*',
            '/api/callback/*',
        ],
    ],

    // SQL注入防护配置
    'sql_injection' => [
        // 是否启用SQL注入防护
        'enabled' => true,
        
        // 检测关键词
        'keywords' => [
            'union', 'select', 'insert', 'update', 'delete', 'drop', 'create', 'alter',
            'exec', 'execute', 'sp_', 'xp_', 'declare', 'cast', 'convert', 'char',
            'nchar', 'varchar', 'nvarchar', 'substring', 'ascii', 'len', 'count',
            'group by', 'order by', 'having', 'waitfor', 'delay'
        ],
        
        // 检测模式
        'patterns' => [
            '/(\s|^)(union|select|insert|update|delete|drop|create|alter)(\s|$)/i',
            '/(\s|^)(exec|execute|sp_|xp_)(\s|$)/i',
            '/(\s|^)(declare|cast|convert)(\s|$)/i',
            '/(\s|^)(char|nchar|varchar|nvarchar)(\s|$)/i',
            '/(\s|^)(substring|ascii|len|count)(\s|$)/i',
            '/(\s|^)(group\s+by|order\s+by|having)(\s|$)/i',
            '/(\s|^)(waitfor|delay)(\s|$)/i',
            '/(\s|^)(and|or)\s+\d+\s*=\s*\d+/i',
            '/(\s|^)(and|or)\s+[\'"].*[\'"](\s|$)/i',
        ],
    ],

    // 文件上传安全配置
    'file_upload' => [
        // 是否启用文件上传安全检查
        'enabled' => true,
        
        // 允许的文件扩展名
        'allowed_extensions' => [
            'jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp',
            'pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx',
            'txt', 'csv', 'zip', 'rar'
        ],
        
        // 禁止的文件扩展名
        'forbidden_extensions' => [
            'php', 'php3', 'php4', 'php5', 'phtml', 'asp', 'aspx', 'jsp',
            'exe', 'bat', 'cmd', 'com', 'scr', 'vbs', 'js', 'jar',
            'sh', 'py', 'pl', 'rb', 'go', 'c', 'cpp', 'h'
        ],
        
        // 最大文件大小（字节）
        'max_file_size' => 10 * 1024 * 1024, // 10MB
        
        // 允许的MIME类型
        'allowed_mime_types' => [
            'image/jpeg', 'image/png', 'image/gif', 'image/bmp', 'image/webp',
            'application/pdf', 'application/msword', 'application/vnd.ms-excel',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'text/plain', 'text/csv', 'application/zip', 'application/x-rar-compressed'
        ],
        
        // 文件内容检查
        'content_check' => [
            'enabled' => true,
            'scan_depth' => 1024, // 扫描文件前1024字节
            'malicious_patterns' => [
                '<?php', '<%', '<script', 'eval(', 'exec(', 'system(',
                'shell_exec(', 'passthru(', 'file_get_contents(', 'file_put_contents(',
                'fopen(', 'fwrite(', 'include(', 'require(', 'include_once(',
                'require_once('
            ]
        ],
    ],

    // 请求频率限制配置
    'rate_limiting' => [
        // 是否启用频率限制
        'enabled' => true,
        
        // 全局限制
        'global' => [
            'requests' => 1000,    // 请求数量
            'window' => 3600,      // 时间窗口（秒）
        ],
        
        // API接口限制
        'api' => [
            'requests' => 100,     // 请求数量
            'window' => 3600,      // 时间窗口（秒）
        ],
        
        // 登录接口限制
        'login' => [
            'requests' => 5,       // 请求数量
            'window' => 300,       // 时间窗口（秒）
        ],
        
        // 特定接口限制
        'endpoints' => [
            '/api/auth/login' => [
                'requests' => 5,
                'window' => 300,
            ],
            '/api/auth/register' => [
                'requests' => 3,
                'window' => 3600,
            ],
            '/api/password/reset' => [
                'requests' => 3,
                'window' => 3600,
            ],
        ],
    ],

    // IP过滤配置
    'ip_filtering' => [
        // 是否启用IP过滤
        'enabled' => true,
        
        // IP白名单
        'whitelist' => [
            // '127.0.0.1',
            // '***********/24',
        ],
        
        // IP黑名单
        'blacklist' => [
            // '*************',
            // '10.0.0.0/8',
        ],
        
        // 是否启用地理位置过滤
        'geo_filtering' => false,
        
        // 允许的国家代码
        'allowed_countries' => ['CN'],
        
        // 禁止的国家代码
        'blocked_countries' => [],
    ],

    // 安全头配置
    'headers' => [
        // 是否启用安全头
        'enabled' => true,
        
        // 安全头列表
        'list' => [
            'X-Frame-Options' => 'DENY',
            'X-Content-Type-Options' => 'nosniff',
            'X-XSS-Protection' => '1; mode=block',
            'Strict-Transport-Security' => 'max-age=31536000; includeSubDomains; preload',
            'Referrer-Policy' => 'strict-origin-when-cross-origin',
            'Permissions-Policy' => 'geolocation=(), microphone=(), camera=()',
            'Content-Security-Policy' => "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self'; frame-ancestors 'none'; base-uri 'self'; form-action 'self';",
        ],
        
        // 移除的头
        'remove' => [
            'Server',
            'X-Powered-By',
        ],
    ],

    // 会话安全配置
    'session' => [
        // 会话超时时间（秒）
        'timeout' => 3600,
        
        // 是否启用会话固定攻击防护
        'regenerate_id' => true,
        
        // 会话ID重新生成间隔（秒）
        'regenerate_interval' => 300,
        
        // 是否启用HttpOnly
        'http_only' => true,
        
        // 是否启用Secure（HTTPS）
        'secure' => false,
        
        // SameSite设置
        'same_site' => 'Lax',
    ],

    // 密码安全配置
    'password' => [
        // 最小长度
        'min_length' => 8,
        
        // 最大长度
        'max_length' => 128,
        
        // 是否要求包含数字
        'require_numbers' => true,
        
        // 是否要求包含字母
        'require_letters' => true,
        
        // 是否要求包含大写字母
        'require_uppercase' => true,
        
        // 是否要求包含小写字母
        'require_lowercase' => true,
        
        // 是否要求包含特殊字符
        'require_symbols' => true,
        
        // 特殊字符列表
        'symbols' => '!@#$%^&*()_+-=[]{}|;:,.<>?',
        
        // 密码历史记录数量
        'history_count' => 5,
        
        // 密码过期天数
        'expire_days' => 90,
        
        // 哈希算法
        'hash_algorithm' => 'bcrypt',
        
        // Bcrypt成本因子
        'bcrypt_cost' => 12,
    ],

    // 审计日志配置
    'audit' => [
        // 是否启用审计日志
        'enabled' => true,
        
        // 日志保留天数
        'retention_days' => 365,
        
        // 是否记录敏感数据访问
        'log_sensitive_access' => true,
        
        // 是否记录失败的操作
        'log_failures' => true,
        
        // 是否实时告警
        'real_time_alerts' => true,
        
        // 告警阈值
        'alert_thresholds' => [
            'failed_login_attempts' => 5,
            'suspicious_ip_requests' => 100,
            'data_access_frequency' => 1000,
        ],
    ],

    // 数据加密配置
    'encryption' => [
        // 加密密钥
        'key' => env('ENCRYPTION_KEY', ''),
        
        // 加密算法
        'cipher' => 'AES-256-CBC',
        
        // 需要加密的字段
        'encrypted_fields' => [
            'customers' => ['id_card', 'phone', 'bank_account'],
            'loan_applications' => ['contact_phone'],
            'agents' => ['id_card', 'phone', 'bank_account'],
            'investors' => ['id_card', 'contact_phone', 'bank_account'],
        ],
        
        // 数据脱敏配置
        'masking' => [
            'enabled' => true,
            'rules' => [
                'phone' => '***-****-****',
                'id_card' => '******########**',
                'bank_card' => '****-****-****-####',
                'name' => '*##',
                'email' => '***@***.***',
            ],
        ],
    ],

    // 安全监控配置
    'monitoring' => [
        // 是否启用安全监控
        'enabled' => true,
        
        // 监控间隔（秒）
        'interval' => 60,
        
        // 异常检测阈值
        'thresholds' => [
            'error_rate' => 0.05,          // 错误率阈值
            'response_time' => 5000,       // 响应时间阈值（毫秒）
            'concurrent_users' => 1000,    // 并发用户阈值
            'memory_usage' => 0.8,         // 内存使用率阈值
            'cpu_usage' => 0.8,            // CPU使用率阈值
        ],
        
        // 告警方式
        'alerts' => [
            'email' => true,
            'sms' => false,
            'webhook' => false,
        ],
    ],
];
