<?php
declare(strict_types=1);

namespace app\common\service;

use think\facade\Log;
use think\facade\Db;

/**
 * 增强版AI风控服务 - 辅助方法类
 * 包含特征工程、评估融合、缓存管理等功能
 */
class EnhancedAiRiskService
{
    /**
     * 计算年龄
     * @param string $birthDate 出生日期
     * @return int 年龄
     */
    public static function calculateAge(string $birthDate): int
    {
        try {
            $birth = new \DateTime($birthDate);
            $now = new \DateTime();
            return $now->diff($birth)->y;
        } catch (\Exception $e) {
            return 0;
        }
    }

    /**
     * 计算收入稳定性评分
     * @param array $customerData 客户数据
     * @return int 评分 0-100
     */
    public static function calculateIncomeStability(array $customerData): int
    {
        $score = 50; // 基础分
        
        // 收入来源稳定性
        $incomeSource = $customerData['income_source'] ?? '';
        $sourceScores = [
            '工资' => 30,
            '营业收入' => 20,
            '投资收益' => 10,
            '其他' => 5
        ];
        $score += $sourceScores[$incomeSource] ?? 0;
        
        // 工作年限
        $workYears = $customerData['work_years'] ?? 0;
        if ($workYears >= 5) {
            $score += 20;
        } elseif ($workYears >= 2) {
            $score += 10;
        } elseif ($workYears >= 1) {
            $score += 5;
        }
        
        return min(100, max(0, $score));
    }

    /**
     * 计算信息完整性评分
     * @param array $customerData 客户数据
     * @return int 评分 0-100
     */
    public static function calculateInfoCompleteness(array $customerData): int
    {
        $requiredFields = [
            'name', 'phone', 'id_card', 'birth_date', 'gender',
            'education', 'marital_status', 'industry', 'occupation',
            'monthly_income', 'residence_address', 'work_address',
            'emergency_contact_name', 'emergency_contact_phone'
        ];
        
        $completedFields = 0;
        foreach ($requiredFields as $field) {
            if (!empty($customerData[$field])) {
                $completedFields++;
            }
        }
        
        return round(($completedFields / count($requiredFields)) * 100);
    }

    /**
     * 计算行业风险评分
     * @param string $industry 行业
     * @return int 评分 0-100 (分数越高风险越低)
     */
    public static function calculateIndustryRisk(string $industry): int
    {
        $industryRiskMap = [
            // 低风险行业 (80-100分)
            '公务员' => 95,
            '教师' => 90,
            '医生' => 88,
            '银行' => 85,
            '国企员工' => 82,
            
            // 中等风险行业 (60-79分)
            'IT互联网' => 75,
            '制造业' => 70,
            '房地产' => 65,
            '金融' => 68,
            '零售' => 62,
            
            // 高风险行业 (40-59分)
            '餐饮' => 55,
            '建筑' => 50,
            '运输' => 48,
            '娱乐' => 45,
            
            // 很高风险行业 (0-39分)
            '个体户' => 35,
            '自由职业' => 30,
            '无业' => 10
        ];
        
        return $industryRiskMap[$industry] ?? 50; // 默认中等风险
    }

    /**
     * 计算地域风险评分
     * @param array $customerData 客户数据
     * @return int 评分 0-100
     */
    public static function calculateLocationRisk(array $customerData): int
    {
        $score = 70; // 基础分
        
        // 根据城市等级调整
        $city = $customerData['city'] ?? '';
        $cityLevels = [
            '一线城市' => ['北京', '上海', '广州', '深圳'],
            '新一线城市' => ['成都', '杭州', '重庆', '武汉', '苏州', '西安', '天津', '南京', '郑州', '长沙', '沈阳', '青岛', '宁波', '东莞', '无锡'],
            '二线城市' => ['昆明', '大连', '厦门', '合肥', '佛山', '福州', '哈尔滨', '济南', '温州', '长春', '石家庄', '常州', '泉州', '南宁', '贵阳']
        ];
        
        foreach ($cityLevels as $level => $cities) {
            if (in_array($city, $cities)) {
                switch ($level) {
                    case '一线城市':
                        $score += 20;
                        break;
                    case '新一线城市':
                        $score += 15;
                        break;
                    case '二线城市':
                        $score += 10;
                        break;
                }
                break;
            }
        }
        
        return min(100, max(0, $score));
    }

    /**
     * 计算联系人可靠性评分
     * @param array $customerData 客户数据
     * @return int 评分 0-100
     */
    public static function calculateContactReliability(array $customerData): int
    {
        $score = 50; // 基础分
        
        // 联系人关系
        $relation = $customerData['emergency_contact_relation'] ?? '';
        $relationScores = [
            '配偶' => 30,
            '父母' => 25,
            '子女' => 20,
            '兄弟姐妹' => 15,
            '朋友' => 10,
            '同事' => 8,
            '其他' => 5
        ];
        $score += $relationScores[$relation] ?? 0;
        
        // 联系人电话是否填写
        if (!empty($customerData['emergency_contact_phone'])) {
            $score += 20;
        }
        
        return min(100, max(0, $score));
    }

    /**
     * 分析历史行为
     * @param int $customerId 客户ID
     * @return array 行为分析结果
     */
    public static function analyzeBehaviorHistory(int $customerId): array
    {
        try {
            // 查询申请历史
            $applications = Db::name('loan_applications')
                ->where('customer_id', $customerId)
                ->order('created_at', 'desc')
                ->limit(10)
                ->select()
                ->toArray();
            
            // 查询还款历史
            $repayments = Db::name('repayment_records')
                ->alias('r')
                ->join('loans l', 'r.loan_id = l.id')
                ->where('l.customer_id', $customerId)
                ->order('r.created_at', 'desc')
                ->limit(20)
                ->select()
                ->toArray();
            
            $analysis = [
                'application_count' => count($applications),
                'application_frequency' => self::calculateApplicationFrequency($applications),
                'repayment_history' => self::analyzeRepaymentHistory($repayments),
                'overdue_count' => self::countOverdueRecords($repayments),
                'max_overdue_days' => self::getMaxOverdueDays($repayments),
                'credit_utilization' => self::calculateCreditUtilization($customerId)
            ];
            
            return $analysis;
            
        } catch (\Exception $e) {
            Log::error('历史行为分析失败', ['customer_id' => $customerId, 'error' => $e->getMessage()]);
            return [
                'application_count' => 0,
                'application_frequency' => '无记录',
                'repayment_history' => '无记录',
                'overdue_count' => 0,
                'max_overdue_days' => 0,
                'credit_utilization' => 0
            ];
        }
    }

    /**
     * 计算申请频率
     * @param array $applications 申请记录
     * @return string 频率描述
     */
    private static function calculateApplicationFrequency(array $applications): string
    {
        if (empty($applications)) {
            return '无记录';
        }
        
        $count = count($applications);
        $firstApp = end($applications);
        $lastApp = reset($applications);
        
        $daysDiff = (strtotime($lastApp['created_at']) - strtotime($firstApp['created_at'])) / 86400;
        
        if ($daysDiff <= 0) {
            return '首次申请';
        }
        
        $frequency = $count / ($daysDiff / 30); // 每月申请次数
        
        if ($frequency > 2) {
            return '频繁';
        } elseif ($frequency > 1) {
            return '较频繁';
        } elseif ($frequency > 0.5) {
            return '正常';
        } else {
            return '较少';
        }
    }

    /**
     * 分析还款历史
     * @param array $repayments 还款记录
     * @return string 还款历史描述
     */
    private static function analyzeRepaymentHistory(array $repayments): string
    {
        if (empty($repayments)) {
            return '无记录';
        }
        
        $totalCount = count($repayments);
        $onTimeCount = 0;
        $overdueCount = 0;
        
        foreach ($repayments as $repayment) {
            if ($repayment['status'] === 'paid' && $repayment['overdue_days'] == 0) {
                $onTimeCount++;
            } elseif ($repayment['overdue_days'] > 0) {
                $overdueCount++;
            }
        }
        
        $onTimeRate = $onTimeCount / $totalCount;
        
        if ($onTimeRate >= 0.95) {
            return '优秀';
        } elseif ($onTimeRate >= 0.85) {
            return '良好';
        } elseif ($onTimeRate >= 0.70) {
            return '一般';
        } else {
            return '较差';
        }
    }

    /**
     * 统计逾期记录数
     * @param array $repayments 还款记录
     * @return int 逾期次数
     */
    private static function countOverdueRecords(array $repayments): int
    {
        $count = 0;
        foreach ($repayments as $repayment) {
            if ($repayment['overdue_days'] > 0) {
                $count++;
            }
        }
        return $count;
    }

    /**
     * 获取最大逾期天数
     * @param array $repayments 还款记录
     * @return int 最大逾期天数
     */
    private static function getMaxOverdueDays(array $repayments): int
    {
        $maxDays = 0;
        foreach ($repayments as $repayment) {
            if ($repayment['overdue_days'] > $maxDays) {
                $maxDays = $repayment['overdue_days'];
            }
        }
        return $maxDays;
    }

    /**
     * 计算信用利用率
     * @param int $customerId 客户ID
     * @return float 利用率 0-1
     */
    private static function calculateCreditUtilization(int $customerId): float
    {
        try {
            // 查询当前未还清的贷款
            $activeLoans = Db::name('loans')
                ->where('customer_id', $customerId)
                ->where('status', 'active')
                ->sum('remaining_amount');
            
            // 查询客户的总授信额度
            $totalCreditLimit = Db::name('customer_credit_limits')
                ->where('customer_id', $customerId)
                ->sum('credit_limit');
            
            if ($totalCreditLimit <= 0) {
                return 0;
            }
            
            return min(1, $activeLoans / $totalCreditLimit);
            
        } catch (\Exception $e) {
            Log::error('计算信用利用率失败', ['customer_id' => $customerId, 'error' => $e->getMessage()]);
            return 0;
        }
    }
}
