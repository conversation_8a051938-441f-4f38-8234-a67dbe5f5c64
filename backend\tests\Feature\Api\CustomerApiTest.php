<?php
declare(strict_types=1);

namespace tests\Feature\Api;

use tests\TestCase;
use think\facade\Db;

/**
 * 客户管理API集成测试
 */
class CustomerApiTest extends TestCase
{
    private $authToken;
    private $testUser;

    protected function setUp(): void
    {
        parent::setUp();
        
        // 创建测试用户并获取认证token
        $this->testUser = $this->createTestUser([
            'username' => 'admin',
            'password' => password_hash('password', PASSWORD_DEFAULT),
            'status' => 1
        ]);
        
        $this->authToken = 'test_token_' . uniqid();
    }

    /**
     * 测试获取客户列表成功
     */
    public function testGetCustomersSuccess(): void
    {
        // 创建测试客户
        $customer1 = $this->createTestCustomer(['name' => '张三', 'status' => 'active']);
        $customer2 = $this->createTestCustomer(['name' => '李四', 'status' => 'active']);
        $customer3 = $this->createTestCustomer(['name' => '王五', 'status' => 'inactive']);

        $response = $this->makeRequest('GET', '/api/customers', [
            'page' => 1,
            'limit' => 10
        ], [
            'Authorization: Bearer ' . $this->authToken
        ]);

        $this->assertResponseSuccess($response);
        $this->assertArrayHasKey('data', $response);
        $this->assertArrayHasKey('meta', $response);
        $this->assertArrayHasKey('pagination', $response['meta']);
        
        $customers = $response['data'];
        $this->assertCount(3, $customers);
        $this->assertEquals('张三', $customers[0]['name']);
        $this->assertEquals('李四', $customers[1]['name']);
        $this->assertEquals('王五', $customers[2]['name']);
    }

    /**
     * 测试按状态筛选客户
     */
    public function testGetCustomersWithStatusFilter(): void
    {
        // 创建测试客户
        $this->createTestCustomer(['name' => '张三', 'status' => 'active']);
        $this->createTestCustomer(['name' => '李四', 'status' => 'active']);
        $this->createTestCustomer(['name' => '王五', 'status' => 'inactive']);

        $response = $this->makeRequest('GET', '/api/customers', [
            'status' => 'active',
            'page' => 1,
            'limit' => 10
        ], [
            'Authorization: Bearer ' . $this->authToken
        ]);

        $this->assertResponseSuccess($response);
        $customers = $response['data'];
        $this->assertCount(2, $customers);
        
        foreach ($customers as $customer) {
            $this->assertEquals('active', $customer['status']);
        }
    }

    /**
     * 测试按风险等级筛选客户
     */
    public function testGetCustomersWithRiskLevelFilter(): void
    {
        // 创建测试客户
        $this->createTestCustomer(['name' => '张三', 'risk_level' => 'low']);
        $this->createTestCustomer(['name' => '李四', 'risk_level' => 'high']);
        $this->createTestCustomer(['name' => '王五', 'risk_level' => 'high']);

        $response = $this->makeRequest('GET', '/api/customers', [
            'risk_level' => 'high',
            'page' => 1,
            'limit' => 10
        ], [
            'Authorization: Bearer ' . $this->authToken
        ]);

        $this->assertResponseSuccess($response);
        $customers = $response['data'];
        $this->assertCount(2, $customers);
        
        foreach ($customers as $customer) {
            $this->assertEquals('high', $customer['risk_level']);
        }
    }

    /**
     * 测试创建客户成功
     */
    public function testCreateCustomerSuccess(): void
    {
        $customerData = [
            'name' => '新客户',
            'phone' => '13800138999',
            'id_card' => '110101199001019999',
            'gender' => 'male',
            'birth_date' => '1990-01-01',
            'education' => 'university',
            'marital_status' => 'single',
            'industry' => '互联网',
            'occupation' => '软件工程师',
            'monthly_income' => 15000.00,
            'residence_address' => '北京市朝阳区',
            'work_address' => '北京市海淀区'
        ];

        $response = $this->makeRequest('POST', '/api/customers', $customerData, [
            'Authorization: Bearer ' . $this->authToken
        ]);

        $this->assertResponseSuccess($response);
        $this->assertEquals('创建成功', $response['message']);
        $this->assertArrayHasKey('data', $response);
        
        $customer = $response['data'];
        $this->assertEquals('新客户', $customer['name']);
        $this->assertEquals('13800138999', $customer['phone']);
        $this->assertEquals('110101199001019999', $customer['id_card']);

        // 验证数据库中的数据
        $this->assertDatabaseHas('customers', [
            'name' => '新客户',
            'phone' => '13800138999',
            'id_card' => '110101199001019999'
        ]);
    }

    /**
     * 测试创建客户失败 - 必填字段缺失
     */
    public function testCreateCustomerFailureMissingRequired(): void
    {
        $customerData = [
            'phone' => '13800138999',
            'id_card' => '110101199001019999'
            // 缺少name字段
        ];

        $response = $this->makeRequest('POST', '/api/customers', $customerData, [
            'Authorization: Bearer ' . $this->authToken
        ]);

        $this->assertResponseError($response, 422);
        $this->assertArrayHasKey('errors', $response);
        $this->assertStringContainsString('姓名不能为空', $response['message']);
    }

    /**
     * 测试创建客户失败 - 手机号重复
     */
    public function testCreateCustomerFailureDuplicatePhone(): void
    {
        // 先创建一个客户
        $this->createTestCustomer(['phone' => '13800138999']);

        $customerData = [
            'name' => '新客户',
            'phone' => '13800138999', // 重复的手机号
            'id_card' => '110101199001019998'
        ];

        $response = $this->makeRequest('POST', '/api/customers', $customerData, [
            'Authorization: Bearer ' . $this->authToken
        ]);

        $this->assertResponseError($response, 409);
        $this->assertStringContainsString('手机号已存在', $response['message']);
    }

    /**
     * 测试获取客户详情成功
     */
    public function testGetCustomerSuccess(): void
    {
        $customer = $this->createTestCustomer([
            'name' => '测试客户',
            'phone' => '13800138999',
            'id_card' => '110101199001019999',
            'status' => 'active',
            'risk_level' => 'medium',
            'risk_score' => 75
        ]);

        $response = $this->makeRequest('GET', "/api/customers/{$customer['id']}", [], [
            'Authorization: Bearer ' . $this->authToken
        ]);

        $this->assertResponseSuccess($response);
        $this->assertArrayHasKey('data', $response);
        
        $customerData = $response['data'];
        $this->assertEquals($customer['id'], $customerData['id']);
        $this->assertEquals('测试客户', $customerData['name']);
        $this->assertEquals('13800138999', $customerData['phone']);
        $this->assertEquals('active', $customerData['status']);
        $this->assertEquals('medium', $customerData['risk_level']);
        $this->assertEquals(75, $customerData['risk_score']);
    }

    /**
     * 测试获取客户详情失败 - 客户不存在
     */
    public function testGetCustomerFailureNotFound(): void
    {
        $response = $this->makeRequest('GET', '/api/customers/99999', [], [
            'Authorization: Bearer ' . $this->authToken
        ]);

        $this->assertResponseError($response, 404);
        $this->assertStringContainsString('客户不存在', $response['message']);
    }

    /**
     * 测试更新客户成功
     */
    public function testUpdateCustomerSuccess(): void
    {
        $customer = $this->createTestCustomer([
            'name' => '原客户名',
            'phone' => '13800138999',
            'monthly_income' => 10000.00
        ]);

        $updateData = [
            'name' => '更新后的客户名',
            'monthly_income' => 15000.00,
            'industry' => '金融',
            'occupation' => '分析师'
        ];

        $response = $this->makeRequest('PUT', "/api/customers/{$customer['id']}", $updateData, [
            'Authorization: Bearer ' . $this->authToken
        ]);

        $this->assertResponseSuccess($response);
        $this->assertEquals('更新成功', $response['message']);

        // 验证数据库中的数据已更新
        $this->assertDatabaseHas('customers', [
            'id' => $customer['id'],
            'name' => '更新后的客户名',
            'monthly_income' => 15000.00,
            'industry' => '金融',
            'occupation' => '分析师'
        ]);
    }

    /**
     * 测试更新客户失败 - 客户不存在
     */
    public function testUpdateCustomerFailureNotFound(): void
    {
        $updateData = [
            'name' => '更新后的客户名'
        ];

        $response = $this->makeRequest('PUT', '/api/customers/99999', $updateData, [
            'Authorization: Bearer ' . $this->authToken
        ]);

        $this->assertResponseError($response, 404);
        $this->assertStringContainsString('客户不存在', $response['message']);
    }

    /**
     * 测试删除客户成功
     */
    public function testDeleteCustomerSuccess(): void
    {
        $customer = $this->createTestCustomer([
            'name' => '待删除客户',
            'phone' => '13800138999'
        ]);

        $response = $this->makeRequest('DELETE', "/api/customers/{$customer['id']}", [], [
            'Authorization: Bearer ' . $this->authToken
        ]);

        $this->assertResponseSuccess($response);
        $this->assertEquals('删除成功', $response['message']);

        // 验证数据库中的记录已被软删除或标记为删除状态
        $this->assertDatabaseMissing('customers', [
            'id' => $customer['id'],
            'deleted_at' => null
        ]);
    }

    /**
     * 测试删除客户失败 - 客户不存在
     */
    public function testDeleteCustomerFailureNotFound(): void
    {
        $response = $this->makeRequest('DELETE', '/api/customers/99999', [], [
            'Authorization: Bearer ' . $this->authToken
        ]);

        $this->assertResponseError($response, 404);
        $this->assertStringContainsString('客户不存在', $response['message']);
    }

    /**
     * 测试客户风险评估
     */
    public function testCustomerRiskAssessment(): void
    {
        $customer = $this->createTestCustomer([
            'name' => '风险评估客户',
            'phone' => '13800138999',
            'monthly_income' => 15000.00,
            'industry' => '互联网'
        ]);

        $response = $this->makeRequest('POST', "/api/customers/{$customer['id']}/risk-assessment", [
            'assessment_type' => 'ai',
            'force_refresh' => true
        ], [
            'Authorization: Bearer ' . $this->authToken
        ]);

        $this->assertResponseSuccess($response);
        $this->assertArrayHasKey('data', $response);
        
        $assessment = $response['data'];
        $this->assertArrayHasKey('overall_score', $assessment);
        $this->assertArrayHasKey('risk_level', $assessment);
        $this->assertArrayHasKey('decision', $assessment);
        $this->assertArrayHasKey('assessment_time', $assessment);
        
        // 验证风险评估记录已保存到数据库
        $this->assertDatabaseHas('risk_assessments', [
            'customer_id' => $customer['id'],
            'assessment_type' => 'ai'
        ]);
    }

    /**
     * 测试未授权访问
     */
    public function testUnauthorizedAccess(): void
    {
        $response = $this->makeRequest('GET', '/api/customers');

        $this->assertResponseError($response, 401);
        $this->assertStringContainsString('未授权访问', $response['message']);
    }

    /**
     * 测试无效token访问
     */
    public function testInvalidTokenAccess(): void
    {
        $response = $this->makeRequest('GET', '/api/customers', [], [
            'Authorization: Bearer invalid_token'
        ]);

        $this->assertResponseError($response, 401);
        $this->assertStringContainsString('token无效', $response['message']);
    }
}
