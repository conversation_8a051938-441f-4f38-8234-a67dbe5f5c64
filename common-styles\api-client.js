/**
 * 统一API客户端
 * 用于所有前端应用的HTTP请求封装
 */

import { UTILS, STATUS_CODES, APP_CONFIG } from './frontend-config.js'

class ApiClient {
  constructor(options = {}) {
    this.baseURL = options.baseURL || UTILS.getEnvConfig().API_BASE_URL
    this.timeout = options.timeout || 30000
    this.interceptors = {
      request: [],
      response: []
    }
    
    // 初始化请求拦截器
    this.setupRequestInterceptors()
    this.setupResponseInterceptors()
  }

  /**
   * 设置请求拦截器
   */
  setupRequestInterceptors() {
    // 添加认证token
    this.addRequestInterceptor((config) => {
      const token = UTILS.storage.get(APP_CONFIG.CACHE.TOKEN_KEY)
      if (token) {
        config.headers = config.headers || {}
        config.headers.Authorization = `Bearer ${token}`
      }
      return config
    })

    // 添加通用请求头
    this.addRequestInterceptor((config) => {
      config.headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'X-Requested-With': 'XMLHttpRequest',
        ...config.headers
      }
      return config
    })

    // 请求日志
    if (UTILS.getEnvConfig().DEBUG) {
      this.addRequestInterceptor((config) => {
        console.log('🚀 API Request:', {
          method: config.method?.toUpperCase(),
          url: config.url,
          data: config.data,
          params: config.params
        })
        return config
      })
    }
  }

  /**
   * 设置响应拦截器
   */
  setupResponseInterceptors() {
    // 响应数据处理
    this.addResponseInterceptor(
      (response) => {
        // 响应日志
        if (UTILS.getEnvConfig().DEBUG) {
          console.log('✅ API Response:', {
            status: response.status,
            url: response.config?.url,
            data: response.data
          })
        }

        // 统一响应格式处理
        if (response.data && typeof response.data === 'object') {
          return response.data
        }
        
        return response
      },
      (error) => {
        // 错误日志
        console.error('❌ API Error:', {
          status: error.response?.status,
          url: error.config?.url,
          message: error.message,
          data: error.response?.data
        })

        // 统一错误处理
        return this.handleError(error)
      }
    )
  }

  /**
   * 添加请求拦截器
   */
  addRequestInterceptor(fulfilled, rejected) {
    this.interceptors.request.push({ fulfilled, rejected })
  }

  /**
   * 添加响应拦截器
   */
  addResponseInterceptor(fulfilled, rejected) {
    this.interceptors.response.push({ fulfilled, rejected })
  }

  /**
   * 执行请求拦截器
   */
  async executeRequestInterceptors(config) {
    let processedConfig = config
    
    for (const interceptor of this.interceptors.request) {
      try {
        if (interceptor.fulfilled) {
          processedConfig = await interceptor.fulfilled(processedConfig)
        }
      } catch (error) {
        if (interceptor.rejected) {
          return interceptor.rejected(error)
        }
        throw error
      }
    }
    
    return processedConfig
  }

  /**
   * 执行响应拦截器
   */
  async executeResponseInterceptors(response, isError = false) {
    let processedResponse = response
    
    for (const interceptor of this.interceptors.response) {
      try {
        const handler = isError ? interceptor.rejected : interceptor.fulfilled
        if (handler) {
          processedResponse = await handler(processedResponse)
        }
      } catch (error) {
        throw error
      }
    }
    
    return processedResponse
  }

  /**
   * 错误处理
   */
  handleError(error) {
    const response = error.response
    const status = response?.status
    const data = response?.data

    let errorMessage = '请求失败'
    let errorCode = 'UNKNOWN_ERROR'

    if (status) {
      switch (status) {
        case STATUS_CODES.UNAUTHORIZED:
          errorMessage = '登录已过期，请重新登录'
          errorCode = 'UNAUTHORIZED'
          // 清除本地认证信息
          UTILS.storage.remove(APP_CONFIG.CACHE.TOKEN_KEY)
          UTILS.storage.remove(APP_CONFIG.CACHE.USER_INFO_KEY)
          // 跳转到登录页
          this.redirectToLogin()
          break
        case STATUS_CODES.FORBIDDEN:
          errorMessage = '没有权限访问该资源'
          errorCode = 'FORBIDDEN'
          break
        case STATUS_CODES.NOT_FOUND:
          errorMessage = '请求的资源不存在'
          errorCode = 'NOT_FOUND'
          break
        case STATUS_CODES.INTERNAL_ERROR:
          errorMessage = '服务器内部错误'
          errorCode = 'INTERNAL_ERROR'
          break
        case STATUS_CODES.SERVICE_UNAVAILABLE:
          errorMessage = '服务暂时不可用'
          errorCode = 'SERVICE_UNAVAILABLE'
          break
        default:
          errorMessage = data?.message || `请求失败 (${status})`
          errorCode = data?.code || 'HTTP_ERROR'
      }
    } else if (error.code === 'NETWORK_ERROR') {
      errorMessage = '网络连接失败，请检查网络设置'
      errorCode = 'NETWORK_ERROR'
    } else if (error.code === 'TIMEOUT') {
      errorMessage = '请求超时，请稍后重试'
      errorCode = 'TIMEOUT'
    }

    const apiError = new Error(errorMessage)
    apiError.code = errorCode
    apiError.status = status
    apiError.response = response
    apiError.originalError = error

    return Promise.reject(apiError)
  }

  /**
   * 跳转到登录页
   */
  redirectToLogin() {
    // 根据不同平台处理跳转逻辑
    if (typeof window !== 'undefined') {
      // Web环境
      if (window.location.pathname !== '/login') {
        window.location.href = '/login'
      }
    } else if (typeof uni !== 'undefined') {
      // uni-app环境
      uni.reLaunch({
        url: '/pages/login/login'
      })
    }
  }

  /**
   * 发送请求
   */
  async request(config) {
    try {
      // 处理配置
      const processedConfig = await this.executeRequestInterceptors({
        timeout: this.timeout,
        ...config,
        url: this.buildUrl(config.url)
      })

      // 发送请求
      let response
      if (typeof uni !== 'undefined') {
        // uni-app环境
        response = await this.uniRequest(processedConfig)
      } else if (typeof wx !== 'undefined') {
        // 微信小程序环境
        response = await this.wxRequest(processedConfig)
      } else {
        // Web环境 (使用fetch或axios)
        response = await this.webRequest(processedConfig)
      }

      // 处理响应
      return await this.executeResponseInterceptors(response)
    } catch (error) {
      return await this.executeResponseInterceptors(error, true)
    }
  }

  /**
   * 构建完整URL
   */
  buildUrl(url) {
    if (url.startsWith('http')) {
      return url
    }
    return this.baseURL + url
  }

  /**
   * uni-app请求
   */
  uniRequest(config) {
    return new Promise((resolve, reject) => {
      uni.request({
        url: config.url,
        method: config.method?.toUpperCase() || 'GET',
        data: config.data,
        header: config.headers,
        timeout: config.timeout,
        success: (res) => {
          resolve({
            data: res.data,
            status: res.statusCode,
            headers: res.header,
            config
          })
        },
        fail: (error) => {
          reject({
            message: error.errMsg || '请求失败',
            code: 'NETWORK_ERROR',
            config
          })
        }
      })
    })
  }

  /**
   * 微信小程序请求
   */
  wxRequest(config) {
    return new Promise((resolve, reject) => {
      wx.request({
        url: config.url,
        method: config.method?.toUpperCase() || 'GET',
        data: config.data,
        header: config.headers,
        timeout: config.timeout,
        success: (res) => {
          resolve({
            data: res.data,
            status: res.statusCode,
            headers: res.header,
            config
          })
        },
        fail: (error) => {
          reject({
            message: error.errMsg || '请求失败',
            code: 'NETWORK_ERROR',
            config
          })
        }
      })
    })
  }

  /**
   * Web环境请求
   */
  async webRequest(config) {
    const response = await fetch(config.url, {
      method: config.method || 'GET',
      headers: config.headers,
      body: config.data ? JSON.stringify(config.data) : undefined,
      signal: AbortSignal.timeout(config.timeout)
    })

    const data = await response.json()
    
    return {
      data,
      status: response.status,
      headers: response.headers,
      config
    }
  }

  // HTTP方法快捷方式
  get(url, params, config = {}) {
    return this.request({
      method: 'GET',
      url,
      params,
      ...config
    })
  }

  post(url, data, config = {}) {
    return this.request({
      method: 'POST',
      url,
      data,
      ...config
    })
  }

  put(url, data, config = {}) {
    return this.request({
      method: 'PUT',
      url,
      data,
      ...config
    })
  }

  delete(url, config = {}) {
    return this.request({
      method: 'DELETE',
      url,
      ...config
    })
  }

  patch(url, data, config = {}) {
    return this.request({
      method: 'PATCH',
      url,
      data,
      ...config
    })
  }
}

// 创建默认实例
const apiClient = new ApiClient()

// 导出
export { ApiClient }
export default apiClient
