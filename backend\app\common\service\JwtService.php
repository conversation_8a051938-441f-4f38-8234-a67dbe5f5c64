<?php
declare (strict_types = 1);

namespace app\common\service;

use Firebase\JWT\JWT;
use Firebase\JWT\Key;
use think\Exception;

class JwtService
{
    private static $secret = 'daihou_system_jwt_secret_key_2025';
    private static $expire = 7200; // 2小时
    private static $refreshExpire = 604800; // 7天

    /**
     * 生成JWT Token
     * 
     * @param array $user 用户信息
     * @return array
     */
    public static function generateToken(array $user): array
    {
        $now = time();
        $payload = [
            'iss' => 'daihou_system',
            'aud' => 'daihou_system',
            'iat' => $now,
            'exp' => $now + self::$expire,
            'user' => [
                'id' => $user['id'],
                'username' => $user['username'],
                'real_name' => $user['real_name'],
                'role' => $user['role'],
                'permissions' => $user['permissions'] ?? []
            ]
        ];

        $token = JWT::encode($payload, self::$secret, 'HS256');
        
        // 生成刷新token
        $refreshPayload = [
            'iss' => 'daihou_system',
            'aud' => 'daihou_system',
            'iat' => $now,
            'exp' => $now + self::$refreshExpire,
            'type' => 'refresh',
            'user_id' => $user['id']
        ];
        
        $refreshToken = JWT::encode($refreshPayload, self::$secret, 'HS256');

        return [
            'token' => $token,
            'refresh_token' => $refreshToken,
            'expires_at' => date('Y-m-d H:i:s', $now + self::$expire),
            'refresh_expires_at' => date('Y-m-d H:i:s', $now + self::$refreshExpire)
        ];
    }

    /**
     * 验证JWT Token
     * 
     * @param string $token
     * @return array|null
     */
    public static function validateToken(string $token): ?array
    {
        try {
            $decoded = JWT::decode($token, new Key(self::$secret, 'HS256'));
            return (array) $decoded;
        } catch (\Exception $e) {
            return null;
        }
    }

    /**
     * 刷新Token
     * 
     * @param string $refreshToken
     * @return array|null
     */
    public static function refreshToken(string $refreshToken): ?array
    {
        try {
            $decoded = JWT::decode($refreshToken, new Key(self::$secret, 'HS256'));
            
            if (!isset($decoded->type) || $decoded->type !== 'refresh') {
                return null;
            }

            // 从数据库获取用户信息
            $user = \app\common\model\User::find($decoded->user_id);
            if (!$user) {
                return null;
            }

            return self::generateToken($user->toArray());
        } catch (\Exception $e) {
            return null;
        }
    }

    /**
     * 从请求中获取Token
     * 
     * @param \think\Request $request
     * @return string|null
     */
    public static function getTokenFromRequest(\think\Request $request): ?string
    {
        $authorization = $request->header('Authorization');
        
        if (!$authorization) {
            return null;
        }

        if (strpos($authorization, 'Bearer ') === 0) {
            return substr($authorization, 7);
        }

        return null;
    }
}
