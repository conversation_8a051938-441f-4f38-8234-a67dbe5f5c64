-- 民间空放贷后管理系统数据库结构
-- 创建数据库
CREATE DATABASE IF NOT EXISTS daihou_system DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE daihou_system;

-- 用户表
CREATE TABLE users (
    id INT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    password VARCHAR(255) NOT NULL COMMENT '密码',
    real_name VARCHAR(50) NOT NULL COMMENT '真实姓名',
    phone VARCHAR(20) NOT NULL COMMENT '手机号',
    email VARCHAR(100) COMMENT '邮箱',
    role ENUM('admin','risk_controller','finance','collection','agent','investor') NOT NULL COMMENT '角色',
    department VARCHAR(50) COMMENT '部门',
    permissions JSON COMMENT '权限配置',
    status ENUM('active','inactive','locked') DEFAULT 'active' COMMENT '状态',
    last_login_at TIMESTAMP NULL COMMENT '最后登录时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_username (username),
    INDEX idx_role (role),
    INDEX idx_status (status)
) COMMENT='用户表';

-- 客户表
CREATE TABLE customers (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(50) NOT NULL COMMENT '客户姓名',
    phone VARCHAR(20) NOT NULL UNIQUE COMMENT '手机号',
    id_card VARCHAR(18) NOT NULL UNIQUE COMMENT '身份证号',
    age INT COMMENT '年龄',
    gender ENUM('male','female') COMMENT '性别',
    address TEXT COMMENT '详细地址',
    industry VARCHAR(50) COMMENT '行业',
    monthly_income DECIMAL(10,2) COMMENT '月收入',
    emergency_contact VARCHAR(50) COMMENT '紧急联系人',
    emergency_phone VARCHAR(20) COMMENT '紧急联系人电话',
    risk_score INT DEFAULT 0 COMMENT '风险评分',
    total_loan_amount DECIMAL(12,2) DEFAULT 0 COMMENT '累计放款金额',
    total_repaid_amount DECIMAL(12,2) DEFAULT 0 COMMENT '累计还款金额',
    active_loans_count INT DEFAULT 0 COMMENT '当前活跃借款数',
    last_loan_date DATE COMMENT '最后放款日期',
    last_repayment_date DATE COMMENT '最后还款日期',
    customer_profit DECIMAL(10,2) DEFAULT 0 COMMENT '客户总利润',
    risk_assessment_score INT COMMENT '风险评估分数',
    status ENUM('active','blacklist','suspended') DEFAULT 'active' COMMENT '客户状态',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_phone (phone),
    INDEX idx_id_card (id_card),
    INDEX idx_status (status)
) COMMENT='客户信息表';

-- 放款登记记录表
CREATE TABLE loan_disbursement_records (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    business_flow_no VARCHAR(32) NOT NULL UNIQUE COMMENT '业务流水号',
    loan_date DATE NOT NULL COMMENT '放款时间',
    customer_name VARCHAR(50) NOT NULL COMMENT '客户姓名',
    customer_id BIGINT UNSIGNED NOT NULL COMMENT '客户ID',
    risk_controller_name VARCHAR(50) NOT NULL COMMENT '风控人员姓名',
    risk_controller_id INT NOT NULL COMMENT '风控人员ID',
    loan_amount DECIMAL(10,2) NOT NULL COMMENT '放款金额',
    
    -- 还款周期设置
    repayment_type ENUM('daily','weekly','monthly') NOT NULL COMMENT '还款方式',
    repayment_cycle INT NOT NULL COMMENT '还款周期(天数)',
    repayment_frequency VARCHAR(20) COMMENT '还款频率描述(每天/每周几/每月几号)',
    
    -- 中介返点
    agent_id INT COMMENT '中介ID',
    agent_name VARCHAR(50) COMMENT '中介姓名',
    agent_commission_rate DECIMAL(5,2) COMMENT '中介返点比例(%)',
    agent_commission_amount DECIMAL(10,2) COMMENT '中介返点金额',
    commission_type ENUM('front','back','both') DEFAULT 'front' COMMENT '返点类型:前返/后返/前后都返',
    front_commission_amount DECIMAL(10,2) DEFAULT 0 COMMENT '前返金额',
    back_commission_amount DECIMAL(10,2) DEFAULT 0 COMMENT '后返金额',
    back_commission_paid TINYINT DEFAULT 0 COMMENT '后返是否已支付',
    
    -- 费用管理
    platform_fee DECIMAL(10,2) NOT NULL DEFAULT 0 COMMENT '平台费(放款即扣)',
    overdue_fee DECIMAL(10,2) DEFAULT 0 COMMENT '逾期费',
    
    -- 财务统计
    total_repaid_amount DECIMAL(10,2) DEFAULT 0 COMMENT '回款总额',
    remaining_amount DECIMAL(10,2) NOT NULL COMMENT '未回款总额',
    customer_profit DECIMAL(10,2) DEFAULT 0 COMMENT '客户利润',
    
    -- 状态管理
    status ENUM('active','completed','overdue','settled') DEFAULT 'active' COMMENT '放款状态',
    created_by INT NOT NULL COMMENT '登记人',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_customer_id (customer_id),
    INDEX idx_agent_id (agent_id),
    INDEX idx_loan_date (loan_date),
    INDEX idx_status (status),
    FOREIGN KEY (customer_id) REFERENCES customers(id),
    FOREIGN KEY (created_by) REFERENCES users(id)
) COMMENT='放款登记记录表';

-- 还款计划详细表
CREATE TABLE repayment_schedule_details (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    disbursement_record_id BIGINT UNSIGNED NOT NULL COMMENT '放款记录ID',
    period_number INT NOT NULL COMMENT '期数',
    due_date DATE NOT NULL COMMENT '应还日期',
    due_amount DECIMAL(10,2) NOT NULL COMMENT '应还金额',
    paid_amount DECIMAL(10,2) DEFAULT 0 COMMENT '已还金额',
    remaining_amount DECIMAL(10,2) NOT NULL COMMENT '剩余金额',
    status ENUM('pending','paid','overdue','partial') DEFAULT 'pending' COMMENT '状态',
    overdue_days INT DEFAULT 0 COMMENT '逾期天数',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_disbursement_record_id (disbursement_record_id),
    INDEX idx_due_date (due_date),
    INDEX idx_status (status),
    FOREIGN KEY (disbursement_record_id) REFERENCES loan_disbursement_records(id)
) COMMENT='还款计划详细表';

-- 还款登记记录表
CREATE TABLE repayment_records (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    business_flow_no VARCHAR(32) NOT NULL UNIQUE COMMENT '业务流水号',
    disbursement_record_id BIGINT UNSIGNED NOT NULL COMMENT '关联放款记录ID',
    customer_id BIGINT UNSIGNED NOT NULL COMMENT '客户ID',
    customer_name VARCHAR(50) NOT NULL COMMENT '客户姓名',
    
    -- 还款信息
    repayment_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '还款时间',
    repayment_method ENUM('wechat','alipay','bank_card','cash','other') NOT NULL COMMENT '还款方式',
    repayment_amount DECIMAL(10,2) NOT NULL COMMENT '还款金额',
    repayment_type ENUM('normal','partial','negotiated','overdue_fee','early_settlement') NOT NULL COMMENT '还款形式',
    
    -- 附件和备注
    payment_screenshot VARCHAR(500) COMMENT '还款记录截图',
    remark TEXT COMMENT '备注说明',
    
    -- 逾期处理
    overdue_days INT DEFAULT 0 COMMENT '逾期天数',
    overdue_fee DECIMAL(10,2) DEFAULT 0 COMMENT '本次逾期费',
    
    -- 状态管理
    status ENUM('pending','confirmed','cancelled') DEFAULT 'confirmed' COMMENT '记录状态',
    created_by INT NOT NULL COMMENT '登记人',
    confirmed_by INT COMMENT '确认人',
    confirmed_at TIMESTAMP NULL COMMENT '确认时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_disbursement_record_id (disbursement_record_id),
    INDEX idx_customer_id (customer_id),
    INDEX idx_repayment_date (repayment_date),
    INDEX idx_status (status),
    FOREIGN KEY (disbursement_record_id) REFERENCES loan_disbursement_records(id),
    FOREIGN KEY (customer_id) REFERENCES customers(id),
    FOREIGN KEY (created_by) REFERENCES users(id)
) COMMENT='还款登记记录表';

-- 系统日志表
CREATE TABLE system_logs (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    user_id INT UNSIGNED COMMENT '用户ID',
    action VARCHAR(100) NOT NULL COMMENT '操作动作',
    resource_type VARCHAR(50) COMMENT '资源类型',
    resource_id BIGINT COMMENT '资源ID',
    details JSON COMMENT '详细信息',
    ip_address VARCHAR(45) COMMENT 'IP地址',
    user_agent TEXT COMMENT '用户代理',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_user_id (user_id),
    INDEX idx_action (action),
    INDEX idx_created_at (created_at),
    FOREIGN KEY (user_id) REFERENCES users(id)
) COMMENT='系统操作日志表';

-- 应用配置表
CREATE TABLE app_configs (
    id INT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    platform ENUM('web','app','miniprogram','h5') NOT NULL COMMENT '平台类型',
    app_type ENUM('admin','customer','agent','investor','finance','collection') NOT NULL COMMENT '应用类型',
    config_key VARCHAR(100) NOT NULL COMMENT '配置键',
    config_value TEXT NOT NULL COMMENT '配置值',
    config_type ENUM('string','number','boolean','json','file') DEFAULT 'string' COMMENT '配置类型',
    description TEXT COMMENT '配置描述',
    is_active TINYINT DEFAULT 1 COMMENT '是否启用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_platform_app (platform, app_type),
    INDEX idx_config_key (config_key),
    UNIQUE KEY uk_platform_app_key (platform, app_type, config_key)
) COMMENT='多端应用配置表';

-- 行业黑名单表
CREATE TABLE industry_blacklist (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    type ENUM('phone','id_card','name','device') NOT NULL COMMENT '黑名单类型',
    value VARCHAR(100) NOT NULL COMMENT '黑名单值',
    source VARCHAR(50) COMMENT '数据来源',
    reason TEXT COMMENT '拉黑原因',
    risk_level ENUM('low','medium','high') DEFAULT 'medium' COMMENT '风险等级',
    effective_date DATE NOT NULL COMMENT '生效日期',
    expire_date DATE COMMENT '过期日期',
    is_active TINYINT DEFAULT 1 COMMENT '是否有效',
    created_by INT UNSIGNED COMMENT '创建人',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_type_value (type, value),
    INDEX idx_risk_level (risk_level),
    INDEX idx_is_active (is_active),
    FOREIGN KEY (created_by) REFERENCES users(id)
) COMMENT='行业黑名单表';

-- 插入初始数据
INSERT INTO users (username, password, real_name, phone, role, status) VALUES 
('admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '系统管理员', '13900000000', 'admin', 'active'),
('finance001', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '财务主管', '13900000001', 'finance', 'active'),
('risk001', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '风控主管', '13900000002', 'risk_controller', 'active');

-- 插入配置数据
INSERT INTO app_configs (platform, app_type, config_key, config_value, config_type, description) VALUES 
('web', 'admin', 'app_name', '管理后台', 'string', '应用名称'),
('web', 'admin', 'primary_color', '#1890ff', 'string', '主题色'),
('app', 'finance', 'app_name', '财务管理', 'string', '应用名称'),
('app', 'finance', 'primary_color', '#52c41a', 'string', '主题色');
