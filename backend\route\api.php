<?php
// +----------------------------------------------------------------------
// | API路由配置
// +----------------------------------------------------------------------

use think\facade\Route;

// API版本分组
Route::group('api/v1', function () {
    
    // 认证相关 - 无需认证
    Route::group('auth', function () {
        Route::post('login', '\app\api\controller\Auth@login');
        Route::post('refresh', '\app\api\controller\Auth@refresh');
    });
    
    // 需要认证的API
    Route::group('', function () {
        
        // 用户信息
        Route::get('auth/user', '\app\api\controller\Auth@user');
        Route::post('auth/logout', '\app\api\controller\Auth@logout');
        
        // 客户管理
        Route::group('customers', function () {
            Route::get('/', '\app\api\controller\Customer@index'); // 客户列表
            Route::get('search', '\app\api\controller\Customer@search'); // 客户搜索
            Route::post('/', '\app\api\controller\Customer@save'); // 创建客户
            Route::get(':id', '\app\api\controller\Customer@read'); // 客户详情
            Route::put(':id', '\app\api\controller\Customer@update'); // 更新客户
            Route::delete(':id', '\app\api\controller\Customer@delete'); // 删除客户
        });
        
        // 财务管理
        Route::group('finance', function () {
            
            // 放款管理
            Route::group('disbursements', function () {
                Route::get('/', '\app\api\controller\Finance@getDisbursements'); // 放款列表
                Route::post('/', '\app\api\controller\Finance@createDisbursement'); // 创建放款
                Route::get(':id', '\app\api\controller\Finance@getDisbursementDetail'); // 放款详情
            });
            
            // 还款管理
            Route::group('repayments', function () {
                Route::get('/', '\app\api\controller\Finance@getRepayments'); // 还款列表
                Route::post('/', '\app\api\controller\Finance@createRepayment'); // 创建还款
            });
            
            // 文件上传
            Route::post('upload-screenshot', '\app\api\controller\Finance@uploadScreenshot');
            
            // 统计数据
            Route::get('statistics', '\app\api\controller\Finance@getStatistics');
        });

        // 资方端（限制 investor/admin 访问）
        Route::group('investor', function () {
            Route::post('query-customer', '\app\api\controller\Investor@queryCustomer');
            Route::get('my-customers', '\app\api\controller\Investor@myCustomers');
            Route::put('customers/:id/tags', '\app\api\controller\Investor@updateCustomerTags');
        })->middleware(\app\common\middleware\RoleMiddleware::class, 'investor');

        // 黑名单（限制 admin/finance 访问）
        Route::group('blacklist', function () {
            Route::post('/', '\app\api\controller\Blacklist@create');
            Route::get('check', '\app\api\controller\Blacklist@check');
            Route::post('batch-check', '\app\api\controller\Blacklist@batchCheck');
        })->middleware(\app\common\middleware\RoleMiddleware::class, 'admin', 'finance');

        // 多端配置 / 小程序配置（变更接口限制 admin）
        Route::get('config', '\app\api\controller\Config@getAppConfig');
        Route::put('config', '\app\api\controller\Config@updateAppConfig')->middleware(\app\common\middleware\RoleMiddleware::class, 'admin');
        Route::get('miniprogram/components', '\app\api\controller\Config@components');
        Route::post('miniprogram/pages', '\app\api\controller\Config@savePage')->middleware(\app\common\middleware\RoleMiddleware::class, 'admin');

        // 导出（需权限）
        Route::get('export/disbursements', '\app\api\controller\Export@disbursements')->middleware(\app\common\middleware\PermissionMiddleware::class, 'finance:export');
        Route::get('export/repayments', '\app\api\controller\Export@repayments')->middleware(\app\common\middleware\PermissionMiddleware::class, 'finance:export');

        // 通知（仅admin）
        Route::post('notifications', '\app\api\controller\Notification@save')->middleware(\app\common\middleware\RoleMiddleware::class, 'admin');
        
    })->middleware(\app\common\middleware\AuthMiddleware::class);
    
})->middleware(\app\common\middleware\CorsMiddleware::class);

// 健康检查
Route::get('health', function () {
    return json([
        'status' => 'ok',
        'timestamp' => time(),
        'version' => '1.0.0'
    ]);
})->middleware(\app\common\middleware\CorsMiddleware::class);