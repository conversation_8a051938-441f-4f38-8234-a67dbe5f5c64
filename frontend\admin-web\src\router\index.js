import { createRouter, createWebHistory } from 'vue-router'
import Login from '../views/Login.vue'
import Dashboard from '../views/Dashboard.vue'
import LoanDisbursement from '../views/finance/LoanDisbursement.vue'
import RepaymentManagement from '../views/finance/RepaymentManagement.vue'
import CustomerManagement from '../views/customer/CustomerManagement.vue'
import BlacklistManagement from '../views/system/BlacklistManagement.vue'
import InvestorManagement from '../views/investor/InvestorManagement.vue'
import ConfigManagement from '../views/system/ConfigManagement.vue'

const routes = [
  {
    path: '/login',
    name: 'Login',
    component: Login
  },
  {
    path: '/',
    name: 'Dashboard',
    component: Dashboard,
    meta: { requiresAuth: true },
    children: [
      {
        path: '/finance/disbursement',
        name: 'LoanDisbursement',
        component: LoanDisbursement,
        meta: { title: '放款登记' }
      },
      {
        path: '/finance/repayment',
        name: 'RepaymentManagement',
        component: RepaymentManagement,
        meta: { title: '还款管理' }
      },
      {
        path: '/customer',
        name: 'CustomerManagement',
        component: CustomerManagement,
        meta: { title: '客户管理' }
      },
      {
        path: '/system/blacklist',
        name: 'BlacklistManagement',
        component: BlacklistManagement,
        meta: { title: '黑名单管理' }
      },
      {
        path: '/investor',
        name: 'InvestorManagement',
        component: InvestorManagement,
        meta: { title: '资方管理' }
      },
      {
        path: '/system/config',
        name: 'ConfigManagement',
        component: ConfigManagement,
        meta: { title: '配置管理' }
      }
    ]
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach((to, from, next) => {
  const token = localStorage.getItem('token')
  
  if (to.matched.some(record => record.meta.requiresAuth)) {
    if (!token) {
      next('/login')
    } else {
      next()
    }
  } else {
    next()
  }
})

export default router
