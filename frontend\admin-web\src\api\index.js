import request from '../utils/request'

// 认证相关API
export const authAPI = {
  // 登录
  login(data) {
    return request.post('/auth/login', data)
  },
  
  // 获取用户信息
  getUserInfo() {
    return request.get('/auth/user')
  },
  
  // 登出
  logout() {
    return request.post('/auth/logout')
  }
}

// 客户管理API
export const customerAPI = {
  // 获取客户列表
  getCustomers(params) {
    return request.get('/customers', { params })
  },
  
  // 搜索客户
  searchCustomers(params) {
    return request.get('/customers/search', { params })
  },
  
  // 创建客户
  createCustomer(data) {
    return request.post('/customers', data)
  },
  
  // 获取客户详情
  getCustomer(id) {
    return request.get(`/customers/${id}`)
  },
  
  // 更新客户
  updateCustomer(id, data) {
    return request.put(`/customers/${id}`, data)
  },
  
  // 删除客户
  deleteCustomer(id) {
    return request.delete(`/customers/${id}`)
  }
}

// 财务管理API
export const financeAPI = {
  // 放款管理
  disbursement: {
    // 获取放款列表
    getList(params) {
      return request.get('/finance/disbursements', { params })
    },
    
    // 创建放款
    create(data) {
      return request.post('/finance/disbursements', data)
    },
    
    // 获取放款详情
    getDetail(id) {
      return request.get(`/finance/disbursements/${id}`)
    }
  },
  
  // 还款管理
  repayment: {
    // 获取还款列表
    getList(params) {
      return request.get('/finance/repayments', { params })
    },
    
    // 创建还款记录
    create(data) {
      return request.post('/finance/repayments', data)
    }
  },
  
  // 上传截图
  uploadScreenshot(file) {
    const formData = new FormData()
    formData.append('file', file)
    return request.post('/finance/upload-screenshot', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },
  
  // 获取统计数据
  getStatistics(params) {
    return request.get('/finance/statistics', { params })
  }
}
