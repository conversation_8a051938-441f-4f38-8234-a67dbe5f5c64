<template>
  <div class="config-management">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>配置管理</span>
        </div>
      </template>
      
      <el-tabs v-model="activeTab" @tab-click="handleTabChange">
        <!-- 应用配置 -->
        <el-tab-pane label="应用配置" name="app">
          <div class="config-section">
            <el-form :model="appConfigs" label-width="150px">
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="系统名称">
                    <el-input v-model="appConfigs.system_name" placeholder="请输入系统名称"/>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="公司名称">
                    <el-input v-model="appConfigs.company_name" placeholder="请输入公司名称"/>
                  </el-form-item>
                </el-col>
              </el-row>
              
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="联系电话">
                    <el-input v-model="appConfigs.contact_phone" placeholder="请输入联系电话"/>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="最大上传大小(MB)">
                    <el-input-number
                      v-model="appConfigs.max_upload_size"
                      :min="1"
                      :max="100"
                      placeholder="请输入最大上传大小"
                      style="width: 100%"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
              
              <el-form-item>
                <el-button type="primary" :loading="saveLoading" @click="saveAppConfigs">保存配置</el-button>
                <el-button @click="resetAppConfigs">重置</el-button>
              </el-form-item>
            </el-form>
          </div>
        </el-tab-pane>
        
        <!-- 小程序配置 -->
        <el-tab-pane label="小程序配置" name="miniprogram">
          <div class="config-section">
            <el-form :model="miniprogramConfigs" label-width="150px">
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="客户端小程序名称">
                    <el-input v-model="miniprogramConfigs.customer_app_name" placeholder="请输入客户端小程序名称"/>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="资方端小程序名称">
                    <el-input v-model="miniprogramConfigs.investor_app_name" placeholder="请输入资方端小程序名称"/>
                  </el-form-item>
                </el-col>
              </el-row>
              
              <el-form-item>
                <el-button type="primary" :loading="saveLoading" @click="saveMiniprogramConfigs">保存配置</el-button>
                <el-button @click="resetMiniprogramConfigs">重置</el-button>
              </el-form-item>
            </el-form>
          </div>
        </el-tab-pane>
        
        <!-- 小程序组件管理 -->
        <el-tab-pane label="组件管理" name="components">
          <div class="config-section">
            <div style="margin-bottom: 20px;">
              <el-button type="primary" @click="showComponentDialog">新增组件</el-button>
            </div>
            
            <el-table
              :data="components"
              :loading="componentsLoading"
              style="width: 100%"
            >
              <el-table-column prop="component_name" label="组件名称" width="150"/>
              <el-table-column prop="component_type" label="组件类型" width="120">
                <template #default="scope">
                  <el-tag>{{ getComponentTypeText(scope.row.component_type) }}</el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="preview_image" label="预览图" width="100">
                <template #default="scope">
                  <el-image
                    v-if="scope.row.preview_image"
                    :src="scope.row.preview_image"
                    style="width: 50px; height: 30px;"
                    fit="cover"
                  />
                </template>
              </el-table-column>
              <el-table-column prop="is_active" label="状态" width="80">
                <template #default="scope">
                  <el-tag :type="scope.row.is_active ? 'success' : 'danger'">
                    {{ scope.row.is_active ? '启用' : '禁用' }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="sort_order" label="排序" width="80"/>
              <el-table-column label="操作" width="150" fixed="right">
                <template #default="scope">
                  <el-button size="small" @click="editComponent(scope.row)">编辑</el-button>
                  <el-button 
                    size="small" 
                    type="danger" 
                    @click="deleteComponent(scope.row)"
                  >
                    删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-tab-pane>
        
        <!-- 页面管理 -->
        <el-tab-pane label="页面管理" name="pages">
          <div class="config-section">
            <div style="margin-bottom: 20px;">
              <el-button type="primary" @click="showPageDialog">新增页面</el-button>
            </div>
            
            <el-table
              :data="pages"
              :loading="pagesLoading"
              style="width: 100%"
            >
              <el-table-column prop="page_name" label="页面名称" width="150"/>
              <el-table-column prop="page_path" label="页面路径" width="200"/>
              <el-table-column prop="is_published" label="发布状态" width="100">
                <template #default="scope">
                  <el-tag :type="scope.row.is_published ? 'success' : 'warning'">
                    {{ scope.row.is_published ? '已发布' : '草稿' }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="created_at" label="创建时间" width="160"/>
              <el-table-column label="操作" width="200" fixed="right">
                <template #default="scope">
                  <el-button size="small" @click="editPage(scope.row)">编辑</el-button>
                  <el-button 
                    size="small" 
                    :type="scope.row.is_published ? 'warning' : 'success'"
                    @click="togglePageStatus(scope.row)"
                  >
                    {{ scope.row.is_published ? '下线' : '发布' }}
                  </el-button>
                  <el-button 
                    size="small" 
                    type="danger" 
                    @click="deletePage(scope.row)"
                  >
                    删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-card>

    <!-- 组件对话框 -->
    <el-dialog
      v-model="componentDialogVisible"
      :title="editComponentMode ? '编辑组件' : '新增组件'"
      width="600px"
    >
      <el-form
        ref="componentFormRef"
        :model="componentForm"
        :rules="componentRules"
        label-width="100px"
      >
        <el-form-item label="组件名称" prop="component_name">
          <el-input v-model="componentForm.component_name" placeholder="请输入组件名称"/>
        </el-form-item>
        
        <el-form-item label="组件类型" prop="component_type">
          <el-select v-model="componentForm.component_type" placeholder="请选择组件类型" style="width: 100%">
            <el-option label="布局" value="layout"/>
            <el-option label="表单" value="form"/>
            <el-option label="展示" value="display"/>
            <el-option label="导航" value="navigation"/>
            <el-option label="媒体" value="media"/>
            <el-option label="其他" value="other"/>
          </el-select>
        </el-form-item>
        
        <el-form-item label="排序">
          <el-input-number v-model="componentForm.sort_order" :min="0" style="width: 100%"/>
        </el-form-item>
        
        <el-form-item label="状态">
          <el-switch v-model="componentForm.is_active" active-text="启用" inactive-text="禁用"/>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="componentDialogVisible = false">取消</el-button>
        <el-button type="primary" :loading="componentSaveLoading" @click="saveComponent">
          {{ editComponentMode ? '更新' : '创建' }}
        </el-button>
      </template>
    </el-dialog>

    <!-- 页面对话框 -->
    <el-dialog
      v-model="pageDialogVisible"
      :title="editPageMode ? '编辑页面' : '新增页面'"
      width="600px"
    >
      <el-form
        ref="pageFormRef"
        :model="pageForm"
        :rules="pageRules"
        label-width="100px"
      >
        <el-form-item label="页面名称" prop="page_name">
          <el-input v-model="pageForm.page_name" placeholder="请输入页面名称"/>
        </el-form-item>
        
        <el-form-item label="页面路径" prop="page_path">
          <el-input v-model="pageForm.page_path" placeholder="请输入页面路径"/>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="pageDialogVisible = false">取消</el-button>
        <el-button type="primary" :loading="pageSaveLoading" @click="savePage">
          {{ editPageMode ? '更新' : '创建' }}
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

export default {
  name: 'ConfigManagement',
  setup() {
    const activeTab = ref('app')
    const saveLoading = ref(false)
    const componentsLoading = ref(false)
    const pagesLoading = ref(false)
    const componentSaveLoading = ref(false)
    const pageSaveLoading = ref(false)
    
    const componentDialogVisible = ref(false)
    const pageDialogVisible = ref(false)
    const editComponentMode = ref(false)
    const editPageMode = ref(false)
    const componentFormRef = ref()
    const pageFormRef = ref()
    
    const components = ref([])
    const pages = ref([])
    
    const appConfigs = reactive({
      system_name: '',
      company_name: '',
      contact_phone: '',
      max_upload_size: 10
    })
    
    const miniprogramConfigs = reactive({
      customer_app_name: '',
      investor_app_name: ''
    })
    
    const componentForm = reactive({
      component_name: '',
      component_type: '',
      sort_order: 0,
      is_active: true
    })
    
    const pageForm = reactive({
      page_name: '',
      page_path: ''
    })
    
    const componentRules = {
      component_name: [{ required: true, message: '请输入组件名称', trigger: 'blur' }],
      component_type: [{ required: true, message: '请选择组件类型', trigger: 'change' }]
    }
    
    const pageRules = {
      page_name: [{ required: true, message: '请输入页面名称', trigger: 'blur' }],
      page_path: [{ required: true, message: '请输入页面路径', trigger: 'blur' }]
    }
    
    // 切换标签页
    const handleTabChange = (tab) => {
      if (tab.props.name === 'components') {
        getComponents()
      } else if (tab.props.name === 'pages') {
        getPages()
      }
    }
    
    // 获取应用配置
    const getAppConfigs = async () => {
      try {
        // TODO: 调用实际API
        // const data = await configAPI.getAppConfigs()
        // Object.assign(appConfigs, data)
        
        // 模拟数据
        Object.assign(appConfigs, {
          system_name: '民间空放贷后管理系统',
          company_name: '某某金融公司',
          contact_phone: '************',
          max_upload_size: 10
        })
      } catch (error) {
        ElMessage.error('获取配置失败')
      }
    }
    
    // 保存应用配置
    const saveAppConfigs = async () => {
      try {
        saveLoading.value = true
        // TODO: 调用实际API
        // await configAPI.updateAppConfigs(appConfigs)
        ElMessage.success('保存成功')
      } catch (error) {
        ElMessage.error('保存失败')
      } finally {
        saveLoading.value = false
      }
    }
    
    // 重置应用配置
    const resetAppConfigs = () => {
      getAppConfigs()
    }
    
    // 保存小程序配置
    const saveMiniprogramConfigs = async () => {
      try {
        saveLoading.value = true
        // TODO: 调用实际API
        // await configAPI.updateMiniprogramConfigs(miniprogramConfigs)
        ElMessage.success('保存成功')
      } catch (error) {
        ElMessage.error('保存失败')
      } finally {
        saveLoading.value = false
      }
    }
    
    // 重置小程序配置
    const resetMiniprogramConfigs = () => {
      Object.assign(miniprogramConfigs, {
        customer_app_name: '客户端小程序',
        investor_app_name: '资方端小程序'
      })
    }
    
    // 获取组件列表
    const getComponents = async () => {
      try {
        componentsLoading.value = true
        // TODO: 调用实际API
        // const data = await configAPI.getComponents()
        // components.value = data
        
        // 模拟数据
        components.value = [
          {
            id: 1,
            component_name: '客户信息卡片',
            component_type: 'display',
            sort_order: 1,
            is_active: true
          }
        ]
      } catch (error) {
        ElMessage.error('获取组件列表失败')
      } finally {
        componentsLoading.value = false
      }
    }
    
    // 获取页面列表
    const getPages = async () => {
      try {
        pagesLoading.value = true
        // TODO: 调用实际API
        // const data = await configAPI.getPages()
        // pages.value = data
        
        // 模拟数据
        pages.value = [
          {
            id: 1,
            page_name: '客户首页',
            page_path: '/pages/customer/index',
            is_published: true,
            created_at: '2024-01-15 10:30:00'
          }
        ]
      } catch (error) {
        ElMessage.error('获取页面列表失败')
      } finally {
        pagesLoading.value = false
      }
    }
    
    // 显示组件对话框
    const showComponentDialog = () => {
      editComponentMode.value = false
      resetComponentForm()
      componentDialogVisible.value = true
    }
    
    // 编辑组件
    const editComponent = (row) => {
      editComponentMode.value = true
      Object.assign(componentForm, row)
      componentDialogVisible.value = true
    }
    
    // 保存组件
    const saveComponent = async () => {
      try {
        const valid = await componentFormRef.value.validate()
        if (!valid) return
        
        componentSaveLoading.value = true
        
        // TODO: 调用API
        if (editComponentMode.value) {
          // await configAPI.updateComponent(componentForm.id, componentForm)
          ElMessage.success('更新成功')
        } else {
          // await configAPI.createComponent(componentForm)
          ElMessage.success('创建成功')
        }
        
        componentDialogVisible.value = false
        getComponents()
      } catch (error) {
        ElMessage.error(editComponentMode.value ? '更新失败' : '创建失败')
      } finally {
        componentSaveLoading.value = false
      }
    }
    
    // 删除组件
    const deleteComponent = async (row) => {
      try {
        await ElMessageBox.confirm('确定要删除这个组件吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        
        // TODO: 调用删除API
        // await configAPI.deleteComponent(row.id)
        ElMessage.success('删除成功')
        getComponents()
      } catch (error) {
        if (error !== 'cancel') {
          ElMessage.error('删除失败')
        }
      }
    }
    
    // 显示页面对话框
    const showPageDialog = () => {
      editPageMode.value = false
      resetPageForm()
      pageDialogVisible.value = true
    }
    
    // 编辑页面
    const editPage = (row) => {
      editPageMode.value = true
      Object.assign(pageForm, row)
      pageDialogVisible.value = true
    }
    
    // 保存页面
    const savePage = async () => {
      try {
        const valid = await pageFormRef.value.validate()
        if (!valid) return
        
        pageSaveLoading.value = true
        
        // TODO: 调用API
        if (editPageMode.value) {
          // await configAPI.updatePage(pageForm.id, pageForm)
          ElMessage.success('更新成功')
        } else {
          // await configAPI.createPage(pageForm)
          ElMessage.success('创建成功')
        }
        
        pageDialogVisible.value = false
        getPages()
      } catch (error) {
        ElMessage.error(editPageMode.value ? '更新失败' : '创建失败')
      } finally {
        pageSaveLoading.value = false
      }
    }
    
    // 切换页面发布状态
    const togglePageStatus = async (row) => {
      try {
        // TODO: 调用API
        // await configAPI.togglePageStatus(row.id, !row.is_published)
        row.is_published = !row.is_published
        ElMessage.success(row.is_published ? '发布成功' : '下线成功')
      } catch (error) {
        ElMessage.error('操作失败')
      }
    }
    
    // 删除页面
    const deletePage = async (row) => {
      try {
        await ElMessageBox.confirm('确定要删除这个页面吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        
        // TODO: 调用删除API
        // await configAPI.deletePage(row.id)
        ElMessage.success('删除成功')
        getPages()
      } catch (error) {
        if (error !== 'cancel') {
          ElMessage.error('删除失败')
        }
      }
    }
    
    // 重置表单
    const resetComponentForm = () => {
      Object.assign(componentForm, {
        component_name: '',
        component_type: '',
        sort_order: 0,
        is_active: true
      })
      componentFormRef.value?.clearValidate()
    }
    
    const resetPageForm = () => {
      Object.assign(pageForm, {
        page_name: '',
        page_path: ''
      })
      pageFormRef.value?.clearValidate()
    }
    
    // 工具函数
    const getComponentTypeText = (type) => {
      const types = {
        layout: '布局',
        form: '表单',
        display: '展示',
        navigation: '导航',
        media: '媒体',
        other: '其他'
      }
      return types[type] || type
    }
    
    onMounted(() => {
      getAppConfigs()
    })
    
    return {
      // 响应式数据
      activeTab,
      saveLoading,
      componentsLoading,
      pagesLoading,
      componentSaveLoading,
      pageSaveLoading,
      componentDialogVisible,
      pageDialogVisible,
      editComponentMode,
      editPageMode,
      componentFormRef,
      pageFormRef,
      components,
      pages,
      appConfigs,
      miniprogramConfigs,
      componentForm,
      pageForm,
      componentRules,
      pageRules,
      
      // 方法
      handleTabChange,
      getAppConfigs,
      saveAppConfigs,
      resetAppConfigs,
      saveMiniprogramConfigs,
      resetMiniprogramConfigs,
      getComponents,
      getPages,
      showComponentDialog,
      editComponent,
      saveComponent,
      deleteComponent,
      showPageDialog,
      editPage,
      savePage,
      togglePageStatus,
      deletePage,
      resetComponentForm,
      resetPageForm,
      getComponentTypeText
    }
  }
}
</script>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.config-section {
  padding: 20px 0;
}
</style>
