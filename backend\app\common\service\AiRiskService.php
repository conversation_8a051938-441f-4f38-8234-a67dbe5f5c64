<?php
declare(strict_types=1);

namespace app\common\service;

use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;
use think\facade\Log;
use think\facade\Db;

/**
 * 增强版AI风控服务类
 * 支持多AI提供商、模型版本管理、评估结果缓存等功能
 */
class AiRiskService
{
    private $client;
    private $providers;
    private $currentProvider;
    private $modelVersion;

    /**
     * AI提供商配置
     */
    const PROVIDERS = [
        'deepseek' => [
            'name' => 'DeepSeek',
            'base_url' => 'https://api.deepseek.com/v1/chat/completions',
            'model' => 'deepseek-chat',
            'max_tokens' => 2000,
            'temperature' => 0.1
        ],
        'openai' => [
            'name' => 'OpenAI',
            'base_url' => 'https://api.openai.com/v1/chat/completions',
            'model' => 'gpt-3.5-turbo',
            'max_tokens' => 2000,
            'temperature' => 0.1
        ],
        'claude' => [
            'name' => '<PERSON>',
            'base_url' => 'https://api.anthropic.com/v1/messages',
            'model' => 'claude-3-sonnet-20240229',
            'max_tokens' => 2000,
            'temperature' => 0.1
        ]
    ];

    /**
     * 风险等级配置
     */
    const RISK_LEVELS = [
        'low' => ['min_score' => 80, 'max_amount' => 100000, 'decision' => 'pass'],
        'medium' => ['min_score' => 60, 'max_amount' => 50000, 'decision' => 'manual'],
        'high' => ['min_score' => 40, 'max_amount' => 20000, 'decision' => 'manual'],
        'very_high' => ['min_score' => 0, 'max_amount' => 0, 'decision' => 'reject']
    ];

    public function __construct()
    {
        $this->client = new Client([
            'timeout' => 60,
            'verify' => false,
            'http_errors' => false
        ]);

        // 初始化配置
        $this->initializeProviders();
        $this->currentProvider = ConfigService::get('ai_risk_provider', 'deepseek', 'ai');
        $this->modelVersion = ConfigService::get('ai_risk_model_version', 'v2.0', 'ai');
    }

    /**
     * 初始化AI提供商配置
     */
    private function initializeProviders(): void
    {
        $this->providers = self::PROVIDERS;

        // 从配置中获取API密钥
        foreach ($this->providers as $key => &$provider) {
            $apiKeyConfig = strtoupper($key) . '_API_KEY';
            $provider['api_key'] = env($apiKeyConfig, '');
        }
    }
    
    /**
     * 客户风险评估（主入口）
     * @param array $customerData 客户数据
     * @param array $options 评估选项
     * @return array 评估结果
     */
    public function assessCustomerRisk(array $customerData, array $options = []): array
    {
        return $this->assessRisk($customerData, $options);
    }

    /**
     * 综合风险评估
     * @param array $customerData 客户数据
     * @param array $options 评估选项
     * @return array 评估结果
     */
    public function assessRisk(array $customerData, array $options = []): array
    {
        $startTime = microtime(true);
        $customerId = $customerData['id'] ?? 0;

        try {
            // 1. 检查缓存
            if (!($options['force_refresh'] ?? false)) {
                $cachedResult = $this->getCachedAssessment($customerId);
                if ($cachedResult) {
                    return $cachedResult;
                }
            }

            // 2. 数据预处理和特征工程
            $processedData = $this->preprocessCustomerData($customerData);

            // 3. 多维度评估
            $assessments = [];

            // AI模型评估
            $aiAssessment = $this->performAiAssessment($processedData, $options);
            $assessments['ai'] = $aiAssessment;

            // 规则引擎评估
            $ruleAssessment = $this->performRuleAssessment($processedData);
            $assessments['rule'] = $ruleAssessment;

            // 统计模型评估
            $modelAssessment = $this->performModelAssessment($processedData);
            $assessments['model'] = $modelAssessment;

            // 4. 融合决策
            $finalResult = $this->fuseAssessments($assessments, $processedData);

            // 5. 结果后处理
            $finalResult = $this->postProcessResult($finalResult, $processedData);

            // 6. 保存评估记录
            $this->saveAssessmentRecord($customerId, $finalResult, $assessments);

            // 7. 缓存结果
            $this->cacheAssessmentResult($customerId, $finalResult);

            // 8. 记录性能指标
            $executionTime = round((microtime(true) - $startTime) * 1000, 2);
            $this->recordPerformanceMetrics($executionTime, $finalResult);

            return $finalResult;

        } catch (\Exception $e) {
            Log::error('AI风控评估失败', [
                'customer_id' => $customerId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return $this->getDefaultRiskResult($customerData);
        }
    }

    /**
     * 数据预处理和特征工程
     * @param array $customerData 原始客户数据
     * @return array 处理后的数据
     */
    private function preprocessCustomerData(array $customerData): array
    {
        $processed = $customerData;

        // 年龄处理
        if (isset($customerData['birth_date'])) {
            $processed['age'] = $this->calculateAge($customerData['birth_date']);
        }

        // 收入稳定性评估
        $processed['income_stability_score'] = $this->calculateIncomeStability($customerData);

        // 信息完整性评分
        $processed['info_completeness_score'] = $this->calculateInfoCompleteness($customerData);

        // 行业风险评分
        $processed['industry_risk_score'] = $this->calculateIndustryRisk($customerData['industry'] ?? '');

        // 地域风险评分
        $processed['location_risk_score'] = $this->calculateLocationRisk($customerData);

        // 联系人可靠性评分
        $processed['contact_reliability_score'] = $this->calculateContactReliability($customerData);

        // 历史行为分析
        $processed['behavior_analysis'] = $this->analyzeBehaviorHistory($customerData['id'] ?? 0);

        return $processed;
    }

    /**
     * 执行AI模型评估
     * @param array $processedData 处理后的数据
     * @param array $options 选项
     * @return array AI评估结果
     */
    private function performAiAssessment(array $processedData, array $options = []): array
    {
        try {
            $provider = $options['provider'] ?? $this->currentProvider;
            $prompt = $this->buildEnhancedRiskAssessmentPrompt($processedData);

            $response = $this->callAiProvider($provider, $prompt);

            if ($response) {
                $result = $this->parseAiAssessmentResult($response, $provider);
                $result['provider'] = $provider;
                $result['model_version'] = $this->modelVersion;
                return $result;
            }

            return $this->getDefaultAiResult();

        } catch (\Exception $e) {
            Log::error('AI评估失败', ['error' => $e->getMessage()]);
            return $this->getDefaultAiResult();
        }
    }
    
    /**
     * 构建增强版风险评估提示词
     * @param array $processedData 处理后的客户数据
     * @return string 提示词
     */
    private function buildEnhancedRiskAssessmentPrompt(array $processedData): string
    {
        $prompt = "你是一位资深的金融风控专家，请对以下客户进行全面的信贷风险评估。\n\n";

        // 基础信息
        $prompt .= "=== 客户基础信息 ===\n";
        $prompt .= "姓名：" . ($processedData['name'] ?? '未知') . "\n";
        $prompt .= "年龄：" . ($processedData['age'] ?? '未知') . "岁\n";
        $prompt .= "性别：" . ($processedData['gender'] ?? '未知') . "\n";
        $prompt .= "学历：" . ($processedData['education'] ?? '未知') . "\n";
        $prompt .= "婚姻状况：" . ($processedData['marital_status'] ?? '未知') . "\n";

        // 职业信息
        $prompt .= "\n=== 职业收入信息 ===\n";
        $prompt .= "行业：" . ($processedData['industry'] ?? '未知') . "\n";
        $prompt .= "职业：" . ($processedData['occupation'] ?? '未知') . "\n";
        $prompt .= "工作年限：" . ($processedData['work_years'] ?? '未知') . "年\n";
        $prompt .= "月收入：" . ($processedData['monthly_income'] ?? '未知') . "元\n";
        $prompt .= "收入来源：" . ($processedData['income_source'] ?? '未知') . "\n";
        $prompt .= "收入稳定性评分：" . ($processedData['income_stability_score'] ?? 0) . "/100\n";

        // 资产负债
        $prompt .= "\n=== 资产负债情况 ===\n";
        $prompt .= "房产情况：" . ($processedData['property_status'] ?? '未知') . "\n";
        $prompt .= "车辆情况：" . ($processedData['vehicle_status'] ?? '未知') . "\n";
        $prompt .= "现有负债：" . ($processedData['existing_debt'] ?? '未知') . "元\n";
        $prompt .= "信用卡数量：" . ($processedData['credit_card_count'] ?? 0) . "张\n";

        // 联系信息
        $prompt .= "\n=== 联系信息 ===\n";
        $prompt .= "居住地址：" . ($processedData['residence_address'] ?? '未知') . "\n";
        $prompt .= "工作地址：" . ($processedData['work_address'] ?? '未知') . "\n";
        $prompt .= "居住年限：" . ($processedData['residence_years'] ?? '未知') . "年\n";
        $prompt .= "紧急联系人：" . ($processedData['emergency_contact_name'] ?? '未知') . "\n";
        $prompt .= "联系人关系：" . ($processedData['emergency_contact_relation'] ?? '未知') . "\n";
        $prompt .= "联系人电话：" . ($processedData['emergency_contact_phone'] ?? '未知') . "\n";

        // 风险评分
        $prompt .= "\n=== 预计算风险评分 ===\n";
        $prompt .= "信息完整性评分：" . ($processedData['info_completeness_score'] ?? 0) . "/100\n";
        $prompt .= "行业风险评分：" . ($processedData['industry_risk_score'] ?? 0) . "/100\n";
        $prompt .= "地域风险评分：" . ($processedData['location_risk_score'] ?? 0) . "/100\n";
        $prompt .= "联系人可靠性评分：" . ($processedData['contact_reliability_score'] ?? 0) . "/100\n";

        // 历史行为
        if (!empty($processedData['behavior_analysis'])) {
            $prompt .= "\n=== 历史行为分析 ===\n";
            $behavior = $processedData['behavior_analysis'];
            $prompt .= "申请频率：" . ($behavior['application_frequency'] ?? '正常') . "\n";
            $prompt .= "还款记录：" . ($behavior['repayment_history'] ?? '无记录') . "\n";
            $prompt .= "逾期情况：" . ($behavior['overdue_count'] ?? 0) . "次\n";
        }

        $prompt .= "\n=== 评估要求 ===\n";
        $prompt .= "请从以下维度进行专业评估：\n";
        $prompt .= "1. 还款能力评估（0-100分）- 基于收入、支出、负债情况\n";
        $prompt .= "2. 还款意愿评估（0-100分）- 基于历史记录、个人品格\n";
        $prompt .= "3. 稳定性评估（0-100分）- 基于工作、居住、收入稳定性\n";
        $prompt .= "4. 信息真实性评估（0-100分）- 基于信息完整性和一致性\n";
        $prompt .= "5. 综合风险等级（low/medium/high/very_high）\n";
        $prompt .= "6. 建议授信额度（考虑风险承受能力）\n";
        $prompt .= "7. 详细风险分析和建议\n\n";

        $prompt .= "请严格按照以下JSON格式返回评估结果：\n";
        $prompt .= "{\n";
        $prompt .= '  "repayment_ability": 85,' . "\n";
        $prompt .= '  "repayment_willingness": 90,' . "\n";
        $prompt .= '  "stability_assessment": 80,' . "\n";
        $prompt .= '  "information_authenticity": 88,' . "\n";
        $prompt .= '  "overall_score": 86,' . "\n";
        $prompt .= '  "risk_level": "medium",' . "\n";
        $prompt .= '  "recommended_credit_limit": 50000,' . "\n";
        $prompt .= '  "risk_factors": ["收入波动较大", "行业竞争激烈"],' . "\n";
        $prompt .= '  "positive_factors": ["工作稳定", "信息完整", "无不良记录"],' . "\n";
        $prompt .= '  "analysis_summary": "客户整体风险可控，建议适度授信",' . "\n";
        $prompt .= '  "recommendations": ["建议定期回访", "关注行业动态", "设置预警机制"]' . "\n";
        $prompt .= "}";

        return $prompt;
    }
    
    /**
     * 调用AI提供商API
     * @param string $provider 提供商名称
     * @param string $prompt 提示词
     * @return array|null API响应
     */
    private function callAiProvider(string $provider, string $prompt): ?array
    {
        if (!isset($this->providers[$provider])) {
            Log::error('不支持的AI提供商', ['provider' => $provider]);
            return null;
        }

        $config = $this->providers[$provider];

        if (empty($config['api_key'])) {
            Log::error('AI提供商API密钥未配置', ['provider' => $provider]);
            return null;
        }

        try {
            switch ($provider) {
                case 'deepseek':
                    return $this->callDeepSeekAPI($prompt, $config);
                case 'openai':
                    return $this->callOpenAiAPI($prompt, $config);
                case 'claude':
                    return $this->callClaudeAPI($prompt, $config);
                default:
                    Log::error('未实现的AI提供商', ['provider' => $provider]);
                    return null;
            }
        } catch (\Exception $e) {
            Log::error('AI提供商调用失败', [
                'provider' => $provider,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * 调用DeepSeek API
     * @param string $prompt 提示词
     * @param array $config 配置
     * @return array|null
     */
    private function callDeepSeekAPI(string $prompt, array $config): ?array
    {
        try {
            $response = $this->client->post($config['base_url'], [
                'headers' => [
                    'Authorization' => 'Bearer ' . $config['api_key'],
                    'Content-Type' => 'application/json',
                ],
                'json' => [
                    'model' => $config['model'],
                    'messages' => [
                        [
                            'role' => 'user',
                            'content' => $prompt
                        ]
                    ],
                    'temperature' => $config['temperature'],
                    'max_tokens' => $config['max_tokens']
                ]
            ]);

            $statusCode = $response->getStatusCode();
            $body = $response->getBody()->getContents();

            if ($statusCode !== 200) {
                Log::error('DeepSeek API返回错误', [
                    'status_code' => $statusCode,
                    'response' => $body
                ]);
                return null;
            }

            return json_decode($body, true);

        } catch (RequestException $e) {
            Log::error('DeepSeek API调用失败', ['error' => $e->getMessage()]);
            return null;
        }
    }

    /**
     * 调用OpenAI API
     * @param string $prompt 提示词
     * @param array $config 配置
     * @return array|null
     */
    private function callOpenAiAPI(string $prompt, array $config): ?array
    {
        try {
            $response = $this->client->post($config['base_url'], [
                'headers' => [
                    'Authorization' => 'Bearer ' . $config['api_key'],
                    'Content-Type' => 'application/json',
                ],
                'json' => [
                    'model' => $config['model'],
                    'messages' => [
                        [
                            'role' => 'user',
                            'content' => $prompt
                        ]
                    ],
                    'temperature' => $config['temperature'],
                    'max_tokens' => $config['max_tokens']
                ]
            ]);

            $statusCode = $response->getStatusCode();
            $body = $response->getBody()->getContents();

            if ($statusCode !== 200) {
                Log::error('OpenAI API返回错误', [
                    'status_code' => $statusCode,
                    'response' => $body
                ]);
                return null;
            }

            return json_decode($body, true);

        } catch (RequestException $e) {
            Log::error('OpenAI API调用失败', ['error' => $e->getMessage()]);
            return null;
        }
    }

    /**
     * 调用Claude API
     * @param string $prompt 提示词
     * @param array $config 配置
     * @return array|null
     */
    private function callClaudeAPI(string $prompt, array $config): ?array
    {
        try {
            $response = $this->client->post($config['base_url'], [
                'headers' => [
                    'x-api-key' => $config['api_key'],
                    'Content-Type' => 'application/json',
                    'anthropic-version' => '2023-06-01'
                ],
                'json' => [
                    'model' => $config['model'],
                    'max_tokens' => $config['max_tokens'],
                    'messages' => [
                        [
                            'role' => 'user',
                            'content' => $prompt
                        ]
                    ]
                ]
            ]);

            $statusCode = $response->getStatusCode();
            $body = $response->getBody()->getContents();

            if ($statusCode !== 200) {
                Log::error('Claude API返回错误', [
                    'status_code' => $statusCode,
                    'response' => $body
                ]);
                return null;
            }

            return json_decode($body, true);

        } catch (RequestException $e) {
            Log::error('Claude API调用失败', ['error' => $e->getMessage()]);
            return null;
        }
    }
    
    /**
     * 解析风险评估结果
     */
    private function parseRiskAssessmentResult($content)
    {
        // 尝试提取JSON
        preg_match('/\{.*\}/s', $content, $matches);
        
        if (!empty($matches[0])) {
            $result = json_decode($matches[0], true);
            if ($result) {
                return [
                    'ai_score' => $result['overall_score'] ?? 60,
                    'ai_decision' => $this->getDecisionByScore($result['overall_score'] ?? 60),
                    'ai_reason' => $result['analysis_reason'] ?? '系统自动评估',
                    'risk_level' => $result['risk_level'] ?? 'medium',
                    'max_loan_amount' => $result['max_loan_amount'] ?? 30000,
                    'details' => $result
                ];
            }
        }
        
        return $this->getDefaultRiskResult();
    }
    
    /**
     * 根据评分获取决策
     */
    private function getDecisionByScore($score)
    {
        if ($score >= 80) {
            return 'pass';
        } elseif ($score >= 60) {
            return 'manual';
        } else {
            return 'reject';
        }
    }
    
    /**
     * 获取默认风险评估结果
     */
    private function getDefaultRiskResult()
    {
        return [
            'ai_score' => 60,
            'ai_decision' => 'manual',
            'ai_reason' => '系统评估，建议人工审核',
            'risk_level' => 'medium',
            'max_loan_amount' => 30000,
            'details' => []
        ];
    }
    
    /**
     * 批量风险评估
     */
    public function batchAssessRisk($customersData)
    {
        $results = [];
        
        foreach ($customersData as $customerData) {
            $results[] = $this->assessCustomerRisk($customerData);
        }
        
        return $results;
    }

    /**
     * 获取客户风险评估历史
     */
    public function getRiskHistory($customerId, $page = 1, $limit = 20)
    {
        try {
            // 模拟风险评估历史数据
            $mockData = [
                [
                    'id' => 1,
                    'customer_id' => $customerId,
                    'risk_score' => 85,
                    'risk_level' => 'medium',
                    'assessment_time' => date('Y-m-d H:i:s', time() - 86400),
                    'factors' => [
                        'income_stability' => 80,
                        'credit_history' => 90,
                        'debt_ratio' => 75
                    ]
                ],
                [
                    'id' => 2,
                    'customer_id' => $customerId,
                    'risk_score' => 78,
                    'risk_level' => 'medium',
                    'assessment_time' => date('Y-m-d H:i:s', time() - 172800),
                    'factors' => [
                        'income_stability' => 75,
                        'credit_history' => 85,
                        'debt_ratio' => 70
                    ]
                ]
            ];

            $total = count($mockData);
            $offset = ($page - 1) * $limit;
            $list = array_slice($mockData, $offset, $limit);

            return [
                'success' => true,
                'data' => [
                    'list' => $list,
                    'total' => $total
                ]
            ];

        } catch (\Exception $e) {
            Log::error('获取风险评估历史失败: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => '获取风险评估历史失败: ' . $e->getMessage()
            ];
        }
    }
}
