<?php
declare(strict_types=1);

namespace app\admin\controller;

use app\common\controller\BaseController;
use app\common\service\ApiDocumentationService;
use app\common\service\StandardResponseService;
use think\Response;

/**
 * 增强版API文档控制器
 */
class ApiDocController extends BaseController
{
    /**
     * 显示Swagger UI文档页面
     */
    public function index(): Response
    {
        try {
            $html = ApiDocumentationService::generateSwaggerUI();
            return response($html, 200, ['Content-Type' => 'text/html']);
        } catch (\Exception $e) {
            return StandardResponseService::serverError('文档生成失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取API文档JSON数据
     */
    public function json(): Response
    {
        try {
            $documentation = ApiDocumentationService::generateFullDocumentation();
            return StandardResponseService::success($documentation, '获取API文档成功');
        } catch (\Exception $e) {
            return StandardResponseService::serverError('获取API文档失败: ' . $e->getMessage());
        }
    }

    /**
     * API文档概览（兼容旧版本）
     */
    public function overview(): Response
    {
        $apiGroups = [
            'auth' => [
                'name' => '认证接口',
                'description' => '用户登录、注册、token验证等接口',
                'apis' => [
                    ['method' => 'POST', 'path' => '/api/auth/login', 'name' => '用户登录'],
                    ['method' => 'POST', 'path' => '/api/auth/register', 'name' => '用户注册'],
                    ['method' => 'POST', 'path' => '/api/auth/logout', 'name' => '用户登出'],
                    ['method' => 'GET', 'path' => '/api/auth/profile', 'name' => '获取用户信息'],
                    ['method' => 'PUT', 'path' => '/api/auth/profile', 'name' => '更新用户信息'],
                ]
            ],
            'customer' => [
                'name' => '客户管理',
                'description' => '客户信息管理、风险评估等接口',
                'apis' => [
                    ['method' => 'GET', 'path' => '/api/customer/list', 'name' => '客户列表'],
                    ['method' => 'GET', 'path' => '/api/customer/detail/{id}', 'name' => '客户详情'],
                    ['method' => 'POST', 'path' => '/api/customer/create', 'name' => '创建客户'],
                    ['method' => 'PUT', 'path' => '/api/customer/update/{id}', 'name' => '更新客户'],
                    ['method' => 'DELETE', 'path' => '/api/customer/delete/{id}', 'name' => '删除客户'],
                    ['method' => 'POST', 'path' => '/api/customer/risk-assess/{id}', 'name' => '风险评估'],
                ]
            ],
            'loan' => [
                'name' => '借款管理',
                'description' => '借款申请、审核、放款等接口',
                'apis' => [
                    ['method' => 'GET', 'path' => '/api/loan/applications', 'name' => '借款申请列表'],
                    ['method' => 'GET', 'path' => '/api/loan/application/{id}', 'name' => '借款申请详情'],
                    ['method' => 'POST', 'path' => '/api/loan/apply', 'name' => '提交借款申请'],
                    ['method' => 'POST', 'path' => '/api/loan/review/{id}', 'name' => '审核借款申请'],
                    ['method' => 'POST', 'path' => '/api/loan/approve/{id}', 'name' => '批准借款'],
                    ['method' => 'POST', 'path' => '/api/loan/reject/{id}', 'name' => '拒绝借款'],
                    ['method' => 'GET', 'path' => '/api/loan/list', 'name' => '借款记录列表'],
                ]
            ],
            'repayment' => [
                'name' => '还款管理',
                'description' => '还款记录、还款计划等接口',
                'apis' => [
                    ['method' => 'GET', 'path' => '/api/repayment/list', 'name' => '还款记录列表'],
                    ['method' => 'GET', 'path' => '/api/repayment/plan/{loan_id}', 'name' => '还款计划'],
                    ['method' => 'POST', 'path' => '/api/repayment/pay', 'name' => '执行还款'],
                    ['method' => 'GET', 'path' => '/api/repayment/overdue', 'name' => '逾期列表'],
                ]
            ],
            'collection' => [
                'name' => '催收管理',
                'description' => '催收任务、催收记录等接口',
                'apis' => [
                    ['method' => 'GET', 'path' => '/api/collection/tasks', 'name' => '催收任务列表'],
                    ['method' => 'GET', 'path' => '/api/collection/task/{id}', 'name' => '催收任务详情'],
                    ['method' => 'POST', 'path' => '/api/collection/assign', 'name' => '分配催收任务'],
                    ['method' => 'POST', 'path' => '/api/collection/record', 'name' => '添加催收记录'],
                    ['method' => 'PUT', 'path' => '/api/collection/task/{id}', 'name' => '更新催收任务'],
                ]
            ],
            'agent' => [
                'name' => '代理管理',
                'description' => '代理信息、佣金管理等接口',
                'apis' => [
                    ['method' => 'GET', 'path' => '/api/agent/list', 'name' => '代理列表'],
                    ['method' => 'GET', 'path' => '/api/agent/detail/{id}', 'name' => '代理详情'],
                    ['method' => 'GET', 'path' => '/api/agent/commission/{id}', 'name' => '代理佣金'],
                    ['method' => 'POST', 'path' => '/api/agent/withdraw', 'name' => '申请提现'],
                    ['method' => 'GET', 'path' => '/api/agent/customers/{id}', 'name' => '代理客户列表'],
                ]
            ],
            'investor' => [
                'name' => '投资人管理',
                'description' => '投资人信息、查询记录等接口',
                'apis' => [
                    ['method' => 'GET', 'path' => '/api/investor/list', 'name' => '投资人列表'],
                    ['method' => 'GET', 'path' => '/api/investor/detail/{id}', 'name' => '投资人详情'],
                    ['method' => 'POST', 'path' => '/api/investor/query', 'name' => '查询客户信息'],
                    ['method' => 'GET', 'path' => '/api/investor/queries/{id}', 'name' => '查询记录'],
                ]
            ],
            'report' => [
                'name' => '报表统计',
                'description' => '各类统计报表接口',
                'apis' => [
                    ['method' => 'GET', 'path' => '/api/report/dashboard', 'name' => '仪表板数据'],
                    ['method' => 'GET', 'path' => '/api/report/loan-stats', 'name' => '借款统计'],
                    ['method' => 'GET', 'path' => '/api/report/repayment-stats', 'name' => '还款统计'],
                    ['method' => 'GET', 'path' => '/api/report/collection-stats', 'name' => '催收统计'],
                    ['method' => 'GET', 'path' => '/api/report/agent-stats', 'name' => '代理统计'],
                    ['method' => 'POST', 'path' => '/api/report/export', 'name' => '导出报表'],
                ]
            ],
            'system' => [
                'name' => '系统管理',
                'description' => '系统配置、日志管理等接口',
                'apis' => [
                    ['method' => 'GET', 'path' => '/api/system/config', 'name' => '系统配置'],
                    ['method' => 'PUT', 'path' => '/api/system/config', 'name' => '更新配置'],
                    ['method' => 'GET', 'path' => '/api/system/logs', 'name' => '系统日志'],
                    ['method' => 'GET', 'path' => '/api/system/monitor', 'name' => '系统监控'],
                ]
            ]
        ];

        return json([
            'code' => 200,
            'message' => 'success',
            'data' => [
                'title' => '民间空放贷后管理系统 API 文档',
                'version' => '1.0.0',
                'description' => '提供完整的API接口文档，包含认证、客户管理、借款管理、还款管理、催收管理等模块',
                'base_url' => request()->domain(),
                'groups' => $apiGroups
            ]
        ]);
    }

    /**
     * 获取API详细信息
     */
    public function detail(): Response
    {
        $path = $this->request->param('path', '');
        $method = $this->request->param('method', 'GET');

        // 这里可以根据路径和方法返回详细的API文档
        $apiDetails = $this->getApiDetails($path, $method);

        return json([
            'code' => 200,
            'message' => 'success',
            'data' => $apiDetails
        ]);
    }

    /**
     * 获取API详细信息
     */
    private function getApiDetails(string $path, string $method): array
    {
        // 示例API详情
        return [
            'path' => $path,
            'method' => $method,
            'name' => 'API名称',
            'description' => 'API描述',
            'parameters' => [
                [
                    'name' => 'id',
                    'type' => 'integer',
                    'required' => true,
                    'description' => 'ID参数'
                ]
            ],
            'request_example' => [
                'id' => 1,
                'name' => '示例'
            ],
            'response_example' => [
                'code' => 200,
                'message' => 'success',
                'data' => []
            ]
        ];
    }

    /**
     * API测试工具
     */
    public function test(): Response
    {
        return json([
            'code' => 200,
            'message' => 'success',
            'data' => [
                'message' => 'API测试工具页面',
                'tools' => [
                    'postman_collection' => '/api/doc/postman',
                    'swagger_ui' => '/api/doc/swagger',
                    'online_test' => '/api/doc/test-ui'
                ]
            ]
        ]);
    }

    /**
     * 生成Postman集合
     */
    public function postman(): Response
    {
        $collection = [
            'info' => [
                'name' => '民间空放贷后管理系统 API',
                'description' => 'API接口集合',
                'version' => '1.0.0'
            ],
            'item' => [
                // 这里可以生成完整的Postman集合
            ]
        ];

        return json($collection);
    }

    /**
     * Swagger文档
     */
    public function swagger(): Response
    {
        $swagger = [
            'openapi' => '3.0.0',
            'info' => [
                'title' => '民间空放贷后管理系统 API',
                'description' => 'API接口文档',
                'version' => '1.0.0'
            ],
            'servers' => [
                ['url' => request()->domain()]
            ],
            'paths' => [
                // 这里可以生成完整的Swagger文档
            ]
        ];

        return json($swagger);
    }
}
