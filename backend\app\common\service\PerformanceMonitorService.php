<?php
declare(strict_types=1);

namespace app\common\service;

use think\facade\Db;
use think\facade\Cache;
use think\facade\Log;
use think\facade\Config;

/**
 * 性能监控服务
 * 监控系统性能指标，收集性能数据，提供性能分析
 */
class PerformanceMonitorService
{
    /**
     * 性能指标类型
     */
    const METRIC_RESPONSE_TIME = 'response_time';
    const METRIC_MEMORY_USAGE = 'memory_usage';
    const METRIC_CPU_USAGE = 'cpu_usage';
    const METRIC_DATABASE_QUERY = 'database_query';
    const METRIC_CACHE_HIT_RATE = 'cache_hit_rate';
    const METRIC_ERROR_RATE = 'error_rate';
    const METRIC_THROUGHPUT = 'throughput';
    const METRIC_CONCURRENT_USERS = 'concurrent_users';

    /**
     * 性能阈值配置
     */
    private static $thresholds = [
        self::METRIC_RESPONSE_TIME => 2000,    // 2秒
        self::METRIC_MEMORY_USAGE => 80,       // 80%
        self::METRIC_CPU_USAGE => 80,          // 80%
        self::METRIC_DATABASE_QUERY => 1000,   // 1秒
        self::METRIC_CACHE_HIT_RATE => 90,     // 90%
        self::METRIC_ERROR_RATE => 5,          // 5%
        self::METRIC_THROUGHPUT => 100,        // 100 req/s
        self::METRIC_CONCURRENT_USERS => 1000, // 1000用户
    ];

    /**
     * 开始性能监控
     * @param string $identifier 监控标识
     * @return array 监控上下文
     */
    public static function startMonitoring(string $identifier): array
    {
        $context = [
            'identifier' => $identifier,
            'start_time' => microtime(true),
            'start_memory' => memory_get_usage(true),
            'start_peak_memory' => memory_get_peak_usage(true),
            'queries_before' => self::getQueryCount(),
        ];

        return $context;
    }

    /**
     * 结束性能监控
     * @param array $context 监控上下文
     * @param array $additional 额外数据
     * @return array 性能指标
     */
    public static function endMonitoring(array $context, array $additional = []): array
    {
        $endTime = microtime(true);
        $endMemory = memory_get_usage(true);
        $endPeakMemory = memory_get_peak_usage(true);
        $queriesAfter = self::getQueryCount();

        $metrics = [
            'identifier' => $context['identifier'],
            'response_time' => round(($endTime - $context['start_time']) * 1000, 2), // 毫秒
            'memory_usage' => $endMemory - $context['start_memory'],
            'peak_memory_usage' => $endPeakMemory - $context['start_peak_memory'],
            'query_count' => $queriesAfter - $context['queries_before'],
            'timestamp' => time(),
        ];

        // 合并额外数据
        $metrics = array_merge($metrics, $additional);

        // 记录性能指标
        self::recordMetrics($metrics);

        // 检查性能阈值
        self::checkThresholds($metrics);

        return $metrics;
    }

    /**
     * 记录性能指标
     * @param array $metrics 性能指标
     */
    public static function recordMetrics(array $metrics): void
    {
        try {
            // 记录到数据库
            Db::name('performance_metrics')->insert([
                'identifier' => $metrics['identifier'],
                'response_time' => $metrics['response_time'],
                'memory_usage' => $metrics['memory_usage'],
                'peak_memory_usage' => $metrics['peak_memory_usage'],
                'query_count' => $metrics['query_count'],
                'additional_data' => json_encode($metrics, JSON_UNESCAPED_UNICODE),
                'created_at' => date('Y-m-d H:i:s', $metrics['timestamp'])
            ]);

            // 记录到缓存（用于实时监控）
            $cacheKey = "performance_metrics:" . date('Y-m-d-H');
            $cachedMetrics = Cache::get($cacheKey, []);
            $cachedMetrics[] = $metrics;
            
            // 只保留最近1000条记录
            if (count($cachedMetrics) > 1000) {
                $cachedMetrics = array_slice($cachedMetrics, -1000);
            }
            
            Cache::set($cacheKey, $cachedMetrics, 3600);

        } catch (\Exception $e) {
            Log::error('Failed to record performance metrics: ' . $e->getMessage());
        }
    }

    /**
     * 获取系统性能概览
     * @return array 性能概览
     */
    public static function getPerformanceOverview(): array
    {
        $overview = [
            'system' => self::getSystemMetrics(),
            'application' => self::getApplicationMetrics(),
            'database' => self::getDatabaseMetrics(),
            'cache' => self::getCacheMetrics(),
            'alerts' => self::getActiveAlerts(),
        ];

        return $overview;
    }

    /**
     * 获取系统指标
     * @return array 系统指标
     */
    public static function getSystemMetrics(): array
    {
        $metrics = [
            'memory' => [
                'used' => memory_get_usage(true),
                'peak' => memory_get_peak_usage(true),
                'limit' => self::getMemoryLimit(),
                'usage_percentage' => round((memory_get_usage(true) / self::getMemoryLimit()) * 100, 2),
            ],
            'cpu' => [
                'usage' => self::getCpuUsage(),
                'load_average' => self::getLoadAverage(),
            ],
            'disk' => [
                'free_space' => disk_free_space('.'),
                'total_space' => disk_total_space('.'),
                'usage_percentage' => round((1 - disk_free_space('.') / disk_total_space('.')) * 100, 2),
            ],
            'uptime' => self::getSystemUptime(),
        ];

        return $metrics;
    }

    /**
     * 获取应用指标
     * @return array 应用指标
     */
    public static function getApplicationMetrics(): array
    {
        $now = time();
        $oneHourAgo = $now - 3600;

        // 从数据库获取最近1小时的性能数据
        $recentMetrics = Db::name('performance_metrics')
            ->where('created_at', '>=', date('Y-m-d H:i:s', $oneHourAgo))
            ->select()
            ->toArray();

        if (empty($recentMetrics)) {
            return [
                'avg_response_time' => 0,
                'max_response_time' => 0,
                'min_response_time' => 0,
                'total_requests' => 0,
                'error_rate' => 0,
                'throughput' => 0,
            ];
        }

        $responseTimes = array_column($recentMetrics, 'response_time');
        $totalRequests = count($recentMetrics);

        $metrics = [
            'avg_response_time' => round(array_sum($responseTimes) / $totalRequests, 2),
            'max_response_time' => max($responseTimes),
            'min_response_time' => min($responseTimes),
            'total_requests' => $totalRequests,
            'error_rate' => self::calculateErrorRate($recentMetrics),
            'throughput' => round($totalRequests / 3600, 2), // 每秒请求数
        ];

        return $metrics;
    }

    /**
     * 获取数据库指标
     * @return array 数据库指标
     */
    public static function getDatabaseMetrics(): array
    {
        try {
            // 获取数据库状态
            $status = Db::query('SHOW STATUS');
            $statusMap = [];
            foreach ($status as $item) {
                $statusMap[$item['Variable_name']] = $item['Value'];
            }

            // 获取数据库大小
            $dbSize = Db::query("
                SELECT 
                    SUM(data_length + index_length) / 1024 / 1024 as size_mb
                FROM information_schema.tables 
                WHERE table_schema = DATABASE()
            ");

            $metrics = [
                'connections' => [
                    'current' => (int)($statusMap['Threads_connected'] ?? 0),
                    'max_used' => (int)($statusMap['Max_used_connections'] ?? 0),
                    'total' => (int)($statusMap['Connections'] ?? 0),
                ],
                'queries' => [
                    'total' => (int)($statusMap['Queries'] ?? 0),
                    'slow' => (int)($statusMap['Slow_queries'] ?? 0),
                    'qps' => self::calculateQPS($statusMap),
                ],
                'innodb' => [
                    'buffer_pool_hit_rate' => self::calculateBufferPoolHitRate($statusMap),
                    'buffer_pool_size' => (int)($statusMap['Innodb_buffer_pool_pages_total'] ?? 0),
                    'buffer_pool_free' => (int)($statusMap['Innodb_buffer_pool_pages_free'] ?? 0),
                ],
                'size_mb' => round($dbSize[0]['size_mb'] ?? 0, 2),
            ];

            return $metrics;
        } catch (\Exception $e) {
            Log::error('Failed to get database metrics: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * 获取缓存指标
     * @return array 缓存指标
     */
    public static function getCacheMetrics(): array
    {
        try {
            // 这里假设使用Redis作为缓存
            $redis = Cache::store('redis')->handler();
            $info = $redis->info();

            $metrics = [
                'memory' => [
                    'used' => $info['used_memory'] ?? 0,
                    'peak' => $info['used_memory_peak'] ?? 0,
                    'rss' => $info['used_memory_rss'] ?? 0,
                ],
                'stats' => [
                    'hits' => $info['keyspace_hits'] ?? 0,
                    'misses' => $info['keyspace_misses'] ?? 0,
                    'hit_rate' => self::calculateCacheHitRate($info),
                ],
                'connections' => [
                    'current' => $info['connected_clients'] ?? 0,
                    'total' => $info['total_connections_received'] ?? 0,
                ],
                'keys' => $info['db0']['keys'] ?? 0,
            ];

            return $metrics;
        } catch (\Exception $e) {
            Log::error('Failed to get cache metrics: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * 获取活跃告警
     * @return array 活跃告警
     */
    public static function getActiveAlerts(): array
    {
        $alerts = [];
        $metrics = self::getSystemMetrics();

        // 检查内存使用率
        if ($metrics['memory']['usage_percentage'] > self::$thresholds[self::METRIC_MEMORY_USAGE]) {
            $alerts[] = [
                'type' => 'memory',
                'level' => 'warning',
                'message' => "内存使用率过高: {$metrics['memory']['usage_percentage']}%",
                'threshold' => self::$thresholds[self::METRIC_MEMORY_USAGE],
                'current' => $metrics['memory']['usage_percentage'],
            ];
        }

        // 检查磁盘使用率
        if ($metrics['disk']['usage_percentage'] > 90) {
            $alerts[] = [
                'type' => 'disk',
                'level' => 'critical',
                'message' => "磁盘使用率过高: {$metrics['disk']['usage_percentage']}%",
                'threshold' => 90,
                'current' => $metrics['disk']['usage_percentage'],
            ];
        }

        return $alerts;
    }

    /**
     * 生成性能报告
     * @param string $startDate 开始日期
     * @param string $endDate 结束日期
     * @return array 性能报告
     */
    public static function generatePerformanceReport(string $startDate, string $endDate): array
    {
        $metrics = Db::name('performance_metrics')
            ->where('created_at', 'between', [$startDate, $endDate])
            ->select()
            ->toArray();

        if (empty($metrics)) {
            return ['error' => '指定时间范围内没有性能数据'];
        }

        $report = [
            'period' => ['start' => $startDate, 'end' => $endDate],
            'summary' => self::calculateSummaryStats($metrics),
            'trends' => self::calculateTrends($metrics),
            'top_slow_requests' => self::getTopSlowRequests($metrics),
            'recommendations' => self::generateRecommendations($metrics),
        ];

        return $report;
    }

    /**
     * 检查性能阈值
     * @param array $metrics 性能指标
     */
    private static function checkThresholds(array $metrics): void
    {
        // 检查响应时间
        if ($metrics['response_time'] > self::$thresholds[self::METRIC_RESPONSE_TIME]) {
            self::triggerAlert(self::METRIC_RESPONSE_TIME, $metrics['response_time'], $metrics);
        }

        // 检查内存使用
        $memoryUsage = (memory_get_usage(true) / self::getMemoryLimit()) * 100;
        if ($memoryUsage > self::$thresholds[self::METRIC_MEMORY_USAGE]) {
            self::triggerAlert(self::METRIC_MEMORY_USAGE, $memoryUsage, $metrics);
        }

        // 检查查询数量
        if ($metrics['query_count'] > 50) { // 单次请求查询数量过多
            self::triggerAlert(self::METRIC_DATABASE_QUERY, $metrics['query_count'], $metrics);
        }
    }

    /**
     * 触发性能告警
     * @param string $metric 指标类型
     * @param float $value 当前值
     * @param array $context 上下文信息
     */
    private static function triggerAlert(string $metric, float $value, array $context): void
    {
        $alert = [
            'metric' => $metric,
            'value' => $value,
            'threshold' => self::$thresholds[$metric],
            'context' => $context,
            'timestamp' => time(),
        ];

        // 记录告警
        Log::warning("Performance Alert: {$metric}", $alert);

        // 可以在这里集成告警通知系统
        // self::sendAlert($alert);
    }

    /**
     * 获取查询数量
     * @return int 查询数量
     */
    private static function getQueryCount(): int
    {
        // 这里需要根据实际的数据库连接实现
        return 0;
    }

    /**
     * 获取内存限制
     * @return int 内存限制（字节）
     */
    private static function getMemoryLimit(): int
    {
        $limit = ini_get('memory_limit');
        if ($limit == -1) {
            return PHP_INT_MAX;
        }
        
        return self::convertToBytes($limit);
    }

    /**
     * 转换内存大小为字节
     * @param string $size 内存大小
     * @return int 字节数
     */
    private static function convertToBytes(string $size): int
    {
        $unit = strtolower(substr($size, -1));
        $value = (int)substr($size, 0, -1);
        
        switch ($unit) {
            case 'g':
                return $value * 1024 * 1024 * 1024;
            case 'm':
                return $value * 1024 * 1024;
            case 'k':
                return $value * 1024;
            default:
                return (int)$size;
        }
    }

    /**
     * 获取CPU使用率
     * @return float CPU使用率
     */
    private static function getCpuUsage(): float
    {
        // 简化实现，实际应该使用系统命令获取
        return 0.0;
    }

    /**
     * 获取系统负载
     * @return array 系统负载
     */
    private static function getLoadAverage(): array
    {
        if (function_exists('sys_getloadavg')) {
            return sys_getloadavg();
        }
        
        return [0, 0, 0];
    }

    /**
     * 获取系统运行时间
     * @return int 运行时间（秒）
     */
    private static function getSystemUptime(): int
    {
        // 简化实现
        return 0;
    }

    /**
     * 计算错误率
     * @param array $metrics 指标数据
     * @return float 错误率
     */
    private static function calculateErrorRate(array $metrics): float
    {
        // 这里需要根据实际的错误记录实现
        return 0.0;
    }

    /**
     * 计算QPS
     * @param array $statusMap 状态映射
     * @return float QPS
     */
    private static function calculateQPS(array $statusMap): float
    {
        $uptime = (int)($statusMap['Uptime'] ?? 1);
        $queries = (int)($statusMap['Queries'] ?? 0);
        
        return round($queries / $uptime, 2);
    }

    /**
     * 计算缓冲池命中率
     * @param array $statusMap 状态映射
     * @return float 命中率
     */
    private static function calculateBufferPoolHitRate(array $statusMap): float
    {
        $reads = (int)($statusMap['Innodb_buffer_pool_read_requests'] ?? 0);
        $misses = (int)($statusMap['Innodb_buffer_pool_reads'] ?? 0);
        
        if ($reads == 0) {
            return 100.0;
        }
        
        return round((1 - $misses / $reads) * 100, 2);
    }

    /**
     * 计算缓存命中率
     * @param array $info Redis信息
     * @return float 命中率
     */
    private static function calculateCacheHitRate(array $info): float
    {
        $hits = (int)($info['keyspace_hits'] ?? 0);
        $misses = (int)($info['keyspace_misses'] ?? 0);
        $total = $hits + $misses;
        
        if ($total == 0) {
            return 100.0;
        }
        
        return round(($hits / $total) * 100, 2);
    }

    /**
     * 计算汇总统计
     * @param array $metrics 指标数据
     * @return array 汇总统计
     */
    private static function calculateSummaryStats(array $metrics): array
    {
        $responseTimes = array_column($metrics, 'response_time');
        $memoryUsages = array_column($metrics, 'memory_usage');
        $queryCounts = array_column($metrics, 'query_count');

        return [
            'total_requests' => count($metrics),
            'avg_response_time' => round(array_sum($responseTimes) / count($responseTimes), 2),
            'max_response_time' => max($responseTimes),
            'min_response_time' => min($responseTimes),
            'avg_memory_usage' => round(array_sum($memoryUsages) / count($memoryUsages), 2),
            'max_memory_usage' => max($memoryUsages),
            'avg_query_count' => round(array_sum($queryCounts) / count($queryCounts), 2),
            'max_query_count' => max($queryCounts),
        ];
    }

    /**
     * 计算趋势
     * @param array $metrics 指标数据
     * @return array 趋势数据
     */
    private static function calculateTrends(array $metrics): array
    {
        // 简化实现，实际应该计算时间序列趋势
        return [
            'response_time_trend' => 'stable',
            'memory_usage_trend' => 'stable',
            'query_count_trend' => 'stable',
        ];
    }

    /**
     * 获取最慢的请求
     * @param array $metrics 指标数据
     * @return array 最慢请求
     */
    private static function getTopSlowRequests(array $metrics): array
    {
        usort($metrics, function($a, $b) {
            return $b['response_time'] <=> $a['response_time'];
        });

        return array_slice($metrics, 0, 10);
    }

    /**
     * 生成优化建议
     * @param array $metrics 指标数据
     * @return array 优化建议
     */
    private static function generateRecommendations(array $metrics): array
    {
        $recommendations = [];
        $summary = self::calculateSummaryStats($metrics);

        if ($summary['avg_response_time'] > 1000) {
            $recommendations[] = '平均响应时间较长，建议优化数据库查询和缓存策略';
        }

        if ($summary['max_query_count'] > 50) {
            $recommendations[] = '单次请求查询数量过多，建议优化数据库查询逻辑';
        }

        if ($summary['avg_memory_usage'] > 50 * 1024 * 1024) {
            $recommendations[] = '内存使用量较大，建议优化内存使用和垃圾回收';
        }

        return $recommendations;
    }
}
