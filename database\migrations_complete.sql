-- 民间空放贷后管理系统 - 完整数据库迁移脚本
-- 统一数据库名为: daihou
-- 包含所有新增表和字段

-- 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS daihou DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE daihou;

-- 1. 核心业务表（已存在的表结构保持不变，只添加新字段）

-- 用户表（增加RBAC支持）
CREATE TABLE IF NOT EXISTS users (
    id INT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) NOT NULL UNIQUE COMMENT 'username',
    password VARCHAR(255) NOT NULL COMMENT 'password',
    real_name VARCHAR(50) NOT NULL COMMENT 'real name',
    phone VARCHAR(20) NOT NULL COMMENT 'phone',
    email VARCHAR(100) COMMENT 'email',
    role ENUM('admin','risk_controller','finance','collection','agent','investor') NOT NULL COMMENT 'role',
    department VARCHAR(50) COMMENT 'department',
    permissions JSON COMMENT 'permissions',
    status ENUM('active','inactive','locked') DEFAULT 'active' COMMENT 'status',
    last_login_at TIMESTAMP NULL COMMENT 'last login time',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_username (username),
    INDEX idx_role (role),
    INDEX idx_status (status)
) COMMENT='users table';

-- 客户表（增加tags字段）
CREATE TABLE IF NOT EXISTS customers (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(50) NOT NULL COMMENT 'customer name',
    phone VARCHAR(20) NOT NULL UNIQUE COMMENT 'phone',
    id_card VARCHAR(18) NOT NULL UNIQUE COMMENT 'id card',
    age INT COMMENT 'age',
    gender ENUM('male','female') COMMENT 'gender',
    address TEXT COMMENT 'address',
    industry VARCHAR(50) COMMENT 'industry',
    monthly_income DECIMAL(10,2) COMMENT 'monthly income',
    emergency_contact VARCHAR(50) COMMENT 'emergency contact',
    emergency_phone VARCHAR(20) COMMENT 'emergency phone',
    risk_score INT DEFAULT 0 COMMENT 'risk score',
    total_loan_amount DECIMAL(12,2) DEFAULT 0 COMMENT 'total loan amount',
    total_repaid_amount DECIMAL(12,2) DEFAULT 0 COMMENT 'total repaid amount',
    active_loans_count INT DEFAULT 0 COMMENT 'active loans count',
    last_loan_date DATE COMMENT 'last loan date',
    last_repayment_date DATE COMMENT 'last repayment date',
    customer_profit DECIMAL(10,2) DEFAULT 0 COMMENT 'customer profit',
    risk_assessment_score INT COMMENT 'risk assessment score',
    status ENUM('active','blacklist','suspended') DEFAULT 'active' COMMENT 'status',
    tags JSON COMMENT 'customer tags for investor',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_phone (phone),
    INDEX idx_id_card (id_card),
    INDEX idx_status (status)
) COMMENT='customers table';

-- 放款登记记录表
CREATE TABLE IF NOT EXISTS loan_disbursement_records (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    business_flow_no VARCHAR(32) NOT NULL UNIQUE COMMENT 'business flow no',
    loan_date DATE NOT NULL COMMENT 'loan date',
    customer_name VARCHAR(50) NOT NULL COMMENT 'customer name',
    customer_id BIGINT UNSIGNED NOT NULL COMMENT 'customer id',
    risk_controller_name VARCHAR(50) NOT NULL COMMENT 'risk controller name',
    risk_controller_id INT NOT NULL COMMENT 'risk controller id',
    loan_amount DECIMAL(10,2) NOT NULL COMMENT 'loan amount',
    
    -- 还款周期设置
    repayment_type ENUM('daily','weekly','monthly') NOT NULL COMMENT 'repayment type',
    repayment_cycle INT NOT NULL COMMENT 'repayment cycle',
    repayment_frequency VARCHAR(20) COMMENT 'repayment frequency',
    
    -- 中介返点
    agent_id INT COMMENT 'agent id',
    agent_name VARCHAR(50) COMMENT 'agent name',
    agent_commission_rate DECIMAL(5,2) COMMENT 'agent commission rate',
    agent_commission_amount DECIMAL(10,2) COMMENT 'agent commission amount',
    commission_type ENUM('front','back','both') DEFAULT 'front' COMMENT 'commission type',
    front_commission_amount DECIMAL(10,2) DEFAULT 0 COMMENT 'front commission amount',
    back_commission_amount DECIMAL(10,2) DEFAULT 0 COMMENT 'back commission amount',
    back_commission_paid TINYINT DEFAULT 0 COMMENT 'back commission paid',
    
    -- 费用管理
    platform_fee DECIMAL(10,2) NOT NULL DEFAULT 0 COMMENT 'platform fee',
    overdue_fee DECIMAL(10,2) DEFAULT 0 COMMENT 'overdue fee',
    
    -- 财务统计
    total_repaid_amount DECIMAL(10,2) DEFAULT 0 COMMENT 'total repaid amount',
    remaining_amount DECIMAL(10,2) NOT NULL COMMENT 'remaining amount',
    customer_profit DECIMAL(10,2) DEFAULT 0 COMMENT 'customer profit',
    
    -- 状态管理
    status ENUM('active','completed','overdue','settled') DEFAULT 'active' COMMENT 'status',
    created_by INT NOT NULL COMMENT 'created by',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_customer_id (customer_id),
    INDEX idx_agent_id (agent_id),
    INDEX idx_loan_date (loan_date),
    INDEX idx_status (status),
    FOREIGN KEY (customer_id) REFERENCES customers(id),
    FOREIGN KEY (created_by) REFERENCES users(id)
) COMMENT='loan disbursement records';

-- 还款计划详细表
CREATE TABLE IF NOT EXISTS repayment_schedule_details (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    disbursement_record_id BIGINT UNSIGNED NOT NULL COMMENT 'disbursement record id',
    period_number INT NOT NULL COMMENT 'period number',
    due_date DATE NOT NULL COMMENT 'due date',
    due_amount DECIMAL(10,2) NOT NULL COMMENT 'due amount',
    paid_amount DECIMAL(10,2) DEFAULT 0 COMMENT 'paid amount',
    remaining_amount DECIMAL(10,2) NOT NULL COMMENT 'remaining amount',
    status ENUM('pending','paid','overdue','partial') DEFAULT 'pending' COMMENT 'status',
    overdue_days INT DEFAULT 0 COMMENT 'overdue days',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_disbursement_record_id (disbursement_record_id),
    INDEX idx_due_date (due_date),
    INDEX idx_status (status),
    FOREIGN KEY (disbursement_record_id) REFERENCES loan_disbursement_records(id)
) COMMENT='repayment schedule details';

-- 还款登记记录表
CREATE TABLE IF NOT EXISTS repayment_records (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    business_flow_no VARCHAR(32) NOT NULL UNIQUE COMMENT 'business flow no',
    disbursement_record_id BIGINT UNSIGNED NOT NULL COMMENT 'disbursement record id',
    customer_id BIGINT UNSIGNED NOT NULL COMMENT 'customer id',
    customer_name VARCHAR(50) NOT NULL COMMENT 'customer name',
    
    -- 还款信息
    repayment_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'repayment date',
    repayment_method ENUM('wechat','alipay','bank_card','cash','other') NOT NULL COMMENT 'repayment method',
    repayment_amount DECIMAL(10,2) NOT NULL COMMENT 'repayment amount',
    repayment_type ENUM('normal','partial','negotiated','overdue_fee','early_settlement') NOT NULL COMMENT 'repayment type',
    
    -- 附件和备注
    payment_screenshot VARCHAR(500) COMMENT 'payment screenshot',
    remark TEXT COMMENT 'remark',
    
    -- 逾期处理
    overdue_days INT DEFAULT 0 COMMENT 'overdue days',
    overdue_fee DECIMAL(10,2) DEFAULT 0 COMMENT 'overdue fee',
    
    -- 状态管理
    status ENUM('pending','confirmed','cancelled') DEFAULT 'confirmed' COMMENT 'status',
    created_by INT NOT NULL COMMENT 'created by',
    confirmed_by INT COMMENT 'confirmed by',
    confirmed_at TIMESTAMP NULL COMMENT 'confirmed at',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_disbursement_record_id (disbursement_record_id),
    INDEX idx_customer_id (customer_id),
    INDEX idx_repayment_date (repayment_date),
    INDEX idx_status (status),
    FOREIGN KEY (disbursement_record_id) REFERENCES loan_disbursement_records(id),
    FOREIGN KEY (customer_id) REFERENCES customers(id),
    FOREIGN KEY (created_by) REFERENCES users(id)
) COMMENT='repayment records';

-- 2. RBAC权限管理表

-- 角色表
CREATE TABLE IF NOT EXISTS roles (
    id INT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(50) NOT NULL UNIQUE COMMENT 'role name',
    display_name VARCHAR(100) COMMENT 'display name',
    description TEXT COMMENT 'description',
    is_system TINYINT DEFAULT 0 COMMENT 'is system role',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_name (name)
) COMMENT='roles table';

-- 权限表
CREATE TABLE IF NOT EXISTS permissions (
    id INT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL UNIQUE COMMENT 'permission name',
    display_name VARCHAR(100) COMMENT 'display name',
    description TEXT COMMENT 'description',
    module VARCHAR(50) COMMENT 'module',
    action VARCHAR(50) COMMENT 'action',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_name (name),
    INDEX idx_module (module)
) COMMENT='permissions table';

-- 用户角色关联表
CREATE TABLE IF NOT EXISTS user_roles (
    id INT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    user_id INT UNSIGNED NOT NULL COMMENT 'user id',
    role_id INT UNSIGNED NOT NULL COMMENT 'role id',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE KEY uk_user_role (user_id, role_id),
    INDEX idx_user_id (user_id),
    INDEX idx_role_id (role_id),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE
) COMMENT='user roles relation table';

-- 角色权限关联表
CREATE TABLE IF NOT EXISTS role_permissions (
    id INT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    role_id INT UNSIGNED NOT NULL COMMENT 'role id',
    permission_id INT UNSIGNED NOT NULL COMMENT 'permission id',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE KEY uk_role_permission (role_id, permission_id),
    INDEX idx_role_id (role_id),
    INDEX idx_permission_id (permission_id),
    FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE,
    FOREIGN KEY (permission_id) REFERENCES permissions(id) ON DELETE CASCADE
) COMMENT='role permissions relation table';

-- 3. 系统支持表

-- 系统日志表
CREATE TABLE IF NOT EXISTS system_logs (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    user_id INT UNSIGNED COMMENT 'user id',
    module VARCHAR(50) NOT NULL COMMENT 'module',
    action VARCHAR(100) NOT NULL COMMENT 'action',
    description TEXT COMMENT 'description',
    request_data JSON COMMENT 'request data',
    response_data JSON COMMENT 'response data',
    ip_address VARCHAR(45) COMMENT 'ip address',
    user_agent TEXT COMMENT 'user agent',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_user_id (user_id),
    INDEX idx_module (module),
    INDEX idx_action (action),
    INDEX idx_created_at (created_at),
    FOREIGN KEY (user_id) REFERENCES users(id)
) COMMENT='system logs table';

-- 行业黑名单表
CREATE TABLE IF NOT EXISTS industry_blacklist (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    type ENUM('phone','id_card','name','company','other') NOT NULL COMMENT 'blacklist type',
    value VARCHAR(255) NOT NULL COMMENT 'blacklist value',
    source ENUM('manual','auto','import','api') DEFAULT 'manual' COMMENT 'source',
    reason TEXT COMMENT 'reason',
    risk_level ENUM('low','medium','high','critical') DEFAULT 'medium' COMMENT 'risk level',
    effective_date DATE COMMENT 'effective date',
    expire_date DATE COMMENT 'expire date',
    is_active TINYINT DEFAULT 1 COMMENT 'is active',
    created_by INT UNSIGNED COMMENT 'created by',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_type_value (type, value),
    INDEX idx_value (value),
    INDEX idx_is_active (is_active),
    INDEX idx_risk_level (risk_level),
    FOREIGN KEY (created_by) REFERENCES users(id)
) COMMENT='industry blacklist table';

-- 风控规则表
CREATE TABLE IF NOT EXISTS risk_rules (
    id INT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    rule_name VARCHAR(100) NOT NULL COMMENT 'rule name',
    rule_type ENUM('phone','id_card','income','industry','blacklist','custom') NOT NULL COMMENT 'rule type',
    rule_condition JSON NOT NULL COMMENT 'rule condition',
    risk_score INT DEFAULT 0 COMMENT 'risk score',
    action ENUM('pass','warning','reject','manual_review') DEFAULT 'pass' COMMENT 'action',
    priority INT DEFAULT 0 COMMENT 'priority',
    is_active TINYINT DEFAULT 1 COMMENT 'is active',
    created_by INT UNSIGNED COMMENT 'created by',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_rule_type (rule_type),
    INDEX idx_is_active (is_active),
    INDEX idx_priority (priority),
    FOREIGN KEY (created_by) REFERENCES users(id)
) COMMENT='risk rules table';

-- 4. 多端配置表

-- 应用配置表
CREATE TABLE IF NOT EXISTS app_configs (
    id INT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    platform ENUM('web','miniprogram','app','h5') NOT NULL COMMENT 'platform',
    app_type ENUM('admin','customer','investor','agent') NOT NULL COMMENT 'app type',
    config_key VARCHAR(100) NOT NULL COMMENT 'config key',
    config_value TEXT COMMENT 'config value',
    config_type ENUM('string','number','boolean','json','array') DEFAULT 'string' COMMENT 'config type',
    description TEXT COMMENT 'description',
    is_active TINYINT DEFAULT 1 COMMENT 'is active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    UNIQUE KEY uk_platform_app_key (platform, app_type, config_key),
    INDEX idx_platform (platform),
    INDEX idx_app_type (app_type),
    INDEX idx_is_active (is_active)
) COMMENT='app configs table';

-- 小程序组件表
CREATE TABLE IF NOT EXISTS miniprogram_components (
    id INT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    component_name VARCHAR(100) NOT NULL COMMENT 'component name',
    component_type ENUM('layout','form','display','navigation','media','other') DEFAULT 'other' COMMENT 'component type',
    component_config JSON COMMENT 'component config',
    preview_image VARCHAR(255) COMMENT 'preview image',
    is_active TINYINT DEFAULT 1 COMMENT 'is active',
    sort_order INT DEFAULT 0 COMMENT 'sort order',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_component_type (component_type),
    INDEX idx_is_active (is_active),
    INDEX idx_sort_order (sort_order)
) COMMENT='miniprogram components table';

-- 小程序页面配置表
CREATE TABLE IF NOT EXISTS miniprogram_pages (
    id INT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    page_name VARCHAR(100) NOT NULL COMMENT 'page name',
    page_path VARCHAR(255) NOT NULL COMMENT 'page path',
    page_config JSON COMMENT 'page config',
    components JSON COMMENT 'components config',
    is_published TINYINT DEFAULT 0 COMMENT 'is published',
    created_by INT UNSIGNED COMMENT 'created by',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    UNIQUE KEY uk_page_path (page_path),
    INDEX idx_is_published (is_published),
    FOREIGN KEY (created_by) REFERENCES users(id)
) COMMENT='miniprogram pages table';

-- 5. 通知系统表

-- 通知记录表
CREATE TABLE IF NOT EXISTS notifications (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(255) NOT NULL COMMENT 'notification title',
    content TEXT NOT NULL COMMENT 'notification content',
    type ENUM('system','business','marketing','reminder') DEFAULT 'system' COMMENT 'notification type',
    level ENUM('info','warning','error','success') DEFAULT 'info' COMMENT 'notification level',
    send_type ENUM('immediate','scheduled','draft') DEFAULT 'immediate' COMMENT 'send type',
    target_type ENUM('all','role','user','custom') DEFAULT 'all' COMMENT 'target type',
    target_users JSON COMMENT 'target users',
    send_time TIMESTAMP NULL COMMENT 'scheduled send time',
    sent_count INT DEFAULT 0 COMMENT 'sent count',
    success_count INT DEFAULT 0 COMMENT 'success count',
    status ENUM('draft','sending','sent','failed') DEFAULT 'draft' COMMENT 'status',
    template_id VARCHAR(100) COMMENT 'template id',
    template_params JSON COMMENT 'template params',
    created_by INT UNSIGNED COMMENT 'created by',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_type (type),
    INDEX idx_status (status),
    INDEX idx_send_time (send_time),
    INDEX idx_created_by (created_by),
    FOREIGN KEY (created_by) REFERENCES users(id)
) COMMENT='notifications table';

-- 6. 文件管理表

-- 文件管理表
CREATE TABLE IF NOT EXISTS file_management (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    original_name VARCHAR(255) NOT NULL COMMENT 'original filename',
    saved_name VARCHAR(255) NOT NULL COMMENT 'saved filename',
    file_path VARCHAR(500) NOT NULL COMMENT 'file path',
    file_size BIGINT UNSIGNED COMMENT 'file size in bytes',
    file_type VARCHAR(100) COMMENT 'file type',
    mime_type VARCHAR(100) COMMENT 'mime type',
    file_hash VARCHAR(64) COMMENT 'file hash',
    business_type ENUM('avatar','document','screenshot','contract','other') DEFAULT 'other' COMMENT 'business type',
    business_id BIGINT UNSIGNED COMMENT 'related business id',
    uploaded_by INT UNSIGNED COMMENT 'uploaded by',
    is_public TINYINT DEFAULT 0 COMMENT 'is public accessible',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_business_type_id (business_type, business_id),
    INDEX idx_uploaded_by (uploaded_by),
    INDEX idx_file_hash (file_hash),
    FOREIGN KEY (uploaded_by) REFERENCES users(id)
) COMMENT='file management table';

-- 7. 初始数据插入

-- 插入初始用户
INSERT IGNORE INTO users (username, password, real_name, phone, role, status) VALUES 
('admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Administrator', '***********', 'admin', 'active'),
('finance001', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Finance Manager', '***********', 'finance', 'active'),
('risk001', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Risk Controller', '***********', 'risk_controller', 'active'),
('investor001', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Investor User', '***********', 'investor', 'active');

-- 插入初始角色
INSERT IGNORE INTO roles (name, display_name, description, is_system) VALUES
('admin', '系统管理员', '系统最高权限管理员', 1),
('finance', '财务人员', '负责财务管理相关功能', 1),
('risk_controller', '风控人员', '负责风险控制相关功能', 1),
('collection', '催收人员', '负责催收管理相关功能', 1),
('agent', '中介代理', '中介代理用户', 1),
('investor', '资方用户', '资方查询和管理用户', 1);

-- 插入基础权限
INSERT IGNORE INTO permissions (name, display_name, description, module, action) VALUES
-- 客户管理
('customer:view', '查看客户', '查看客户信息', 'customer', 'view'),
('customer:create', '创建客户', '创建新客户', 'customer', 'create'),
('customer:edit', '编辑客户', '编辑客户信息', 'customer', 'edit'),
('customer:delete', '删除客户', '删除客户信息', 'customer', 'delete'),

-- 财务管理
('finance:view', '查看财务', '查看财务数据', 'finance', 'view'),
('finance:disbursement', '放款管理', '创建和管理放款记录', 'finance', 'disbursement'),
('finance:repayment', '还款管理', '创建和管理还款记录', 'finance', 'repayment'),
('finance:export', '财务导出', '导出财务数据', 'finance', 'export'),

-- 黑名单管理
('blacklist:view', '查看黑名单', '查看黑名单数据', 'blacklist', 'view'),
('blacklist:create', '创建黑名单', '添加黑名单记录', 'blacklist', 'create'),
('blacklist:edit', '编辑黑名单', '编辑黑名单记录', 'blacklist', 'edit'),
('blacklist:delete', '删除黑名单', '删除黑名单记录', 'blacklist', 'delete'),

-- 配置管理
('config:view', '查看配置', '查看系统配置', 'config', 'view'),
('config:edit', '编辑配置', '编辑系统配置', 'config', 'edit'),

-- 资方权限
('investor:query', '资方查询', '资方客户查询功能', 'investor', 'query'),
('investor:customer', '客户管理', '资方客户管理功能', 'investor', 'customer'),

-- 通知权限
('notification:view', '查看通知', '查看通知记录', 'notification', 'view'),
('notification:create', '创建通知', '创建通知', 'notification', 'create');

-- 插入角色权限关联（基础配置）
INSERT IGNORE INTO role_permissions (role_id, permission_id)
SELECT r.id, p.id 
FROM roles r, permissions p 
WHERE r.name = 'admin';  -- admin 拥有所有权限

-- 插入默认配置
INSERT IGNORE INTO app_configs (platform, app_type, config_key, config_value, config_type, description) VALUES
('web', 'admin', 'system_name', '民间空放贷后管理系统', 'string', '系统名称'),
('web', 'admin', 'company_name', '某某金融公司', 'string', '公司名称'),
('web', 'admin', 'contact_phone', '************', 'string', '联系电话'),
('web', 'admin', 'max_upload_size', '10', 'number', '最大上传文件大小(MB)'),
('miniprogram', 'customer', 'app_name', '客户端小程序', 'string', '小程序名称'),
('miniprogram', 'investor', 'app_name', '资方端小程序', 'string', '资方端小程序名称');

-- 8. 增量迁移脚本（用于现有数据库升级）

-- 检查并添加customers表的tags字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = 'daihou' 
     AND TABLE_NAME = 'customers' 
     AND COLUMN_NAME = 'tags') = 0,
    'ALTER TABLE customers ADD COLUMN tags JSON COMMENT "customer tags for investor" AFTER status;',
    'SELECT "customers.tags field already exists";'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 清理语句
SET @sql = NULL;

COMMIT;
