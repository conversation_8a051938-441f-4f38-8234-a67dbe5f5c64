<?php
declare(strict_types=1);

namespace tests\Performance;

use tests\TestCase;
use think\facade\Db;

/**
 * 数据库性能测试
 */
class DatabasePerformanceTest extends TestCase
{
    /**
     * 性能测试阈值配置
     */
    const PERFORMANCE_THRESHOLDS = [
        'single_query_max_time' => 100, // 单次查询最大时间(毫秒)
        'batch_insert_max_time' => 1000, // 批量插入最大时间(毫秒)
        'complex_query_max_time' => 500, // 复杂查询最大时间(毫秒)
        'pagination_query_max_time' => 200, // 分页查询最大时间(毫秒)
    ];

    /**
     * 测试单表查询性能
     */
    public function testSingleTableQueryPerformance(): void
    {
        // 创建大量测试数据
        $this->createLargeDataset();

        // 测试简单查询性能
        $startTime = microtime(true);
        $customers = Db::name('customers')
            ->where('status', 'active')
            ->limit(100)
            ->select();
        $endTime = microtime(true);

        $executionTime = ($endTime - $startTime) * 1000; // 转换为毫秒

        $this->assertLessThan(
            self::PERFORMANCE_THRESHOLDS['single_query_max_time'],
            $executionTime,
            "单表查询耗时 {$executionTime}ms 超过阈值 " . self::PERFORMANCE_THRESHOLDS['single_query_max_time'] . "ms"
        );

        $this->assertGreaterThan(0, count($customers), '查询应该返回结果');
    }

    /**
     * 测试索引查询性能
     */
    public function testIndexedQueryPerformance(): void
    {
        $this->createLargeDataset();

        // 测试基于索引的查询
        $startTime = microtime(true);
        $customer = Db::name('customers')
            ->where('phone', '13800138100')
            ->find();
        $endTime = microtime(true);

        $executionTime = ($endTime - $startTime) * 1000;

        $this->assertLessThan(
            50, // 索引查询应该更快
            $executionTime,
            "索引查询耗时 {$executionTime}ms 超过阈值 50ms"
        );

        $this->assertNotNull($customer, '索引查询应该返回结果');
    }

    /**
     * 测试复杂查询性能
     */
    public function testComplexQueryPerformance(): void
    {
        $this->createLargeDataset();
        $this->createLoanApplications();

        // 测试多表关联查询
        $startTime = microtime(true);
        $results = Db::name('customers')
            ->alias('c')
            ->leftJoin('loan_applications la', 'c.id = la.customer_id')
            ->where('c.status', 'active')
            ->where('c.risk_level', 'in', ['low', 'medium'])
            ->field('c.*, COUNT(la.id) as loan_count, SUM(la.amount) as total_amount')
            ->group('c.id')
            ->having('loan_count > 0')
            ->order('total_amount DESC')
            ->limit(50)
            ->select();
        $endTime = microtime(true);

        $executionTime = ($endTime - $startTime) * 1000;

        $this->assertLessThan(
            self::PERFORMANCE_THRESHOLDS['complex_query_max_time'],
            $executionTime,
            "复杂查询耗时 {$executionTime}ms 超过阈值 " . self::PERFORMANCE_THRESHOLDS['complex_query_max_time'] . "ms"
        );

        $this->assertGreaterThan(0, count($results), '复杂查询应该返回结果');
    }

    /**
     * 测试分页查询性能
     */
    public function testPaginationQueryPerformance(): void
    {
        $this->createLargeDataset();

        // 测试分页查询性能
        $startTime = microtime(true);
        $customers = Db::name('customers')
            ->where('status', 'active')
            ->order('id DESC')
            ->paginate([
                'list_rows' => 20,
                'page' => 10 // 测试深度分页
            ]);
        $endTime = microtime(true);

        $executionTime = ($endTime - $startTime) * 1000;

        $this->assertLessThan(
            self::PERFORMANCE_THRESHOLDS['pagination_query_max_time'],
            $executionTime,
            "分页查询耗时 {$executionTime}ms 超过阈值 " . self::PERFORMANCE_THRESHOLDS['pagination_query_max_time'] . "ms"
        );

        $this->assertGreaterThan(0, count($customers->items()), '分页查询应该返回结果');
    }

    /**
     * 测试批量插入性能
     */
    public function testBatchInsertPerformance(): void
    {
        // 准备批量插入数据
        $batchData = [];
        for ($i = 1; $i <= 1000; $i++) {
            $batchData[] = [
                'name' => "批量客户{$i}",
                'phone' => '138' . str_pad((string)$i, 8, '0', STR_PAD_LEFT),
                'id_card' => '110101199001' . str_pad((string)$i, 6, '0', STR_PAD_LEFT),
                'status' => 'active',
                'risk_level' => 'medium',
                'risk_score' => rand(50, 90),
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];
        }

        // 测试批量插入性能
        $startTime = microtime(true);
        $result = Db::name('customers')->insertAll($batchData);
        $endTime = microtime(true);

        $executionTime = ($endTime - $startTime) * 1000;

        $this->assertLessThan(
            self::PERFORMANCE_THRESHOLDS['batch_insert_max_time'],
            $executionTime,
            "批量插入耗时 {$executionTime}ms 超过阈值 " . self::PERFORMANCE_THRESHOLDS['batch_insert_max_time'] . "ms"
        );

        $this->assertTrue($result, '批量插入应该成功');
    }

    /**
     * 测试批量更新性能
     */
    public function testBatchUpdatePerformance(): void
    {
        $this->createLargeDataset();

        // 测试批量更新性能
        $startTime = microtime(true);
        $result = Db::name('customers')
            ->where('status', 'pending')
            ->update([
                'status' => 'active',
                'updated_at' => date('Y-m-d H:i:s')
            ]);
        $endTime = microtime(true);

        $executionTime = ($endTime - $startTime) * 1000;

        $this->assertLessThan(
            300, // 批量更新阈值
            $executionTime,
            "批量更新耗时 {$executionTime}ms 超过阈值 300ms"
        );

        $this->assertGreaterThan(0, $result, '批量更新应该影响记录');
    }

    /**
     * 测试事务性能
     */
    public function testTransactionPerformance(): void
    {
        // 测试事务性能
        $startTime = microtime(true);
        
        Db::transaction(function () {
            // 在事务中执行多个操作
            for ($i = 1; $i <= 100; $i++) {
                $customerId = Db::name('customers')->insertGetId([
                    'name' => "事务客户{$i}",
                    'phone' => '139' . str_pad((string)$i, 8, '0', STR_PAD_LEFT),
                    'id_card' => '110101199002' . str_pad((string)$i, 6, '0', STR_PAD_LEFT),
                    'status' => 'active',
                    'risk_level' => 'medium',
                    'risk_score' => rand(50, 90),
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ]);

                Db::name('loan_applications')->insert([
                    'customer_id' => $customerId,
                    'amount' => rand(10000, 100000),
                    'cycle' => rand(30, 365),
                    'status' => 'pending',
                    'submit_time' => date('Y-m-d H:i:s'),
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ]);
            }
        });

        $endTime = microtime(true);
        $executionTime = ($endTime - $startTime) * 1000;

        $this->assertLessThan(
            2000, // 事务操作阈值
            $executionTime,
            "事务操作耗时 {$executionTime}ms 超过阈值 2000ms"
        );
    }

    /**
     * 测试连接池性能
     */
    public function testConnectionPoolPerformance(): void
    {
        $connectionTimes = [];

        // 测试多次数据库连接的性能
        for ($i = 0; $i < 10; $i++) {
            $startTime = microtime(true);
            
            // 执行简单查询来测试连接性能
            Db::query('SELECT 1');
            
            $endTime = microtime(true);
            $connectionTimes[] = ($endTime - $startTime) * 1000;
        }

        $avgConnectionTime = array_sum($connectionTimes) / count($connectionTimes);
        $maxConnectionTime = max($connectionTimes);

        $this->assertLessThan(
            50, // 平均连接时间阈值
            $avgConnectionTime,
            "平均连接时间 {$avgConnectionTime}ms 超过阈值 50ms"
        );

        $this->assertLessThan(
            100, // 最大连接时间阈值
            $maxConnectionTime,
            "最大连接时间 {$maxConnectionTime}ms 超过阈值 100ms"
        );
    }

    /**
     * 创建大量测试数据
     */
    private function createLargeDataset(): void
    {
        // 检查是否已有足够的测试数据
        $count = Db::name('customers')->count();
        if ($count >= 1000) {
            return;
        }

        // 创建大量客户数据
        $batchData = [];
        for ($i = $count + 1; $i <= 1000; $i++) {
            $batchData[] = [
                'name' => "测试客户{$i}",
                'phone' => '138' . str_pad((string)$i, 8, '0', STR_PAD_LEFT),
                'id_card' => '110101199001' . str_pad((string)$i, 6, '0', STR_PAD_LEFT),
                'status' => ['active', 'inactive', 'pending'][rand(0, 2)],
                'risk_level' => ['low', 'medium', 'high', 'very_high'][rand(0, 3)],
                'risk_score' => rand(30, 95),
                'created_at' => date('Y-m-d H:i:s', time() - rand(0, 86400 * 30)),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            // 批量插入，每500条一批
            if (count($batchData) >= 500) {
                Db::name('customers')->insertAll($batchData);
                $batchData = [];
            }
        }

        // 插入剩余数据
        if (!empty($batchData)) {
            Db::name('customers')->insertAll($batchData);
        }
    }

    /**
     * 创建借款申请测试数据
     */
    private function createLoanApplications(): void
    {
        $customerIds = Db::name('customers')->column('id');
        
        if (empty($customerIds)) {
            return;
        }

        $batchData = [];
        foreach ($customerIds as $customerId) {
            // 为每个客户创建1-3个借款申请
            $loanCount = rand(1, 3);
            for ($j = 0; $j < $loanCount; $j++) {
                $batchData[] = [
                    'customer_id' => $customerId,
                    'amount' => rand(10000, 200000),
                    'cycle' => rand(30, 365),
                    'status' => ['pending', 'approved', 'rejected', 'disbursed'][rand(0, 3)],
                    'submit_time' => date('Y-m-d H:i:s', time() - rand(0, 86400 * 30)),
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ];

                // 批量插入，每1000条一批
                if (count($batchData) >= 1000) {
                    Db::name('loan_applications')->insertAll($batchData);
                    $batchData = [];
                }
            }
        }

        // 插入剩余数据
        if (!empty($batchData)) {
            Db::name('loan_applications')->insertAll($batchData);
        }
    }
}
