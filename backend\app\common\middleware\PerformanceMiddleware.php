<?php
declare(strict_types=1);

namespace app\common\middleware;

use app\common\service\CacheService;
use think\facade\Log;
use think\Request;
use think\Response;

/**
 * 性能监控中间件
 */
class PerformanceMiddleware
{
    /**
     * 处理请求
     * @param Request $request
     * @param \Closure $next
     * @return Response
     */
    public function handle(Request $request, \Closure $next): Response
    {
        $startTime = microtime(true);
        $startMemory = memory_get_usage();
        
        // 执行请求
        $response = $next($request);
        
        $endTime = microtime(true);
        $endMemory = memory_get_usage();
        
        // 计算性能指标
        $executionTime = round(($endTime - $startTime) * 1000, 2); // 毫秒
        $memoryUsage = round(($endMemory - $startMemory) / 1024 / 1024, 2); // MB
        $peakMemory = round(memory_get_peak_usage() / 1024 / 1024, 2); // MB
        
        // 记录性能数据
        $this->recordPerformance($request, $executionTime, $memoryUsage, $peakMemory);
        
        // 添加性能头信息（开发环境）
        if (app()->isDebug()) {
            $response->header([
                'X-Execution-Time' => $executionTime . 'ms',
                'X-Memory-Usage' => $memoryUsage . 'MB',
                'X-Peak-Memory' => $peakMemory . 'MB'
            ]);
        }
        
        return $response;
    }

    /**
     * 记录性能数据
     * @param Request $request
     * @param float $executionTime
     * @param float $memoryUsage
     * @param float $peakMemory
     */
    private function recordPerformance(Request $request, float $executionTime, float $memoryUsage, float $peakMemory): void
    {
        $performanceData = [
            'url' => $request->url(),
            'method' => $request->method(),
            'execution_time' => $executionTime,
            'memory_usage' => $memoryUsage,
            'peak_memory' => $peakMemory,
            'timestamp' => time(),
            'date' => date('Y-m-d H:i:s')
        ];

        // 记录到日志
        if ($executionTime > 1000) { // 超过1秒的请求记录警告
            Log::warning('慢查询检测', $performanceData);
        } elseif ($executionTime > 500) { // 超过500ms的请求记录信息
            Log::info('性能监控', $performanceData);
        }

        // 缓存性能统计数据
        $this->cachePerformanceStats($performanceData);
    }

    /**
     * 缓存性能统计数据
     * @param array $performanceData
     */
    private function cachePerformanceStats(array $performanceData): void
    {
        try {
            $today = date('Y-m-d');
            $hour = date('H');
            
            // 每小时统计
            $hourlyKey = "performance_stats:{$today}:{$hour}";
            $hourlyStats = CacheService::get($hourlyKey, 'temp') ?: [
                'total_requests' => 0,
                'total_time' => 0,
                'total_memory' => 0,
                'max_time' => 0,
                'max_memory' => 0,
                'slow_requests' => 0
            ];
            
            $hourlyStats['total_requests']++;
            $hourlyStats['total_time'] += $performanceData['execution_time'];
            $hourlyStats['total_memory'] += $performanceData['memory_usage'];
            $hourlyStats['max_time'] = max($hourlyStats['max_time'], $performanceData['execution_time']);
            $hourlyStats['max_memory'] = max($hourlyStats['max_memory'], $performanceData['peak_memory']);
            
            if ($performanceData['execution_time'] > 1000) {
                $hourlyStats['slow_requests']++;
            }
            
            // 计算平均值
            $hourlyStats['avg_time'] = round($hourlyStats['total_time'] / $hourlyStats['total_requests'], 2);
            $hourlyStats['avg_memory'] = round($hourlyStats['total_memory'] / $hourlyStats['total_requests'], 2);
            
            CacheService::set($hourlyKey, $hourlyStats, 3600 * 25, 'temp'); // 缓存25小时
            
            // 每日统计
            $dailyKey = "performance_stats:{$today}";
            $dailyStats = CacheService::get($dailyKey, 'temp') ?: [
                'total_requests' => 0,
                'total_time' => 0,
                'total_memory' => 0,
                'max_time' => 0,
                'max_memory' => 0,
                'slow_requests' => 0
            ];
            
            $dailyStats['total_requests']++;
            $dailyStats['total_time'] += $performanceData['execution_time'];
            $dailyStats['total_memory'] += $performanceData['memory_usage'];
            $dailyStats['max_time'] = max($dailyStats['max_time'], $performanceData['execution_time']);
            $dailyStats['max_memory'] = max($dailyStats['max_memory'], $performanceData['peak_memory']);
            
            if ($performanceData['execution_time'] > 1000) {
                $dailyStats['slow_requests']++;
            }
            
            // 计算平均值
            $dailyStats['avg_time'] = round($dailyStats['total_time'] / $dailyStats['total_requests'], 2);
            $dailyStats['avg_memory'] = round($dailyStats['total_memory'] / $dailyStats['total_requests'], 2);
            
            CacheService::set($dailyKey, $dailyStats, 3600 * 24 * 7, 'temp'); // 缓存7天
            
        } catch (\Exception $e) {
            Log::error('性能统计缓存失败', ['error' => $e->getMessage()]);
        }
    }

    /**
     * 获取性能统计数据
     * @param string $date 日期 Y-m-d
     * @param int|null $hour 小时
     * @return array|null
     */
    public static function getPerformanceStats(string $date, ?int $hour = null): ?array
    {
        if ($hour !== null) {
            $key = "performance_stats:{$date}:{$hour}";
        } else {
            $key = "performance_stats:{$date}";
        }
        
        return CacheService::get($key, 'temp');
    }

    /**
     * 获取最近7天的性能趋势
     * @return array
     */
    public static function getPerformanceTrend(): array
    {
        $trend = [];
        for ($i = 6; $i >= 0; $i--) {
            $date = date('Y-m-d', strtotime("-{$i} days"));
            $stats = self::getPerformanceStats($date);
            $trend[$date] = $stats ?: [
                'total_requests' => 0,
                'avg_time' => 0,
                'avg_memory' => 0,
                'slow_requests' => 0
            ];
        }
        return $trend;
    }

    /**
     * 清理过期的性能统计数据
     */
    public static function cleanExpiredStats(): void
    {
        try {
            // 清理7天前的数据
            $expiredDate = date('Y-m-d', strtotime('-7 days'));
            for ($hour = 0; $hour < 24; $hour++) {
                $key = "performance_stats:{$expiredDate}:{$hour}";
                CacheService::delete($key, 'temp');
            }
            
            $dailyKey = "performance_stats:{$expiredDate}";
            CacheService::delete($dailyKey, 'temp');
            
        } catch (\Exception $e) {
            Log::error('清理性能统计数据失败', ['error' => $e->getMessage()]);
        }
    }
}
