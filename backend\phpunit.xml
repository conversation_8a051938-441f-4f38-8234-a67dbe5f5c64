<?xml version="1.0" encoding="UTF-8"?>
<phpunit xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:noNamespaceSchemaLocation="https://schema.phpunit.de/9.5/phpunit.xsd"
         bootstrap="vendor/autoload.php"
         cacheResultFile=".phpunit.result.cache"
         executionOrder="depends,defects"
         forceCoversAnnotation="false"
         beStrictAboutCoversAnnotation="true"
         beStrictAboutOutputDuringTests="true"
         beStrictAboutTodoAnnotatedTests="true"
         convertDeprecationsToExceptions="true"
         failOnRisky="true"
         failOnWarning="true"
         verbose="true">
    <testsuites>
        <testsuite name="Unit">
            <directory suffix="Test.php">./tests/Unit</directory>
        </testsuite>
        <testsuite name="Feature">
            <directory suffix="Test.php">./tests/Feature</directory>
        </testsuite>
        <testsuite name="Integration">
            <directory suffix="Test.php">./tests/Integration</directory>
        </testsuite>
        <testsuite name="Performance">
            <directory suffix="Test.php">./tests/Performance</directory>
        </testsuite>
        <testsuite name="All">
            <directory suffix="Test.php">./tests</directory>
        </testsuite>
    </testsuites>
    <coverage cacheDirectory=".phpunit.cache/code-coverage"
              processUncoveredFiles="true">
        <include>
            <directory suffix=".php">./app</directory>
        </include>
        <exclude>
            <directory suffix=".php">./app/common/model</directory>
            <directory>./app/common/exception</directory>
            <file>./app/common.php</file>
            <file>./app/event.php</file>
            <file>./app/middleware.php</file>
            <file>./app/provider.php</file>
            <file>./app/service.php</file>
        </exclude>
        <report>
            <html outputDirectory="tests/coverage/html"/>
            <xml outputDirectory="tests/coverage/xml"/>
            <clover outputFile="tests/coverage/clover.xml"/>
            <text outputFile="tests/coverage/coverage.txt"/>
        </report>
    </coverage>

    <!-- 日志配置 -->
    <logging>
        <junit outputFile="tests/logs/junit.xml"/>
        <teamcity outputFile="tests/logs/teamcity.txt"/>
        <testdoxHtml outputFile="tests/logs/testdox.html"/>
        <testdoxText outputFile="tests/logs/testdox.txt"/>
    </logging>
    <php>
        <!-- 应用环境 -->
        <env name="APP_ENV" value="testing"/>
        <env name="APP_DEBUG" value="true"/>

        <!-- 数据库配置 -->
        <env name="DB_CONNECTION" value="mysql"/>
        <env name="DB_HOST" value="127.0.0.1"/>
        <env name="DB_PORT" value="3306"/>
        <env name="DB_DATABASE" value="iapp_test"/>
        <env name="DB_USERNAME" value="root"/>
        <env name="DB_PASSWORD" value="4390024"/>

        <!-- 缓存配置 -->
        <env name="CACHE_DRIVER" value="array"/>

        <!-- 会话配置 -->
        <env name="SESSION_DRIVER" value="array"/>

        <!-- 队列配置 -->
        <env name="QUEUE_CONNECTION" value="sync"/>

        <!-- 邮件配置 -->
        <env name="MAIL_MAILER" value="array"/>

        <!-- 测试配置 -->
        <env name="BCRYPT_ROUNDS" value="4"/>
        <env name="TELESCOPE_ENABLED" value="false"/>
    </php>
</phpunit>
