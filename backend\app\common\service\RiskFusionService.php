<?php
declare(strict_types=1);

namespace app\common\service;

use think\facade\Log;

/**
 * 风险评估融合决策服务
 * 整合AI评估、规则引擎、统计模型的结果，做出最终决策
 */
class RiskFusionService
{
    /**
     * 融合多种评估结果
     * @param array $assessments 各种评估结果
     * @param array $customerData 客户数据
     * @return array 融合后的最终结果
     */
    public static function fuseAssessments(array $assessments, array $customerData): array
    {
        try {
            // 获取各评估结果
            $aiResult = $assessments['ai'] ?? [];
            $ruleResult = $assessments['rule'] ?? [];
            $modelResult = $assessments['model'] ?? [];
            
            // 权重配置
            $weights = self::getAssessmentWeights($customerData);
            
            // 计算加权综合评分
            $finalScore = self::calculateWeightedScore($aiResult, $ruleResult, $modelResult, $weights);
            
            // 确定风险等级
            $riskLevel = self::determineRiskLevel($finalScore, $assessments);
            
            // 计算建议授信额度
            $recommendedAmount = self::calculateRecommendedAmount($finalScore, $riskLevel, $customerData);
            
            // 生成决策建议
            $decision = self::makeDecision($finalScore, $riskLevel, $assessments);
            
            // 分析风险因素
            $riskFactors = self::analyzeRiskFactors($assessments, $customerData);
            
            // 生成决策解释
            $explanation = self::generateExplanation($finalScore, $riskLevel, $decision, $riskFactors);
            
            return [
                'final_score' => $finalScore,
                'risk_level' => $riskLevel,
                'decision' => $decision,
                'recommended_amount' => $recommendedAmount,
                'risk_factors' => $riskFactors,
                'explanation' => $explanation,
                'assessment_details' => [
                    'ai_assessment' => $aiResult,
                    'rule_assessment' => $ruleResult,
                    'model_assessment' => $modelResult,
                    'weights_used' => $weights
                ],
                'fusion_timestamp' => date('Y-m-d H:i:s'),
                'fusion_version' => 'v2.0'
            ];
            
        } catch (\Exception $e) {
            Log::error('风险评估融合失败', ['error' => $e->getMessage()]);
            return self::getDefaultFusionResult();
        }
    }

    /**
     * 获取评估权重
     * @param array $customerData 客户数据
     * @return array 权重配置
     */
    private static function getAssessmentWeights(array $customerData): array
    {
        // 基础权重
        $weights = [
            'ai' => 0.4,      // AI评估权重
            'rule' => 0.35,   // 规则引擎权重
            'model' => 0.25   // 统计模型权重
        ];
        
        // 根据客户特征调整权重
        $monthlyIncome = $customerData['monthly_income'] ?? 0;
        $industry = $customerData['industry'] ?? '';
        $hasHistory = !empty($customerData['behavior_analysis']['application_count']);
        
        // 高收入客户增加AI权重
        if ($monthlyIncome > 20000) {
            $weights['ai'] += 0.1;
            $weights['rule'] -= 0.05;
            $weights['model'] -= 0.05;
        }
        
        // 有历史记录的客户增加模型权重
        if ($hasHistory) {
            $weights['model'] += 0.1;
            $weights['ai'] -= 0.05;
            $weights['rule'] -= 0.05;
        }
        
        // 高风险行业增加规则权重
        $highRiskIndustries = ['个体户', '自由职业', '餐饮', '建筑'];
        if (in_array($industry, $highRiskIndustries)) {
            $weights['rule'] += 0.1;
            $weights['ai'] -= 0.05;
            $weights['model'] -= 0.05;
        }
        
        // 确保权重和为1
        $totalWeight = array_sum($weights);
        foreach ($weights as &$weight) {
            $weight = $weight / $totalWeight;
        }
        
        return $weights;
    }

    /**
     * 计算加权综合评分
     * @param array $aiResult AI评估结果
     * @param array $ruleResult 规则评估结果
     * @param array $modelResult 模型评估结果
     * @param array $weights 权重
     * @return int 综合评分
     */
    private static function calculateWeightedScore(array $aiResult, array $ruleResult, array $modelResult, array $weights): int
    {
        $aiScore = $aiResult['overall_score'] ?? 60;
        $ruleScore = $ruleResult['total_score'] ?? 60;
        $modelScore = $modelResult['predicted_score'] ?? 60;
        
        $weightedScore = ($aiScore * $weights['ai']) + 
                        ($ruleScore * $weights['rule']) + 
                        ($modelScore * $weights['model']);
        
        return round($weightedScore);
    }

    /**
     * 确定风险等级
     * @param int $finalScore 最终评分
     * @param array $assessments 评估结果
     * @return string 风险等级
     */
    private static function determineRiskLevel(int $finalScore, array $assessments): string
    {
        // 基于评分的基础等级
        if ($finalScore >= 85) {
            $baseLevel = 'low';
        } elseif ($finalScore >= 70) {
            $baseLevel = 'medium';
        } elseif ($finalScore >= 50) {
            $baseLevel = 'high';
        } else {
            $baseLevel = 'very_high';
        }
        
        // 检查是否有严重风险因素需要调整等级
        $aiLevel = $assessments['ai']['risk_level'] ?? 'medium';
        $ruleLevel = $assessments['rule']['risk_level'] ?? 'medium';
        $modelLevel = $assessments['model']['risk_level'] ?? 'medium';
        
        // 如果任一评估为very_high，最终等级不能低于high
        if (in_array('very_high', [$aiLevel, $ruleLevel, $modelLevel])) {
            if ($baseLevel === 'low' || $baseLevel === 'medium') {
                $baseLevel = 'high';
            }
        }
        
        // 如果多数评估为high以上，最终等级不能低于medium
        $highRiskCount = 0;
        foreach ([$aiLevel, $ruleLevel, $modelLevel] as $level) {
            if (in_array($level, ['high', 'very_high'])) {
                $highRiskCount++;
            }
        }
        
        if ($highRiskCount >= 2 && $baseLevel === 'low') {
            $baseLevel = 'medium';
        }
        
        return $baseLevel;
    }

    /**
     * 计算建议授信额度
     * @param int $finalScore 最终评分
     * @param string $riskLevel 风险等级
     * @param array $customerData 客户数据
     * @return int 建议额度
     */
    private static function calculateRecommendedAmount(int $finalScore, string $riskLevel, array $customerData): int
    {
        $monthlyIncome = $customerData['monthly_income'] ?? 0;
        
        // 基于收入的基础额度
        $baseAmount = $monthlyIncome * 3; // 3倍月收入
        
        // 根据风险等级调整
        $riskMultipliers = [
            'low' => 1.2,
            'medium' => 1.0,
            'high' => 0.6,
            'very_high' => 0.2
        ];
        
        $riskMultiplier = $riskMultipliers[$riskLevel] ?? 0.5;
        
        // 根据评分调整
        $scoreMultiplier = $finalScore / 100;
        
        $recommendedAmount = $baseAmount * $riskMultiplier * $scoreMultiplier;
        
        // 设置额度上下限
        $minAmount = 1000;
        $maxAmount = 200000;
        
        // 根据风险等级设置最大额度
        $maxAmountByRisk = [
            'low' => 200000,
            'medium' => 100000,
            'high' => 50000,
            'very_high' => 10000
        ];
        
        $maxAmount = min($maxAmount, $maxAmountByRisk[$riskLevel] ?? 50000);
        
        return max($minAmount, min($maxAmount, round($recommendedAmount, -2))); // 取整到百位
    }

    /**
     * 做出最终决策
     * @param int $finalScore 最终评分
     * @param string $riskLevel 风险等级
     * @param array $assessments 评估结果
     * @return string 决策结果
     */
    private static function makeDecision(int $finalScore, string $riskLevel, array $assessments): string
    {
        // 基于风险等级的基础决策
        $baseDecisions = [
            'low' => 'pass',
            'medium' => 'manual',
            'high' => 'manual',
            'very_high' => 'reject'
        ];
        
        $baseDecision = $baseDecisions[$riskLevel] ?? 'manual';
        
        // 检查是否有强制拒绝因素
        $aiDecision = $assessments['ai']['decision'] ?? 'manual';
        $ruleDecision = $assessments['rule']['decision'] ?? 'manual';
        $modelDecision = $assessments['model']['decision'] ?? 'manual';
        
        // 如果任一评估为reject，最终决策为reject
        if (in_array('reject', [$aiDecision, $ruleDecision, $modelDecision])) {
            return 'reject';
        }
        
        // 如果评分过低，强制拒绝
        if ($finalScore < 40) {
            return 'reject';
        }
        
        // 如果评分很高且风险等级低，可以自动通过
        if ($finalScore >= 90 && $riskLevel === 'low') {
            return 'pass';
        }
        
        return $baseDecision;
    }

    /**
     * 分析风险因素
     * @param array $assessments 评估结果
     * @param array $customerData 客户数据
     * @return array 风险因素分析
     */
    private static function analyzeRiskFactors(array $assessments, array $customerData): array
    {
        $riskFactors = [];
        $positiveFactors = [];
        
        // 从AI评估中提取风险因素
        if (!empty($assessments['ai']['risk_factors'])) {
            $riskFactors = array_merge($riskFactors, $assessments['ai']['risk_factors']);
        }
        
        if (!empty($assessments['ai']['positive_factors'])) {
            $positiveFactors = array_merge($positiveFactors, $assessments['ai']['positive_factors']);
        }
        
        // 从规则评估中提取风险因素
        if (!empty($assessments['rule']['triggered_rules'])) {
            foreach ($assessments['rule']['triggered_rules'] as $rule) {
                if ($rule['result'] === 'reject' || $rule['result'] === 'warning') {
                    $riskFactors[] = $rule['description'];
                }
            }
        }
        
        // 从模型评估中提取风险因素
        if (!empty($assessments['model']['risk_indicators'])) {
            $riskFactors = array_merge($riskFactors, $assessments['model']['risk_indicators']);
        }
        
        return [
            'risk_factors' => array_unique($riskFactors),
            'positive_factors' => array_unique($positiveFactors),
            'risk_factor_count' => count(array_unique($riskFactors)),
            'positive_factor_count' => count(array_unique($positiveFactors))
        ];
    }

    /**
     * 生成决策解释
     * @param int $finalScore 最终评分
     * @param string $riskLevel 风险等级
     * @param string $decision 决策结果
     * @param array $riskFactors 风险因素
     * @return string 决策解释
     */
    private static function generateExplanation(int $finalScore, string $riskLevel, string $decision, array $riskFactors): string
    {
        $explanation = "综合评估结果：评分{$finalScore}分，风险等级为{$riskLevel}。";
        
        switch ($decision) {
            case 'pass':
                $explanation .= "建议自动通过，客户风险可控，具备良好的还款能力。";
                break;
            case 'manual':
                $explanation .= "建议人工审核，需要进一步核实客户信息和还款能力。";
                break;
            case 'reject':
                $explanation .= "建议拒绝，客户风险过高，不符合放贷标准。";
                break;
        }
        
        if (!empty($riskFactors['risk_factors'])) {
            $explanation .= "主要风险因素包括：" . implode('、', array_slice($riskFactors['risk_factors'], 0, 3));
        }
        
        if (!empty($riskFactors['positive_factors'])) {
            $explanation .= "积极因素包括：" . implode('、', array_slice($riskFactors['positive_factors'], 0, 3));
        }
        
        return $explanation;
    }

    /**
     * 获取默认融合结果
     * @return array 默认结果
     */
    private static function getDefaultFusionResult(): array
    {
        return [
            'final_score' => 60,
            'risk_level' => 'medium',
            'decision' => 'manual',
            'recommended_amount' => 30000,
            'risk_factors' => [
                'risk_factors' => ['系统评估异常'],
                'positive_factors' => [],
                'risk_factor_count' => 1,
                'positive_factor_count' => 0
            ],
            'explanation' => '系统评估异常，建议人工审核',
            'assessment_details' => [],
            'fusion_timestamp' => date('Y-m-d H:i:s'),
            'fusion_version' => 'v2.0'
        ];
    }
}
