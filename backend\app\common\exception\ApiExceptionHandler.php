<?php
declare(strict_types=1);

namespace app\common\exception;

use app\common\service\ResponseService;
use think\db\exception\DataNotFoundException;
use think\db\exception\ModelNotFoundException;
use think\exception\Handle;
use think\exception\HttpException;
use think\exception\HttpResponseException;
use think\exception\ValidateException;
use think\facade\Log;
use think\Request;
use think\Response;
use Throwable;

/**
 * 统一API异常处理器
 */
class ApiExceptionHandler extends Handle
{
    /**
     * 不需要记录信息（日志）的异常类列表
     * @var array
     */
    protected $ignoreReport = [
        HttpException::class,
        HttpResponseException::class,
        ModelNotFoundException::class,
        DataNotFoundException::class,
        ValidateException::class,
    ];

    /**
     * 记录异常信息（包括日志或者其它方式记录）
     * @param Throwable $exception
     * @return void
     */
    public function report(Throwable $exception): void
    {
        // 记录异常日志
        if (!$this->isIgnoreReport($exception)) {
            Log::error('系统异常', [
                'message' => $exception->getMessage(),
                'file' => $exception->getFile(),
                'line' => $exception->getLine(),
                'trace' => $exception->getTraceAsString(),
                'url' => request()->url(),
                'method' => request()->method(),
                'ip' => request()->ip(),
                'user_agent' => request()->header('user-agent', ''),
                'params' => request()->param()
            ]);
        }
    }

    /**
     * Render an exception into an HTTP response.
     * @param Request $request
     * @param Throwable $e
     * @return Response
     */
    public function render($request, Throwable $e): Response
    {
        // 添加自定义异常处理机制
        if ($e instanceof ValidateException) {
            return ResponseService::error($e->getError(), 422);
        }

        if ($e instanceof DataNotFoundException || $e instanceof ModelNotFoundException) {
            return ResponseService::notFound('数据不存在');
        }

        if ($e instanceof HttpException) {
            return ResponseService::error($e->getMessage(), $e->getStatusCode());
        }

        if ($e instanceof HttpResponseException) {
            return $e->getResponse();
        }

        // 数据库异常
        if ($e instanceof \PDOException) {
            if (app()->isDebug()) {
                return ResponseService::serverError('数据库错误: ' . $e->getMessage());
            }
            return ResponseService::serverError('数据库操作失败');
        }

        // 其他异常
        if (app()->isDebug()) {
            return ResponseService::serverError($e->getMessage() . ' in ' . $e->getFile() . ':' . $e->getLine());
        }

        return ResponseService::serverError('系统内部错误');
    }

    /**
     * 判断异常是否忽略记录
     * @param Throwable $exception
     * @return bool
     */
    protected function isIgnoreReport(Throwable $exception): bool
    {
        foreach ($this->ignoreReport as $class) {
            if ($exception instanceof $class) {
                return true;
            }
        }

        return false;
    }
}
