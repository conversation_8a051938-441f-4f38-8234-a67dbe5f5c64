<?php
declare (strict_types = 1);

namespace app\api\controller;

use app\common\model\Notification as NotificationModel;
use think\Request;

class Notification
{
    /** 保存一条通知（草稿/发送记录，不实际外发） */
    public function save(Request $request)
    {
        $data = $request->only(['title','content','type','level','send_type','target_type','target_users','template_id','template_params','send_time']);
        if (empty($data['title']) || empty($data['content']) || empty($data['send_type'])) {
            return json(['code'=>400,'message'=>'缺少必要字段','data'=>null],400);
        }
        $notification = NotificationModel::create([
            'title' => $data['title'],
            'content' => $data['content'],
            'type' => $data['type'] ?? 'business',
            'level' => $data['level'] ?? 'info',
            'send_type' => $data['send_type'],
            'target_type' => $data['target_type'] ?? 'custom',
            'target_users' => $data['target_users'] ?? [],
            'template_id' => $data['template_id'] ?? '',
            'template_params' => $data['template_params'] ?? [],
            'send_time' => $data['send_time'] ?? null,
            'status' => 'draft',
            'created_by' => $request->user['id'] ?? 0,
        ]);
        return json(['code'=>200,'message'=>'保存成功','data'=>$notification]);
    }
}


