<?php
declare (strict_types = 1);

namespace app\common\service;

use app\common\model\LoanDisbursementRecord;
use app\common\model\RepaymentScheduleDetail;
use think\facade\Db;

class DisbursementService
{
    /**
     * 创建放款登记
     */
    public static function createDisbursementRecord($data)
    {
        Db::startTrans();
        try {
            // 1. 生成业务流水号
            $businessFlowNo = self::generateBusinessFlowNo();
            
            // 2. 计算中介返点
            $commissionData = self::calculateAgentCommission($data);
            
            // 3. 计算未回款总额 = 放款金额 + 中介返点 - 平台费
            $remainingAmount = $data['loan_amount'] + $commissionData['total_commission'] - ($data['platform_fee'] ?? 0);
            
            // 4. 创建放款记录
            $disbursementRecord = LoanDisbursementRecord::create([
                'business_flow_no' => $businessFlowNo,
                'loan_date' => $data['loan_date'],
                'customer_name' => $data['customer_name'],
                'customer_id' => $data['customer_id'],
                'risk_controller_name' => $data['risk_controller_name'] ?? '',
                'risk_controller_id' => $data['risk_controller_id'] ?? 0,
                'loan_amount' => $data['loan_amount'],
                'repayment_type' => $data['repayment_type'],
                'repayment_cycle' => $data['repayment_cycle'],
                'repayment_frequency' => $data['repayment_frequency'] ?? '',
                'agent_id' => $data['agent_id'] ?? null,
                'agent_name' => $data['agent_name'] ?? null,
                'agent_commission_rate' => $commissionData['commission_rate'],
                'agent_commission_amount' => $commissionData['total_commission'],
                'commission_type' => $data['commission_type'] ?? 'front',
                'front_commission_amount' => $commissionData['front_commission'],
                'back_commission_amount' => $commissionData['back_commission'],
                'platform_fee' => $data['platform_fee'] ?? 0,
                'remaining_amount' => $remainingAmount,
                'customer_profit' => -$remainingAmount, // 初始利润为负
                'created_by' => $data['created_by'] ?? 0
            ]);
            
            // 5. 生成还款计划
            self::generateRepaymentSchedule($disbursementRecord);
            
            Db::commit();
            return $disbursementRecord;
            
        } catch (\Exception $e) {
            Db::rollback();
            throw $e;
        }
    }
    
    /**
     * 生成业务流水号
     */
    private static function generateBusinessFlowNo(): string
    {
        return 'DIS' . date('YmdHis') . sprintf('%03d', rand(1, 999));
    }
    
    /**
     * 生成还款计划
     */
    private static function generateRepaymentSchedule($disbursementRecord)
    {
        $schedules = [];
        $loanAmount = $disbursementRecord->loan_amount;
        $cycle = $disbursementRecord->repayment_cycle;
        $type = $disbursementRecord->repayment_type;
        
        // 根据还款方式生成计划
        switch ($type) {
            case 'daily':
                $dailyAmount = $loanAmount / $cycle;
                for ($i = 1; $i <= $cycle; $i++) {
                    $dueDate = date('Y-m-d', strtotime($disbursementRecord->loan_date . " +{$i} day"));
                    $schedules[] = [
                        'disbursement_record_id' => $disbursementRecord->id,
                        'period_number' => $i,
                        'due_date' => $dueDate,
                        'due_amount' => round($dailyAmount, 2),
                        'remaining_amount' => round($dailyAmount, 2)
                    ];
                }
                break;
                
            case 'weekly':
                $weeklyAmount = $loanAmount / $cycle;
                for ($i = 1; $i <= $cycle; $i++) {
                    $dueDate = date('Y-m-d', strtotime($disbursementRecord->loan_date . " +{$i} week"));
                    $schedules[] = [
                        'disbursement_record_id' => $disbursementRecord->id,
                        'period_number' => $i,
                        'due_date' => $dueDate,
                        'due_amount' => round($weeklyAmount, 2),
                        'remaining_amount' => round($weeklyAmount, 2)
                    ];
                }
                break;
                
            case 'monthly':
                $monthlyAmount = $loanAmount / $cycle;
                for ($i = 1; $i <= $cycle; $i++) {
                    $dueDate = date('Y-m-d', strtotime($disbursementRecord->loan_date . " +{$i} month"));
                    $schedules[] = [
                        'disbursement_record_id' => $disbursementRecord->id,
                        'period_number' => $i,
                        'due_date' => $dueDate,
                        'due_amount' => round($monthlyAmount, 2),
                        'remaining_amount' => round($monthlyAmount, 2)
                    ];
                }
                break;
        }
        
        // 批量插入还款计划
        RepaymentScheduleDetail::insertAll($schedules);
    }
    
    /**
     * 计算中介返点
     */
    private static function calculateAgentCommission($data)
    {
        if (empty($data['agent_commission_rate'])) {
            return [
                'commission_rate' => 0,
                'total_commission' => 0,
                'front_commission' => 0,
                'back_commission' => 0
            ];
        }
        
        $rate = $data['agent_commission_rate'] / 100; // 转换为小数
        $totalCommission = $data['loan_amount'] * $rate;
        
        $commissionType = $data['commission_type'] ?? 'front';
        
        switch ($commissionType) {
            case 'front':
                return [
                    'commission_rate' => $data['agent_commission_rate'],
                    'total_commission' => $totalCommission,
                    'front_commission' => $totalCommission,
                    'back_commission' => 0
                ];
                
            case 'back':
                return [
                    'commission_rate' => $data['agent_commission_rate'],
                    'total_commission' => $totalCommission,
                    'front_commission' => 0,
                    'back_commission' => $totalCommission
                ];
                
            case 'both':
                $frontRate = $data['front_commission_rate'] ?? 50; // 默认前返50%
                $frontCommission = $totalCommission * ($frontRate / 100);
                $backCommission = $totalCommission - $frontCommission;
                
                return [
                    'commission_rate' => $data['agent_commission_rate'],
                    'total_commission' => $totalCommission,
                    'front_commission' => $frontCommission,
                    'back_commission' => $backCommission
                ];
        }
    }
}
