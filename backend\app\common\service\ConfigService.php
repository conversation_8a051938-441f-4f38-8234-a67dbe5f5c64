<?php
declare(strict_types=1);

namespace app\common\service;

use think\facade\Cache;
use think\facade\Db;
use think\facade\Log;

/**
 * 统一配置管理服务
 */
class ConfigService
{
    /**
     * 缓存前缀
     */
    const CACHE_PREFIX = 'config:';

    /**
     * 缓存过期时间（秒）
     */
    const CACHE_EXPIRE = 3600;

    /**
     * 获取配置值
     * @param string $key 配置键
     * @param mixed $default 默认值
     * @param string $type 配置类型
     * @return mixed
     */
    public static function get(string $key, $default = null, string $type = 'system')
    {
        $cacheKey = self::CACHE_PREFIX . $type . ':' . $key;
        
        // 先从缓存获取
        $value = Cache::get($cacheKey);
        if ($value !== null) {
            return $value;
        }
        
        try {
            // 从数据库获取
            $config = Db::name('system_configs')
                ->where('key', $key)
                ->where('type', $type)
                ->find();
            
            if ($config) {
                $value = self::parseValue($config['value'], $config['data_type'] ?? 'string');
                // 缓存配置
                Cache::set($cacheKey, $value, self::CACHE_EXPIRE);
                return $value;
            }
            
            return $default;
        } catch (\Exception $e) {
            Log::error('获取配置失败', ['key' => $key, 'error' => $e->getMessage()]);
            return $default;
        }
    }

    /**
     * 设置配置值
     * @param string $key 配置键
     * @param mixed $value 配置值
     * @param string $type 配置类型
     * @param string $description 配置描述
     * @return bool
     */
    public static function set(string $key, $value, string $type = 'system', string $description = ''): bool
    {
        try {
            $dataType = self::getDataType($value);
            $valueStr = self::formatValue($value, $dataType);
            
            $data = [
                'key' => $key,
                'value' => $valueStr,
                'type' => $type,
                'data_type' => $dataType,
                'description' => $description,
                'update_time' => date('Y-m-d H:i:s')
            ];
            
            // 检查是否存在
            $exists = Db::name('system_configs')
                ->where('key', $key)
                ->where('type', $type)
                ->find();
            
            if ($exists) {
                // 更新
                $result = Db::name('system_configs')
                    ->where('key', $key)
                    ->where('type', $type)
                    ->update($data);
            } else {
                // 新增
                $data['create_time'] = date('Y-m-d H:i:s');
                $result = Db::name('system_configs')->insert($data);
            }
            
            if ($result) {
                // 清除缓存
                $cacheKey = self::CACHE_PREFIX . $type . ':' . $key;
                Cache::delete($cacheKey);
                return true;
            }
            
            return false;
        } catch (\Exception $e) {
            Log::error('设置配置失败', ['key' => $key, 'error' => $e->getMessage()]);
            return false;
        }
    }

    /**
     * 删除配置
     * @param string $key 配置键
     * @param string $type 配置类型
     * @return bool
     */
    public static function delete(string $key, string $type = 'system'): bool
    {
        try {
            $result = Db::name('system_configs')
                ->where('key', $key)
                ->where('type', $type)
                ->delete();
            
            if ($result) {
                // 清除缓存
                $cacheKey = self::CACHE_PREFIX . $type . ':' . $key;
                Cache::delete($cacheKey);
                return true;
            }
            
            return false;
        } catch (\Exception $e) {
            Log::error('删除配置失败', ['key' => $key, 'error' => $e->getMessage()]);
            return false;
        }
    }

    /**
     * 获取配置列表
     * @param string $type 配置类型
     * @return array
     */
    public static function getList(string $type = 'system'): array
    {
        try {
            $configs = Db::name('system_configs')
                ->where('type', $type)
                ->order('sort', 'asc')
                ->order('create_time', 'asc')
                ->select()
                ->toArray();
            
            $result = [];
            foreach ($configs as $config) {
                $result[$config['key']] = self::parseValue($config['value'], $config['data_type'] ?? 'string');
            }
            
            return $result;
        } catch (\Exception $e) {
            Log::error('获取配置列表失败', ['type' => $type, 'error' => $e->getMessage()]);
            return [];
        }
    }

    /**
     * 清除配置缓存
     * @param string|null $key 配置键，为空则清除所有
     * @param string $type 配置类型
     * @return bool
     */
    public static function clearCache(?string $key = null, string $type = 'system'): bool
    {
        try {
            if ($key) {
                $cacheKey = self::CACHE_PREFIX . $type . ':' . $key;
                Cache::delete($cacheKey);
            } else {
                // 清除所有配置缓存
                $pattern = self::CACHE_PREFIX . $type . ':*';
                Cache::clear($pattern);
            }
            return true;
        } catch (\Exception $e) {
            Log::error('清除配置缓存失败', ['key' => $key, 'error' => $e->getMessage()]);
            return false;
        }
    }

    /**
     * 解析配置值
     * @param string $value 配置值
     * @param string $dataType 数据类型
     * @return mixed
     */
    private static function parseValue(string $value, string $dataType)
    {
        switch ($dataType) {
            case 'int':
            case 'integer':
                return (int)$value;
            case 'float':
            case 'double':
                return (float)$value;
            case 'bool':
            case 'boolean':
                return in_array(strtolower($value), ['1', 'true', 'yes', 'on']);
            case 'array':
            case 'json':
                return json_decode($value, true) ?: [];
            default:
                return $value;
        }
    }

    /**
     * 格式化配置值
     * @param mixed $value 配置值
     * @param string $dataType 数据类型
     * @return string
     */
    private static function formatValue($value, string $dataType): string
    {
        switch ($dataType) {
            case 'array':
            case 'json':
                return json_encode($value, JSON_UNESCAPED_UNICODE);
            case 'bool':
            case 'boolean':
                return $value ? '1' : '0';
            default:
                return (string)$value;
        }
    }

    /**
     * 获取数据类型
     * @param mixed $value 值
     * @return string
     */
    private static function getDataType($value): string
    {
        if (is_int($value)) {
            return 'int';
        } elseif (is_float($value)) {
            return 'float';
        } elseif (is_bool($value)) {
            return 'bool';
        } elseif (is_array($value)) {
            return 'array';
        } else {
            return 'string';
        }
    }
}
