<template>
  <div class="repayment-management">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>还款管理</span>
          <el-button type="primary" @click="showCreateDialog">新增还款</el-button>
        </div>
      </template>
      
      <!-- 搜索区域 -->
      <div class="search-area">
        <el-form :inline="true" :model="searchForm">
          <el-form-item label="客户">
            <el-select
              v-model="searchForm.customer_id"
              placeholder="请选择客户"
              filterable
              remote
              :remote-method="searchCustomers"
              :loading="customerLoading"
              clearable
              style="width: 200px"
            >
              <el-option
                v-for="customer in customerOptions"
                :key="customer.id"
                :label="`${customer.name} (${customer.phone})`"
                :value="customer.id"
              />
            </el-select>
          </el-form-item>
          
          <el-form-item label="还款方式">
            <el-select v-model="searchForm.repayment_method" placeholder="请选择还款方式" clearable>
              <el-option label="微信" value="wechat"/>
              <el-option label="支付宝" value="alipay"/>
              <el-option label="银行卡" value="bank_card"/>
              <el-option label="现金" value="cash"/>
              <el-option label="其他" value="other"/>
            </el-select>
          </el-form-item>
          
          <el-form-item>
            <el-button type="primary" @click="handleSearch">搜索</el-button>
            <el-button @click="resetSearch">重置</el-button>
            <el-button type="success" @click="handleExport" :loading="exportLoading">
              <el-icon><Download /></el-icon>
              导出数据
            </el-button>
          </el-form-item>
        </el-form>
      </div>
      
      <!-- 表格 -->
      <el-table
        v-loading="tableLoading"
        :data="tableData"
        style="width: 100%"
      >
        <el-table-column prop="business_flow_no" label="还款流水号" width="200"/>
        
        <el-table-column prop="customer_name" label="客户姓名" width="120"/>
        
        <el-table-column prop="repayment_date" label="还款时间" width="180">
          <template #default="scope">
            {{ formatDateTime(scope.row.repayment_date) }}
          </template>
        </el-table-column>
        
        <el-table-column prop="repayment_amount" label="还款金额" width="120">
          <template #default="scope">
            ¥{{ formatMoney(scope.row.repayment_amount) }}
          </template>
        </el-table-column>
        
        <el-table-column prop="repayment_method" label="还款方式" width="100">
          <template #default="scope">
            {{ getRepaymentMethodText(scope.row.repayment_method) }}
          </template>
        </el-table-column>
        
        <el-table-column prop="repayment_type" label="还款类型" width="120">
          <template #default="scope">
            {{ getRepaymentTypeText(scope.row.repayment_type) }}
          </template>
        </el-table-column>
        
        <el-table-column prop="overdue_days" label="逾期天数" width="100">
          <template #default="scope">
            <el-tag v-if="scope.row.overdue_days > 0" type="danger">
              {{ scope.row.overdue_days }}天
            </el-tag>
            <span v-else>-</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="getStatusTagType(scope.row.status)">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="180" fixed="right">
          <template #default="scope">
            <el-button
              type="primary"
              size="small"
              @click="viewDetail(scope.row)"
            >
              详情
            </el-button>
            <el-button
              v-if="scope.row.payment_screenshot"
              type="info"
              size="small"
              @click="viewScreenshot(scope.row)"
            >
              查看截图
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <el-pagination
        v-model:current-page="pagination.page"
        v-model:page-size="pagination.limit"
        :page-sizes="[10, 20, 50, 100]"
        :total="pagination.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-card>
    
    <!-- 新增还款对话框 -->
    <el-dialog
      v-model="createDialogVisible"
      title="新增还款记录"
      width="600px"
      @close="resetCreateForm"
    >
      <el-form
        ref="createFormRef"
        :model="createForm"
        :rules="createRules"
        label-width="120px"
      >
        <el-form-item label="选择客户" prop="disbursement_record_id">
          <el-select
            v-model="createForm.disbursement_record_id"
            placeholder="请选择放款记录"
            filterable
            remote
            :remote-method="searchDisbursementRecords"
            :loading="disbursementLoading"
            style="width: 100%"
            @change="handleDisbursementChange"
          >
            <el-option
              v-for="record in disbursementOptions"
              :key="record.id"
              :label="`${record.customer_name} - ¥${formatMoney(record.loan_amount)} (${record.business_flow_no})`"
              :value="record.id"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="还款方式" prop="repayment_method">
          <el-select v-model="createForm.repayment_method" placeholder="请选择还款方式">
            <el-option label="微信" value="wechat"/>
            <el-option label="支付宝" value="alipay"/>
            <el-option label="银行卡" value="bank_card"/>
            <el-option label="现金" value="cash"/>
            <el-option label="其他" value="other"/>
          </el-select>
        </el-form-item>
        
        <el-form-item label="还款金额" prop="repayment_amount">
          <el-input-number
            v-model="createForm.repayment_amount"
            :min="0"
            :precision="2"
            placeholder="请输入还款金额"
            style="width: 100%"
          />
        </el-form-item>
        
        <el-form-item label="还款类型" prop="repayment_type">
          <el-select v-model="createForm.repayment_type" placeholder="请选择还款类型">
            <el-option label="正常还款" value="normal"/>
            <el-option label="部分还款" value="partial"/>
            <el-option label="协商还款" value="negotiated"/>
            <el-option label="逾期费" value="overdue_fee"/>
            <el-option label="提前结清" value="early_settlement"/>
          </el-select>
        </el-form-item>
        
        <el-form-item label="逾期天数">
          <el-input-number
            v-model="createForm.overdue_days"
            :min="0"
            placeholder="请输入逾期天数"
            style="width: 100%"
          />
        </el-form-item>
        
        <el-form-item label="逾期费">
          <el-input-number
            v-model="createForm.overdue_fee"
            :min="0"
            :precision="2"
            placeholder="请输入逾期费"
            style="width: 100%"
          />
        </el-form-item>
        
        <el-form-item label="还款截图">
          <el-upload
            ref="uploadRef"
            action="#"
            :auto-upload="false"
            :on-change="handleFileChange"
            :show-file-list="false"
            accept="image/*"
          >
            <el-button type="primary" :loading="uploadLoading">选择图片</el-button>
          </el-upload>
          <div v-if="createForm.payment_screenshot" class="upload-preview">
            <img :src="createForm.payment_screenshot" alt="还款截图" style="width: 100px; height: 100px; object-fit: cover;"/>
          </div>
        </el-form-item>
        
        <el-form-item label="备注">
          <el-input
            v-model="createForm.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入备注"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="createDialogVisible = false">取消</el-button>
        <el-button type="primary" :loading="createLoading" @click="handleCreate">确定</el-button>
      </template>
    </el-dialog>
    
    <!-- 截图预览对话框 -->
    <el-dialog
      v-model="screenshotDialogVisible"
      title="还款截图"
      width="500px"
    >
      <div style="text-align: center;">
        <img :src="screenshotUrl" alt="还款截图" style="max-width: 100%; max-height: 400px;"/>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { Download } from '@element-plus/icons-vue'
import { financeAPI, customerAPI } from '../../api'

export default {
  name: 'RepaymentManagement',
  components: {
    Download
  },
  setup() {
    const tableLoading = ref(false)
    const createLoading = ref(false)
    const uploadLoading = ref(false)
    const customerLoading = ref(false)
    const disbursementLoading = ref(false)
    const exportLoading = ref(false)
    
    const createDialogVisible = ref(false)
    const screenshotDialogVisible = ref(false)
    const createFormRef = ref()
    const uploadRef = ref()
    
    const tableData = ref([])
    const customerOptions = ref([])
    const disbursementOptions = ref([])
    const screenshotUrl = ref('')
    
    const pagination = reactive({
      page: 1,
      limit: 20,
      total: 0
    })
    
    const searchForm = reactive({
      customer_id: '',
      repayment_method: ''
    })
    
    const createForm = reactive({
      disbursement_record_id: '',
      repayment_method: '',
      repayment_amount: 0,
      repayment_type: '',
      overdue_days: 0,
      overdue_fee: 0,
      payment_screenshot: '',
      remark: ''
    })
    
    const createRules = {
      disbursement_record_id: [{ required: true, message: '请选择放款记录', trigger: 'change' }],
      repayment_method: [{ required: true, message: '请选择还款方式', trigger: 'change' }],
      repayment_amount: [{ required: true, message: '请输入还款金额', trigger: 'blur' }],
      repayment_type: [{ required: true, message: '请选择还款类型', trigger: 'change' }]
    }
    
    // 计算属性
    const searchParams = computed(() => {
      return {
        page: pagination.page,
        limit: pagination.limit,
        ...searchForm
      }
    })
    
    // 获取还款列表
    const getTableData = async () => {
      try {
        tableLoading.value = true
        const data = await financeAPI.repayment.getList(searchParams.value)
        
        tableData.value = data.items
        pagination.total = data.pagination.total
        
      } catch (error) {
        console.error('获取还款列表失败:', error)
      } finally {
        tableLoading.value = false
      }
    }
    
    // 搜索客户
    const searchCustomers = async (query) => {
      if (!query) return
      
      try {
        customerLoading.value = true
        const data = await customerAPI.searchCustomers({ keyword: query, limit: 10 })
        customerOptions.value = data
      } catch (error) {
        console.error('搜索客户失败:', error)
      } finally {
        customerLoading.value = false
      }
    }
    
    // 搜索放款记录
    const searchDisbursementRecords = async (query) => {
      if (!query) return
      
      try {
        disbursementLoading.value = true
        const data = await financeAPI.disbursement.getList({ 
          keyword: query, 
          limit: 10,
          status: 'active'
        })
        disbursementOptions.value = data.items
      } catch (error) {
        console.error('搜索放款记录失败:', error)
      } finally {
        disbursementLoading.value = false
      }
    }
    
    // 处理放款记录选择
    const handleDisbursementChange = () => {
      // 可以在这里设置一些默认值
    }
    
    // 处理文件选择
    const handleFileChange = async (file) => {
      try {
        uploadLoading.value = true
        const data = await financeAPI.uploadScreenshot(file.raw)
        createForm.payment_screenshot = data.url
        ElMessage.success('图片上传成功')
      } catch (error) {
        console.error('图片上传失败:', error)
      } finally {
        uploadLoading.value = false
      }
    }
    
    // 显示创建对话框
    const showCreateDialog = () => {
      createDialogVisible.value = true
    }
    
    // 创建还款记录
    const handleCreate = async () => {
      try {
        await createFormRef.value.validate()
        createLoading.value = true
        
        await financeAPI.repayment.create(createForm)
        
        ElMessage.success('还款记录创建成功')
        createDialogVisible.value = false
        getTableData()
        
      } catch (error) {
        console.error('创建还款记录失败:', error)
      } finally {
        createLoading.value = false
      }
    }
    
    // 重置创建表单
    const resetCreateForm = () => {
      Object.assign(createForm, {
        disbursement_record_id: '',
        repayment_method: '',
        repayment_amount: 0,
        repayment_type: '',
        overdue_days: 0,
        overdue_fee: 0,
        payment_screenshot: '',
        remark: ''
      })
      disbursementOptions.value = []
    }
    
    // 搜索
    const handleSearch = () => {
      pagination.page = 1
      getTableData()
    }
    
    // 重置搜索
    const resetSearch = () => {
      Object.assign(searchForm, {
        customer_id: '',
        repayment_method: ''
      })
      pagination.page = 1
      getTableData()
    }
    
    // 分页处理
    const handleSizeChange = (size) => {
      pagination.limit = size
      pagination.page = 1
      getTableData()
    }
    
    const handleCurrentChange = (page) => {
      pagination.page = page
      getTableData()
    }
    
    // 查看详情
    const viewDetail = () => {
      ElMessage.info('详情功能开发中')
    }
    
    // 查看截图
    const viewScreenshot = (row) => {
      screenshotUrl.value = row.payment_screenshot
      screenshotDialogVisible.value = true
    }
    
    // 工具函数
    const formatMoney = (amount) => {
      return (amount || 0).toLocaleString()
    }
    
    const formatDateTime = (datetime) => {
      if (!datetime) return ''
      return new Date(datetime).toLocaleString()
    }
    
    const getRepaymentMethodText = (method) => {
      const methods = {
        wechat: '微信',
        alipay: '支付宝',
        bank_card: '银行卡',
        cash: '现金',
        other: '其他'
      }
      return methods[method] || method
    }
    
    const getRepaymentTypeText = (type) => {
      const types = {
        normal: '正常还款',
        partial: '部分还款',
        negotiated: '协商还款',
        overdue_fee: '逾期费',
        early_settlement: '提前结清'
      }
      return types[type] || type
    }
    
    const getStatusText = (status) => {
      const statuses = {
        pending: '待确认',
        confirmed: '已确认',
        cancelled: '已取消'
      }
      return statuses[status] || status
    }
    
    const getStatusTagType = (status) => {
      const types = {
        pending: 'warning',
        confirmed: 'success',
        cancelled: 'danger'
      }
      return types[status] || ''
    }
    
    // 导出数据
    const handleExport = async () => {
      try {
        exportLoading.value = true
        
        // 构建查询参数
        const params = {
          ...searchForm
        }
        
        // 调用导出API
        const response = await fetch('/api/export/repayments?' + new URLSearchParams(params), {
          method: 'GET',
          headers: {
            'Authorization': 'Bearer ' + localStorage.getItem('token')
          }
        })
        
        if (!response.ok) {
          throw new Error('导出失败')
        }
        
        // 创建下载链接
        const blob = await response.blob()
        const url = window.URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `还款记录_${new Date().toISOString().split('T')[0]}.csv`
        document.body.appendChild(a)
        a.click()
        window.URL.revokeObjectURL(url)
        document.body.removeChild(a)
        
        ElMessage.success('导出成功')
      } catch (error) {
        console.error('Export error:', error)
        ElMessage.error('导出失败')
      } finally {
        exportLoading.value = false
      }
    }
    
    onMounted(() => {
      getTableData()
      // 允许从放款列表预填 disbursement_record_id
      const params = new URLSearchParams(window.location.search)
      const prefillId = params.get('disbursement_record_id')
      if (prefillId) {
        createDialogVisible.value = true
        createForm.disbursement_record_id = Number(prefillId)
      }
    })
    
    return {
      // 响应式数据
      tableLoading,
      createLoading,
      uploadLoading,
      customerLoading,
      disbursementLoading,
      exportLoading,
      createDialogVisible,
      screenshotDialogVisible,
      createFormRef,
      uploadRef,
      tableData,
      customerOptions,
      disbursementOptions,
      screenshotUrl,
      pagination,
      searchForm,
      createForm,
      createRules,
      
      // 方法
      getTableData,
      searchCustomers,
      searchDisbursementRecords,
      handleDisbursementChange,
      handleFileChange,
      showCreateDialog,
      handleCreate,
      resetCreateForm,
      handleSearch,
      resetSearch,
      handleSizeChange,
      handleCurrentChange,
      viewDetail,
      viewScreenshot,
      handleExport,
      formatMoney,
      formatDateTime,
      getRepaymentMethodText,
      getRepaymentTypeText,
      getStatusText,
      getStatusTagType
    }
  }
}
</script>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-area {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.upload-preview {
  margin-top: 10px;
}

.el-pagination {
  margin-top: 20px;
  text-align: right;
}
</style>
