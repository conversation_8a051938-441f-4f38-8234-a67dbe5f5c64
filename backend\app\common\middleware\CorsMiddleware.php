<?php
declare (strict_types = 1);

namespace app\common\middleware;

use think\Response;

class CorsMiddleware
{
    /**
     * 处理请求
     *
     * @param \think\Request $request
     * @param \Closure       $next
     * @return Response
     */
    public function handle($request, \Closure $next)
    {
        // 处理预检请求
        if ($request->method() == 'OPTIONS') {
            $response = response();
        } else {
            $response = $next($request);
        }

        // 设置CORS头
        $response->header([
            'Access-Control-Allow-Origin' => '*',
            'Access-Control-Allow-Methods' => 'GET, POST, PUT, DELETE, OPTIONS',
            'Access-Control-Allow-Headers' => 'Content-Type, Authorization, X-Requested-With',
            'Access-Control-Max-Age' => '86400',
        ]);

        return $response;
    }
}
